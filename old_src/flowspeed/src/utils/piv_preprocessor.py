import cv2
import numpy as np
from pathlib import Path
from .logging_utils import setup_logger

logger = setup_logger("piv_preprocessor")

def extract_frames_from_video(video_path, frame_interval=1, logger=logger):
    """
    从视频中提取两帧用于PIV分析
    
    Args:
        video_path: 视频文件路径
        frame_interval: 两帧之间的间隔帧数 (第一帧为0，第二帧为frame_interval)
        logger: 日志器
        
    Returns:
        tuple: (帧1灰度图, 帧2灰度图, fps) 或者在失败时返回 (None, None, 0)
    """
    # 打开视频
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        logger.error(f"无法打开视频: {video_path}")
        return None, None, 0
    
    try:
        # 获取视频帧率
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            fps = 25.0  # 默认帧率
            logger.warning(f"无法获取视频帧率，使用默认值: {fps} fps")
        else:
            logger.info(f"视频帧率: {fps:.2f} 帧/秒")
        
        # 读取第一帧 (frame 0)
        ret, frame1 = cap.read()
        if not ret:
            logger.error("无法读取第一帧")
            return None, None, 0
            
        # 跳过中间帧到目标帧 (frame_interval)
        # 如果 frame_interval 是 1，表示取第0帧和第1帧，不需要跳过
        target_frame_index = frame_interval
        current_frame_index = 0 # 第一帧已经是第0帧
        
        # 定位到目标帧
        # set(cv2.CAP_PROP_POS_FRAMES, target_frame_index) 可能不精确，使用循环读取更可靠
        while current_frame_index < target_frame_index:
            ret, _ = cap.read()
            if not ret:
                logger.error(f"无法读取到第 {target_frame_index} 帧 (总帧数可能不足)")
                return None, None, 0
            current_frame_index += 1
        
        # 读取第二帧 (frame_interval)
        ret, frame2 = cap.read()
        if not ret:
            logger.error(f"无法读取目标第二帧 (帧索引 {target_frame_index})")
            return None, None, 0
            
        # 转换为灰度图
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        
        return gray1, gray2, fps
        
    except Exception as e:
        logger.error(f"提取帧时发生错误: {e}")
        return None, None, 0
    finally:
        cap.release()

def apply_roi_mask(frames, roi_points, image_dir=None, logger=logger):
    """
    应用ROI掩码到帧
    
    Args:
        frames: 包含两个灰度图像的元组
        roi_points: ROI多边形的点坐标列表
        image_dir: 保存掩码图像的目录 (可选)
        logger: 日志器
        
    Returns:
        tuple: 应用掩码后的两帧
    """
    if not roi_points:
        logger.info("未提供ROI掩码点")
        return frames
    
    gray1, gray2 = frames
    
    logger.info(f"应用ROI掩码，共{len(roi_points)}个点")
    roi_points_array = np.array(roi_points, np.int32)
    roi_mask = np.zeros(gray1.shape, dtype=np.uint8)
    # 确保数组是连续的，以兼容OpenCV的Mat类型
    roi_points_array = np.ascontiguousarray(roi_points_array)
    cv2.fillPoly(roi_mask, [roi_points_array], 255)  # type: ignore
    
    # 保存掩码图像以检查 (如果提供了目录)
    if image_dir:
        image_dir = Path(image_dir)
        mask_path = image_dir / "roi_mask.png"
        cv2.imwrite(str(mask_path), roi_mask)
        logger.info(f"保存ROI掩码图像到: {mask_path}")
    
    # 应用掩码
    masked_gray1 = cv2.bitwise_and(gray1, gray1, mask=roi_mask)
    masked_gray2 = cv2.bitwise_and(gray2, gray2, mask=roi_mask)
    
    return masked_gray1, masked_gray2

def save_processed_frames(frames, output_dir, logger=logger):
    """
    保存处理后的帧到磁盘
    
    Args:
        frames: 包含两个处理后图像的元组
        output_dir: 输出目录
        logger: 日志器
        
    Returns:
        tuple: 保存的图像文件路径
    """
    gray1, gray2 = frames
    
    # 确保输出目录存在
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存处理后的图像
    frame_a_path = output_dir / "frame_a.bmp"
    frame_b_path = output_dir / "frame_b.bmp"
    
    cv2.imwrite(str(frame_a_path), gray1)
    cv2.imwrite(str(frame_b_path), gray2)
    
    logger.info(f"已保存图像对: {frame_a_path}, {frame_b_path}")
    
    return str(frame_a_path), str(frame_b_path)

def prepare_frames_for_piv(video_path, config, image_dir=None, logger=logger):
    """
    准备用于PIV分析的图像帧
    
    Args:
        video_path: 视频文件路径
        config: 配置字典
        image_dir: 保存中间图像的目录 (可选)
        logger: 日志器
        
    Returns:
        tuple: (帧A路径, 帧B路径, 帧率) 或者在失败时返回 (None, None, 0)
    """
    # 提取配置参数
    common_params = config.get("common_params", {})
    roi_points = common_params.get("roi_points", None)
    # 从配置中获取 dt (时间间隔，秒)
    dt = common_params.get("dt", None)
    if dt is None:
        logger.error("配置中缺少 'dt' 参数 (common_params.dt)")
        return None, None, 0
    if dt <= 0:
        logger.error(f"'dt' 参数必须为正数，当前值为: {dt}")
        return None, None, 0
    
    # 设置图像目录
    if image_dir is None:
        image_dir = Path("data/image")
    else:
        image_dir = Path(image_dir)
    
    image_dir.mkdir(parents=True, exist_ok=True)
    
    # --- 计算 frame_interval ---
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        logger.error(f"无法打开视频以获取帧率: {video_path}")
        return None, None, 0
    fps = cap.get(cv2.CAP_PROP_FPS)
    cap.release() # 释放资源
    
    if fps <= 0:
        fps = 25.0  # 使用默认帧率
        logger.warning(f"无法获取视频帧率，使用默认值: {fps} fps 计算帧间隔")
    else:
        logger.info(f"视频帧率: {fps:.2f} fps")
    
    # 根据 dt 和 fps 计算帧间隔
    # frame_interval = 1 表示取 第0帧 和 第1帧
    # frame_interval = N 表示取 第0帧 和 第N帧
    frame_interval = max(1, int(round(dt * fps)))
    logger.info(f"根据 dt={dt}s 和 fps={fps:.2f}, 计算得到帧间隔 frame_interval = {frame_interval}")
    # --- 结束计算 frame_interval ---
    
    # 从视频提取帧 (使用计算出的 frame_interval)
    gray1, gray2, actual_fps = extract_frames_from_video(video_path, frame_interval, logger)
    if gray1 is None or gray2 is None:
        return None, None, 0
    # 使用从 extract_frames_from_video 返回的实际帧率 (可能为默认值)
    fps_to_return = actual_fps
    
    # 应用ROI掩码 (如果有)
    if roi_points:
        gray1, gray2 = apply_roi_mask((gray1, gray2), roi_points, image_dir, logger)
    
    # 保存处理后的帧
    frame_a_path, frame_b_path = save_processed_frames((gray1, gray2), image_dir, logger)
    
    return frame_a_path, frame_b_path, fps_to_return 