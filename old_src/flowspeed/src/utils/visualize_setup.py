import numpy as np
import cv2
from pathlib import Path
import copy
import sys

# Helper function to draw grid lines
def draw_grid(image, grid_size=50, color=(200, 200, 200), thickness=1):
    h, w = image.shape[:2]
    for x in range(0, w, grid_size):
        cv2.line(image, (x, 0), (x, h), color=color, thickness=thickness)
    for y in range(0, h, grid_size):
        cv2.line(image, (0, y), (w, y), color=color, thickness=thickness)
    return image

# Helper function to draw lines and ranges
def draw_lines_and_ranges(image, lines, lines_range, line_color, range_color, thickness=2):
    h, w = image.shape[:2]
    if not lines or not lines_range or len(lines) != len(lines_range):
        print("警告: lines 或 lines_range 数据无效或不匹配，无法绘制。", file=sys.stderr)
        return image

    for i, (line_y, line_r) in enumerate(zip(lines, lines_range)):
        # Clamp coordinates to image boundaries
        line_y = max(0, min(line_y, h - 1))
        start_x = max(0, min(line_r[0], w - 1))
        end_x = max(0, min(line_r[1], w - 1))

        # Ensure start <= end after clamping
        if start_x > end_x:
            print(f"警告: 调整后的 line_range {i} 无效 ({start_x}, {end_x})，跳过绘制范围。", file=sys.stderr)
            start_x = end_x # Or skip drawing range? Let's just make it a point for now

        # Draw the full horizontal line
        cv2.line(image, (0, line_y), (w - 1, line_y), color=line_color, thickness=thickness)
        # Draw the specific range on top/thicker
        cv2.line(image, (start_x, line_y), (end_x, line_y), color=range_color, thickness=thickness + 1)
        # Add label
        cv2.putText(image, f"L{i}", (start_x + 5, line_y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, range_color, 1)
    return image

def visualize_cropping_and_lines(
    frame: np.ndarray,
    original_config: dict,
    adjusted_config: dict, # The fully adjusted one
    output_dir: Path,
    base_filename: str,
    is_stiv: bool
) -> None:
    """
    Visualize pre-cropping, final cropping, and lines/lines_range settings.

    Args:
        frame: Original video frame.
        original_config: Configuration dictionary loaded from the original JSON file.
        adjusted_config: Configuration dictionary after processing by adjust_roi_coordinates.
        output_dir: Directory to save visualization images.
        base_filename: Base part for the output image filenames (usually video filename).
        is_stiv: Boolean indicating if STIV-specific final crop visualization should be generated.
    """
    vis_frame = frame.copy()
    h_orig, w_orig = vis_frame.shape[:2]

    # --- 1. 可视化 pre_roi 和在其内部调整后的内容 ---
    try:
        pre_roi = original_config.get('preprocessing', {}).get('pre_roi')
        if not (pre_roi and isinstance(pre_roi, list) and len(pre_roi) == 2 and
                isinstance(pre_roi[0], list) and len(pre_roi[0]) == 2 and
                isinstance(pre_roi[1], list) and len(pre_roi[1]) == 2):
            print("警告: original_config 中 pre_roi 缺失或格式无效，无法进行预裁剪可视化。", file=sys.stderr)
            return # Cannot proceed without pre_roi for this visualization

        pre_y1, pre_x1 = pre_roi[0]
        pre_y2, pre_x2 = pre_roi[1]

        # Clamp pre_roi to original frame dimensions
        pre_y1, pre_x1 = max(0, pre_y1), max(0, pre_x1)
        pre_y2, pre_x2 = min(h_orig, pre_y2), min(w_orig, pre_x2)

        if pre_y1 >= pre_y2 or pre_x1 >= pre_x2:
             print("警告: pre_roi 坐标无效或超出原始图像范围，无法进行预裁剪。", file=sys.stderr)
             return

        # Crop frame based on pre_roi
        frame_after_pre_crop = vis_frame[pre_y1:pre_y2, pre_x1:pre_x2].copy()
        h_pre, w_pre = frame_after_pre_crop.shape[:2]

        # Draw grid on the pre-cropped frame
        frame_after_pre_crop = draw_grid(frame_after_pre_crop)

        # Get the adjusted ROI (relative to pre_crop)
        adj_roi = adjusted_config.get('preprocessing', {}).get('roi')
        if not (adj_roi and isinstance(adj_roi, list) and len(adj_roi) == 2 and
                isinstance(adj_roi[0], list) and len(adj_roi[0]) == 2 and
                isinstance(adj_roi[1], list) and len(adj_roi[1]) == 2):
            print("警告: adjusted_config 中 roi 缺失或格式无效，无法绘制调整后 ROI。", file=sys.stderr)
        else:
            adj_y1, adj_x1 = adj_roi[0]
            adj_y2, adj_x2 = adj_roi[1]
            # Clamp adjusted ROI to pre-cropped frame dimensions
            adj_y1, adj_x1 = max(0, adj_y1), max(0, adj_x1)
            adj_y2, adj_x2 = min(h_pre, adj_y2), min(w_pre, adj_x2)
            if adj_y1 < adj_y2 and adj_x1 < adj_x2:
                 # Draw adjusted ROI rectangle (e.g., Blue)
                 cv2.rectangle(frame_after_pre_crop, (adj_x1, adj_y1), (adj_x2, adj_y2), (255, 0, 0), 2)
                 cv2.putText(frame_after_pre_crop, "Adjusted ROI", (adj_x1, adj_y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            else:
                 print("警告: 调整后的 ROI 坐标无效或超出预裁剪图像范围。", file=sys.stderr)


        # Recalculate lines/ranges relative to pre_crop (Step 1 adjustment)
        lines_orig = original_config.get('lines')
        lines_range_orig = original_config.get('stiv', {}).get('lines_range')

        lines_after_pre = []
        if lines_orig and isinstance(lines_orig, list) and all(isinstance(y, int) for y in lines_orig):
            lines_after_pre = [max(0, y - pre_y1) for y in lines_orig]

        lines_range_after_pre = []
        if (lines_range_orig and isinstance(lines_range_orig, list) and
            all(isinstance(r, list) and len(r) == 2 and isinstance(r[0], int) and isinstance(r[1], int) for r in lines_range_orig)):
            for r in lines_range_orig:
                orig_x_start, orig_x_end = r
                x_start_after_pre = max(0, orig_x_start - pre_x1)
                x_end_after_pre = max(x_start_after_pre, orig_x_end - pre_x1) # Ensure start <= end
                lines_range_after_pre.append([x_start_after_pre, x_end_after_pre])

        # Draw lines/ranges relative to pre_crop (e.g., Green lines, Yellow ranges)
        frame_after_pre_crop = draw_lines_and_ranges(
            frame_after_pre_crop, lines_after_pre, lines_range_after_pre, (0, 255, 0), (0, 255, 255), thickness=1
        )

        # Save the first visualization
        output_path_pre = output_dir / f"{base_filename}_visualization_pre_crop.png"
        cv2.imwrite(str(output_path_pre), frame_after_pre_crop)
        print(f"预裁剪可视化图像已保存至: {output_path_pre}")

    except Exception as e:
        print(f"生成预裁剪可视化图像时出错: {e}", file=sys.stderr)
        # Decide if we should stop or try the next step
        return # Stop if pre-crop visualization fails


    # --- 2. 可视化最终裁剪和在其内部调整后的内容 (仅 STIV) ---
    if is_stiv:
        try:
            # We need frame_after_pre_crop and adj_roi from the previous step
            if 'frame_after_pre_crop' not in locals():
                 print("错误: 无法获取预裁剪图像，无法进行最终裁剪可视化。", file=sys.stderr)
                 return
            if not adj_roi: # Check if adj_roi was valid
                 print("警告: 调整后的 ROI 无效，无法进行最终裁剪可视化。", file=sys.stderr)
                 return

            adj_y1, adj_x1 = adj_roi[0]
            adj_y2, adj_x2 = adj_roi[1]

            # Clamp again just to be safe, relative to frame_after_pre_crop dims
            h_pre, w_pre = frame_after_pre_crop.shape[:2]
            adj_y1, adj_x1 = max(0, adj_y1), max(0, adj_x1)
            adj_y2, adj_x2 = min(h_pre, adj_y2), min(w_pre, adj_x2)

            if adj_y1 >= adj_y2 or adj_x1 >= adj_x2:
                 print("警告: 调整后的 ROI 坐标无效或超出预裁剪图像范围，无法进行最终裁剪。", file=sys.stderr)
                 return

            # Crop based on adjusted ROI
            frame_final_crop = frame_after_pre_crop[adj_y1:adj_y2, adj_x1:adj_x2].copy()

            # Draw grid on the final cropped frame
            frame_final_crop = draw_grid(frame_final_crop)

            # Get final lines and ranges (already adjusted relative to final crop)
            final_lines = adjusted_config.get('lines')
            final_lines_range = adjusted_config.get('stiv', {}).get('lines_range')

            # Draw final lines/ranges (e.g., Red lines, Cyan ranges)
            frame_final_crop = draw_lines_and_ranges(
                frame_final_crop, final_lines, final_lines_range, (0, 0, 255), (255, 255, 0), thickness=1
            )

            # Save the second visualization
            output_path_final = output_dir / f"{base_filename}_visualization_final_crop.png"
            cv2.imwrite(str(output_path_final), frame_final_crop)
            print(f"最终裁剪可视化图像已保存至: {output_path_final}")

        except Exception as e:
            print(f"生成最终裁剪可视化图像时出错: {e}", file=sys.stderr)

# Example usage (for testing purposes, can be removed)
# if __name__ == '__main__':
#     # Create dummy data
#     dummy_frame = np.zeros((1200, 2000, 3), dtype=np.uint8) + 50 # Gray background
#     dummy_orig_config = {
#         "preprocessing": {
#             "pre_roi": [[400, 600], [1000, 1900]],
#             "roi": [[660, 800], [820, 1800]]
#         },
#         "lines": [700, 750, 800],
#         "stiv": {
#              "lines_range": [[900, 1700], [900, 1700], [900, 1700]]
#         }
#     }
#     # Simulate adjusted config (assuming adjust_roi_coordinates works)
#     dummy_adjusted_config = {
#          "preprocessing": {
#              "roi": [[260, 200], [420, 1200]] # Rel to pre_roi
#          },
#          "lines": [40, 90, 140], # Rel to final crop
#          "stiv": {
#               "lines_range": [[100, 900], [100, 900], [100, 900]] # Rel to final crop
#          }
#     }
#     output_directory = Path("./data/output_test")
#     output_directory.mkdir(parents=True, exist_ok=True)
#     visualize_cropping_and_lines(
#         dummy_frame,
#         dummy_orig_config,
#         dummy_adjusted_config,
#         output_directory,
#         "dummy_video",
#         is_stiv=True
#     )
#     visualize_cropping_and_lines(
#         dummy_frame,
#         dummy_orig_config,
#         dummy_adjusted_config,
#         output_directory,
#         "dummy_video_otv",
#         is_stiv=False
#     ) 