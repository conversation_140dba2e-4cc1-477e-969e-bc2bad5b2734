Project Overview

  This is a drone video stream real-time object detection system built
  with FastAPI, designed for water environment monitoring and security
  inspection. The system supports 20+ concurrent drone streams and
  provides AI-powered detection for 9 types of targets.

  Quick Commands

  Development

  # Install dependencies
  pip install -r requirements.txt

  # Run in development mode
  python main.py --config config_fake.yaml

  # Or specify custom config
  python main.py --config config.yaml --debug

  # Run with uvicorn directly
  uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  Testing

  # Run all tests
  python tests/run_tests.py

  # Run specific module tests
  python tests/run_tests.py --module event_reporter
  python tests/run_tests.py --module image_processor

  # Check test environment
  python tests/check_env.py

  # Run pytest directly
  pytest tests/ -v

  Configuration

  # Copy and edit config
  cp config_fake.yaml config.yaml
  # Edit config.yaml with your settings

  # Environment variables
  export CONFIG_FILE=config.yaml
  export RUN_MODE=dev

  Core Architecture

  Layered Architecture

  ┌─────────────────┐
  │   API Routes    │ (app/api/)
  ├─────────────────┤
  │  Service Layer  │ (app/services/)
  ├─────────────────┤
  │ Processor Layer │ (app/processor/)
  ├─────────────────┤
  │   Model Layer   │ (app/models/)
  ├─────────────────┤
  │   Data Layer    │ (app/database/)
  ├─────────────────┤
  │   Utilities     │ (app/utils/)
  └─────────────────┘

  Key Components

  Detection Pipeline:
  - app/api/detect.py - Main API endpoints for detection
  - app/processor/image_processor.py - Image preprocessing
  - app/processor/video_processor.py - Video file processing
  - app/processor/stream_processor.py - Real-time stream processing
  - app/models/yolo_model.py - YOLO model wrapper
  - app/models/multimodal_model.py - Multi-modal AI integration

  Monitoring System:
  - app/services/monitor_service.py - Central monitoring service
  - app/video/manager.py - Video stream management
  - app/database/drone_info.py - Drone information management

  Data Management:
  - app/utils/nfs_manager.py - NFS file system integration
  - app/utils/event_reporter.py - Event reporting to external systems
  - app/utils/config.py - Configuration management

  API Endpoints

  Object Detection

  - POST /api/image - Image detection
  - POST /api/video - Video file detection
  - POST /api/stream - Stream detection start
  - POST /api/stream/stop - Stop stream detection
  - POST /api/reload_model - Hot reload AI models

  WebSocket

  - WS /api/ws/stream/{client_id} - Real-time detection updates

  Web Interface

  - GET / - Main dashboard
  - GET /api/ - Image detection interface
  - GET /api/video - Video detection interface
  - GET /api/stream - Stream detection interface
  - GET /api/webrtc - WebRTC test interface

  Configuration Files

  Main Config: config.yaml

  Key sections to configure:
  - mode.run_mode - "dev" or "prod"
  - database.* - MySQL connection settings
  - models.yolo.model_path - YOLO model file path
  - storage.nfs.* - NFS server settings
  - api.security.api_key - API authentication key   

  Drone Configuration

  - drone_list - List of drone IDs to monitor
  - detection.targets - 9 predefined target types with keywords
  - video_stream.* - Stream processing parameters

  Model Information

  YOLO Models:
  - models/pond_aeration_yolo11n.pt - Custom trained model for pond
  aeration detection
  - models/yolo11n.pt - Standard YOLO11n model
  - models/yolov11x.pt - Larger YOLO11x model for better accuracy

  Detection Targets:
  - 船只 (boat) - Water safety
  - 人员 (person) - Personnel safety
  - 疑似异常藻类 (algae) - Water quality
  - 曝气 (water_aeration) - Water treatment
  - 施工 (construction) - Environmental safety
  - 管路异常 (pipe) - Infrastructure

  Development Workflow

  Adding New Detection Targets

  1. Add target configuration in config.yaml under detection.targets
  2. Update the keyword mapping in your YOLO model training
  3. Test with sample images/videos

  Model Updates

  1. Replace model file in models/ directory
  2. Update config.yaml with new model path
  3. Use /api/reload_model endpoint for hot reload
  4. Test detection accuracy

  Testing New Features

  1. Add tests in appropriate tests/ subdirectory
  2. Run python tests/run_tests.py --module <module_name>
  3. Use python tests/check_env.py to verify environment

  Common Issues & Solutions

  NFS Mount Issues:
  - Ensure NFS server is accessible
  - Check mount points in config.yaml
  - Verify network connectivity between client/server

  Model Loading Errors:
  - Check model file paths in config
  - Ensure sufficient GPU memory
  - Verify CUDA installation for GPU acceleration

  Stream Connection Issues:
  - Check RTMP/WebRTC URLs in config
  - Verify network connectivity to streaming servers
  - Check firewall settings for required ports

  Database Connection:
  - Ensure MySQL is running and accessible
  - Verify credentials in config.yaml
  - Check database schema compatibility

  Environment Requirements

  - Python: 3.10+ (specified in pyproject.toml)
  - CUDA: Optional but recommended for GPU acceleration
  - MySQL: 5.7+ for data persistence
  - NFS Server: Optional for distributed file storage
  - Ollama: For multi-modal AI capabilities (localhost:11434)

  系统启动流程

  项目启动包含6个核心步骤：

  1. **环境配置阶段**
     - `app.utils.config.config` - 加载配置文件
     - `app.utils.logger.setup_logger` - 设置日志级别
     - 设置OpenCV环境变量 `OPENCV_FFMPEG_READ_ATTEMPTS`

  2. **应用初始化阶段**
     - `FastAPI(lifespan=lifespan)` - 创建FastAPI应用实例
     - 配置生命周期管理（启动/关闭钩子）

  3. **启动时初始化**
     - `app.utils.nfs_manager.nfs_manager.setup()` - NFS挂载初始化
     - `app.services.monitor_service.MonitorService()` - 创建监控服务
     - `asyncio.create_task()` - 创建后台监控任务

  4. **应用配置阶段**
     - `CORSMiddleware` - 配置跨域访问
     - `StaticFiles` - 挂载静态文件
     - `Jinja2Templates` - 配置Web界面模板

  5. **服务启动阶段**
     - `uvicorn.Server` - 启动Web服务器
     - 监听配置文件中指定的端口

  6. **关闭清理阶段**
     - 停止监控服务
     - 清理NFS挂载
     - 关闭数据库连接

  无人机视频流识别完整流程

  ### 数据流架构
  ```
  WebSocket → DroneInfoManager → VideoStreamManager → StreamProcessor → YOLOModel
      ↓           ↓                ↓                  ↓               ↓
    实时数据 → 无人机信息管理 → 视频流管理 → 流处理器 → AI检测
  ```

  ### 核心组件职责

  #### MonitorService (监控总控)
  - **类路径**: `app/services/monitor_service.py`
  - **职责**: 整个监控系统的核心调度器
  - **关键方法**:
    - `start()`: 启动完整监控服务链
    - `_init_drone_streams()`: 初始化所有无人机视频流
    - `setup_scheduler()`: 设置定时任务(重连、状态检查)

  #### StreamMonitor (流监控器)
  - **类路径**: `app/processor/stream_processor.py`
  - **职责**: 管理多个视频流的处理器
  - **关键方法**:
    - `_monitor_loop()`: 持续监控活跃无人机
    - `_manage_stream()`: 管理单个无人机视频流
    - `_on_detection()`: 检测结果处理回调

  #### StreamProcessor (流处理器)
  - **类路径**: `app/processor/stream_processor.py`
  - **职责**: 处理单个视频流的实时检测
  - **关键方法**:
    - `_process_stream()`: 主处理循环(读取帧→缓存→检测)
    - `_handle_detection_result()`: 处理检测结果
    - `get_frames_around_timestamp()`: 获取指定时间段的帧

  #### YOLOModel (AI检测引擎)
  - **类路径**: `app/models/yolo_model.py`
  - **职责**: 执行目标检测
  - **关键方法**:
    - `detect_multiple()`: 多目标检测
    - `detect()`: 单目标检测
    - `reload_model()`: 模型热更新

  ### 完整数据流步骤

  #### 阶段1: 初始化流程
  1. **WebSocket客户端启动** → 接收实时无人机数据
  2. **DroneInfoManager初始化** → 从MySQL加载无人机基础信息
  3. **VideoStreamManager创建** → 建立RTMP/WebRTC视频流连接
  4. **StreamProcessor启动** → 为每个活跃无人机创建独立处理线程

  #### 阶段2: 实时处理流程
  1. **WebSocket数据接收** → `DroneInfoManager.handle_websocket_data()`
  2. **活跃无人机检测** → 基于mode_code判断无人机状态
  3. **视频流连接** → `VideoStreamManager.get_stream()`创建视频流实例
  4. **帧读取** → `StreamProcessor._process_stream()`循环读取视频帧
  5. **AI检测** → `ImagePreprocessor.detect_targets()`调用YOLOModel
  6. **结果处理** → 保存视频片段 + 事件上报

  #### 阶段3: 结果输出流程
  1. **视频保存** → `file_manager.save_detection_video()`
  2. **事件上报** → `event_reporter.report_event()`
  3. **缓存管理** → `FrameBuffer`维护最近帧缓存

  ### 并发处理架构

  #### 多线程/多进程设计
  - **主协程**: MonitorService单例模式统一调度
  - **独立线程**: 每个视频流一个StreamProcessor独立线程
  - **异步检测**: AI检测使用asyncio任务，不阻塞主流程
  - **WebSocket**: 独立协程处理实时数据流

  #### 数据同步机制
  - **FrameBuffer**: 线程安全的帧缓存系统
  - **active_drones**: 共享状态字典维护全局无人机状态
  - **asyncio队列**: 处理检测任务的异步回调

  ### 配置驱动的灵活性

  #### 可配置参数
  - **检测频率**: `detection_interval`控制AI检测间隔
  - **帧率控制**: `frame_rate`控制视频处理速度
  - **缓存时长**: `buffer_duration`控制回溯视频长度
  - **失败阈值**: `read_fail_threshold`控制容错机制

  #### 扩展点
  - **新增检测目标**: 修改config.yaml中的detection.targets
  - **新增无人机**: 更新config.yaml中的drone_list
  - **切换AI模型**: 通过/reload_model API热更新YOLO模型

  File Structure Quick Reference

```
  CV/
  ├── main.py                 # Application entry point
  ├── config.yaml            # Main configuration file
  ├── app/                   # Main application package
  │   ├── api/               # REST API endpoints
  │   ├── processor/         # Image/video/stream processors
  │   ├── models/            # AI model wrappers
  │   ├── services/          # Business logic services
  │   ├── utils/             # Utility functions
  │   ├── video/             # Video management
  │   └── templates/         # Web interface templates
  ├── models/                # AI model files
  ├── tests/                 # Test suite
  ├── tools/                 # Development utilities
  └── app_data/              # Runtime data storage
```