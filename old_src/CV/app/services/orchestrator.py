import asyncio
import logging
from typing import Optional

from app.database.websocket_client import WebSocketClient
from app.processor.stream_processor import StreamMonitor
from app.video.manager import VideoStreamManager
from app.database.drone_info import DroneInfoManager
from app.utils.config import config
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

logger = logging.getLogger(__name__)


class Orchestrator:
    """
    轻量化服务编排器，主要用于启动/停止关键子系统
    职责：连接组件，并将健康状况/心跳监测委托给子系统。
    """

    _instance: Optional["Orchestrator"] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.drone_info_manager = DroneInfoManager()
        self.video_manager = VideoStreamManager(self.drone_info_manager)
        self.stream_monitor = StreamMonitor(self.video_manager)
        self.ws_client = WebSocketClient()
        self.scheduler = AsyncIOScheduler()
        self._running = False
        self._tasks = []

        # 委托消息处理到下游服务，如果需要
        # 保持轻量级处理程序，子系统处理自己的健康
        self._initialized = True
        logger.info("Orchestrator initialized")

    async def start(self):
        if self._running:
            return
        self._running = True

        try:
            # 开启WebSocket客户端在后台
            logger.info("Starting WebSocketClient...")
            ws_task = asyncio.create_task(self.ws_client.start())
            self._tasks.append(ws_task)

            await asyncio.sleep(2)
            logger.info("WebSocketClient started")

            # 可选设置stream_monitor到drone_info_manager
            self.drone_info_manager.set_stream_monitor(self.stream_monitor)

            # 启动调度器，只包含必要任务，由子系统处理健康
            self._setup_scheduler()

            # 启动流监控
            await self.stream_monitor.start()
            logger.info("StreamMonitor started")

        except Exception as e:
            logger.error(f"Orchestrator start failed: {e}")
            await self.stop()

    async def stop(self):
        if not self._running:
            return
        self._running = False

        try:
            # 停止流监控
            if self.stream_monitor:
                await self.stream_monitor.stop()
            # 停止websocket
            if self.ws_client:
                await self.ws_client.stop()
            # 停止调度器
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown(wait=False)
        finally:
            # 确保任务已取消
            for t in self._tasks:
                if not t.done():
                    t.cancel()
            self._tasks.clear()
            logger.info("Orchestrator stopped")

    def _setup_scheduler(self):
        try:
            # 最小化调度程序示例：定期状态日志。子系统管理自己的健康
            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("Scheduler started (lightweight)")

            # 轻量级周期任务示例
            self.scheduler.add_job(
                self._log_status,
                IntervalTrigger(seconds=config.scheduler.status_check_interval),
                id='status_log',
                replace_existing=True
            )
        except Exception as e:
            logger.error(f"Failed to setup scheduler: {e}")

    async def _log_status(self):
        try:
            logger.debug("Orchestrator heartbeat")
        except Exception:
            pass

