import asyncio
import logging
import time
from app.processor.stream_processor import StreamMonitor
from app.video.manager import VideoStreamManager
from app.database.drone_info import DroneInfoManager
from app.database.websocket_client import WebSocketClient
from app.utils.config import config
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_ERROR, EVENT_SCHEDULER_STARTED, EVENT_SCHEDULER_SHUTDOWN

logger = logging.getLogger(__name__)

class MonitorService:
    """监控服务，负责管理所有监控任务"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MonitorService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    """监控服务"""
    def __init__(self):
        """初始化监控服务"""
        # 避免重复初始化
        if self._initialized:
            return

        # 创建无人机信息管理器
        self.drone_info_manager = DroneInfoManager()
        # 预加载配置无人机基础信息，减少运行时数据库访问
        try:
            self.drone_info_manager.preload_from_config()
        except Exception:
            logger.warning("[预加载] 调用 preload_from_config 失败，将在运行时按需加载（可能增加DB交互）")

        # 创建视频流管理器
        self.video_manager = VideoStreamManager(self.drone_info_manager)

        # 创建流监控器
        self.stream_monitor = StreamMonitor(self.video_manager)

        self.ws_client = WebSocketClient()
        self.scheduler = AsyncIOScheduler()
        self._running = False
        self._tasks = []  # 存储所有异步任务

        # 任务状态监控
        self.last_reconnect_time = 0
        self.last_status_check_time = 0
        self.reconnect_failures = 0
        self.max_reconnect_failures = 5

        # 注册WebSocket消息处理器
        self.ws_client.register_message_handler('device_osd', self._forward_drone_data)

        self._initialized = True
        logger.info("MonitorService 初始化完成")

    async def _forward_drone_data(self, data: dict) -> None:
        """
        转发WebSocket数据到DroneInfoManager
        :param data: WebSocket消息
        """
        # 将WebSocket数据转发到DroneInfoManager处理
        await self.drone_info_manager.handle_websocket_data(data)

    async def log_active_drones(self) -> None:
        """记录活跃的无人机信息"""
        try:
            current_time = time.time()
            self.last_status_check_time = current_time

            # 使用DroneInfoManager获取基于视频流状态的活跃无人机
            active_drone_ids = self.drone_info_manager.get_active_drones()
            logger.info(f"当前活跃的无人机: {active_drone_ids}")

            for drone_code in active_drone_ids:
                drone_info = self.drone_info_manager.get_drone_info(drone_code)
                if drone_info:
                    logger.info(f"无人机 {drone_code} 信息: {drone_info}")

        except Exception as e:
            logger.error(f"记录活跃无人机信息失败: {str(e)}")
        finally:
            pass

    def setup_scheduler(self) -> None:
        """设置定时任务"""
        try:
            # 监听调度器事件（仅诊断）
            try:
                self.scheduler.add_listener(self._scheduler_listener, EVENT_JOB_ERROR | EVENT_SCHEDULER_STARTED | EVENT_SCHEDULER_SHUTDOWN)
            except Exception:
                pass

            # 清除现有任务
            self.scheduler.remove_all_jobs()

            # 添加定时重连任务 - 使用更短的间隔避免错过执行
            self.scheduler.add_job(
                self._reinit_drone_streams,
                IntervalTrigger(seconds=min(config.scheduler.reconnect_interval, 60)),  # 最大60秒间隔
                id='reconnect_task',
                name='重连任务',
                replace_existing=True,
                misfire_grace_time=30  # 允许30秒的错过执行时间
            )

            # 添加定时状态检查任务
            self.scheduler.add_job(
                self.log_active_drones,
                IntervalTrigger(seconds=min(config.scheduler.status_check_interval, 30)),  # 最大30秒间隔
                id='status_check_task',
                name='状态检查任务',
                replace_existing=True,
                misfire_grace_time=15  # 允许15秒的错过执行时间
            )

            # 添加WebSocket连接健康检查任务
            self.scheduler.add_job(
                self._check_websocket_health,
                IntervalTrigger(seconds=30),  # 每30秒检查一次
                id='websocket_health_check',
                name='WebSocket健康检查',
                replace_existing=True,
                misfire_grace_time=10
            )

            # 添加夜间模式切换任务
            self.scheduler.add_job(
                self._check_night_mode,
                CronTrigger(minute='*/5'),  # 每5分钟检查一次
                id='night_mode_check',
                name='夜间模式检查',
                replace_existing=True
            )

            # 添加NFS自检与重挂载任务
            self.scheduler.add_job(
                self._check_nfs_and_remount,
                IntervalTrigger(seconds=max(10, int(config.MONITOR_NFS_CHECK_INTERVAL))),
                id='nfs_check_task',
                name='NFS自检与重挂载',
                replace_existing=True,
                misfire_grace_time=15
            )

            # 添加关键统计日志任务
            self.scheduler.add_job(
                self._log_key_stats,
                IntervalTrigger(seconds=max(30, int(config.MONITOR_KEY_STATS_INTERVAL))),
                id='key_stats_task',
                name='关键统计日志',
                replace_existing=True,
                misfire_grace_time=30
            )

            # 启动调度器
            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("定时任务调度器已启动")
    
        except Exception as e:
            logger.error(f"设置定时任务失败: {str(e)}")


    async def _check_nfs_and_remount(self) -> None:
        """检查NFS挂载状态并尝试重挂载，失败不阻断主流程。"""
        try:
            from app.utils.nfs_manager import nfs_manager
            ok = nfs_manager.check_and_remount()
            if not ok:
                logger.warning("NFS自检重挂载失败，将继续使用本地临时目录作为降级存储")
        except Exception as e:
            logger.error(f"NFS自检任务异常: {e}")

    async def _log_key_stats(self) -> None:
        """输出关键统计信息，便于运维观测。"""
        try:
            from app.utils.nfs_manager import nfs_manager
            # 分段统计各步骤耗时，便于定位阻塞来源
            t0 = time.monotonic()
            nfs_status = nfs_manager.get_status()
            t1 = time.monotonic()
            streams_status = self.video_manager.get_all_streams_status()
            t2 = time.monotonic()

            monitor_stats = {}
            try:
                for s in self.video_manager.streams.values():
                    if hasattr(s, 'component_monitor') and s.component_monitor:
                        monitor_stats = s.component_monitor.get_monitor_stats()
                        break
            except Exception:
                pass
            t3 = time.monotonic()

        except Exception as e:
            logger.error(f"关键统计日志任务异常: {e}")


    async def _check_websocket_health(self) -> None:
        """检查WebSocket连接健康状态"""
        try:
            if not self.ws_client.ws or self.ws_client.ws.closed:
                logger.warning("WebSocket连接已断开，尝试重连")
                await self._force_websocket_reconnect()
            else:
                # 检查最后消息时间
                current_time = time.time()
                if current_time - self.ws_client.last_message_time > 120:  # 2分钟无消息
                    logger.warning("WebSocket连接长时间无消息，可能已断开")
                    await self._force_websocket_reconnect()
        except Exception as e:
            logger.error(f"WebSocket健康检查失败: {str(e)}")
        finally:
            pass

    async def _force_websocket_reconnect(self) -> None:
        """强制重连WebSocket"""
        try:
            logger.info("强制重连WebSocket连接")
            await self.ws_client.reconnect()
        except Exception as e:
            logger.error(f"强制重连WebSocket失败: {str(e)}")

    async def _check_night_mode(self) -> None:
        """检查并切换夜间模式 - 优化版本"""
        try:
            current_is_night_time = self.ws_client._is_night_mode()
            current_mode_enabled = self.ws_client.night_mode_enabled

            # 只在需要切换时才进行操作
            if current_is_night_time != current_mode_enabled:
                self.ws_client.night_mode_enabled = current_is_night_time
                if current_is_night_time:
                    logger.info("切换到夜间模式")
                else:
                    logger.info("切换到日间模式")
            else:
                # 状态一致，无需切换 - 记录调试信息
                logger.debug(f"夜间模式状态一致: {'夜间' if current_mode_enabled else '日间'}模式")
        except Exception as e:
            logger.error(f"夜间模式检查失败: {str(e)}")
        finally:
            pass

    async def _reinit_drone_streams(self) -> None:
        """重新初始化无人机视频流"""
        try:
            current_time = time.time()

            # 检查重连频率，避免过于频繁
            if current_time - self.last_reconnect_time < 30:  # 至少间隔30秒
                logger.debug("重连间隔过短，跳过本次重连")
                return

            self.last_reconnect_time = current_time
            logger.info("开始重新初始化无人机视频流...")

            # 检查WebSocket连接状态
            if not self.ws_client.ws or self.ws_client.ws.closed:
                logger.warning("WebSocket连接断开，先尝试重连")
                if not await self.ws_client.reconnect():
                    logger.error("WebSocket重连失败，跳过视频流重连")
                    self.reconnect_failures += 1
                    return

            # 重置失败计数
            self.reconnect_failures = 0

            await self._init_drone_streams()
            logger.info("重新初始化无人机视频流完成")
        except Exception as e:
            logger.error(f"重新初始化无人机视频流失败: {str(e)}")
            self.reconnect_failures += 1
        finally:
            pass

    async def start(self) -> None:
        """启动监控服务"""
        if self._running:
            return

        self._running = True
        logger.info("启动监控服务")

        try:
            # 在后台启动WebSocket客户端
            logger.info("开始启动WebSocket客户端...")
            ws_task = asyncio.create_task(self.ws_client.start())
            self._tasks.append(ws_task)

            # 等待WebSocket客户端连接成功并初始化消息处理器
            await asyncio.sleep(3)  # 增加等待时间，确保WebSocket连接和消息处理器初始化完成
            logger.info("WebSocket客户端启动完成")

            # 设置stream_monitor到drone_info_manager（在调用log_active_drones之前）
            self.drone_info_manager.set_stream_monitor(self.stream_monitor)
            logger.info("成功设置stream_monitor到drone_info_manager")

            # 在WebSocket客户端初始化后检查活跃无人机
            logger.info("开始初始检查活跃无人机...")
            await self.log_active_drones()
            logger.info("完成初始活跃无人机检查")

            # 设置并启动定时任务
            self.setup_scheduler()
            logger.info("成功设置定时任务")

            # 初始化无人机视频流
            await self._init_drone_streams()
            logger.info("完成无人机视频流初始化")

            # 在后台启动流监控器
            stream_task = asyncio.create_task(self.stream_monitor.start())
            self._tasks.append(stream_task)
            logger.info("成功启动流监控器")

            # 启动事件循环看门狗（仅诊断，不改变业务行为）
            from app.utils.diag_watchdog import StallWatchdog
            self._stall_watchdog = StallWatchdog(timeout=5.0)
            self._stall_watchdog.start()

            watchdog_task = asyncio.create_task(self._event_loop_watchdog())
            self._tasks.append(watchdog_task)

            # 保持服务运行，同时监控后台任务的状态
            while self._running:
                try:
                    # 定期输出心跳日志，确保主循环正常运行
                    current_time = time.time()
                    if current_time - getattr(self, '_last_heartbeat', 0) > 120:  # 2分钟输出一次心跳
                        logger.info(f"监控服务心跳检查 - 运行状态: {self._running}, 任务数: {len(self._tasks)}")
                        self._last_heartbeat = current_time

                    # 检查WebSocket任务状态
                    if ws_task.done() and not ws_task.cancelled():
                        exc = ws_task.exception()
                        if exc:
                            logger.error(f"WebSocket客户端异常退出: {str(exc)}")
                            # 尝试重启WebSocket客户端
                            logger.info("尝试重启WebSocket客户端...")
                            ws_task = asyncio.create_task(self.ws_client.start())
                            self._tasks.append(ws_task)

                    # 检查流监控器任务状态
                    if stream_task.done() and not stream_task.cancelled():
                        exc = stream_task.exception()
                        if exc:
                            logger.error(f"流监控器异常退出: {str(exc)}")
                            # 尝试重启流监控器
                            logger.info("尝试重启流监控器...")
                            stream_task = asyncio.create_task(self.stream_monitor.start())
                            self._tasks.append(stream_task)

                    # 检查重连失败次数
                    if self.reconnect_failures >= self.max_reconnect_failures:
                        logger.warning(f"重连失败次数过多 ({self.reconnect_failures})，暂停重连任务")
                        # 暂停重连任务一段时间
                        await asyncio.sleep(300)  # 暂停5分钟
                        self.reconnect_failures = 0

                except Exception as e:
                    logger.error(f"监控任务状态检查异常: {str(e)}", exc_info=True)
                    # 添加异常恢复逻辑
                    try:
                        # 检查关键组件是否还在运行
                        if not ws_task or ws_task.done():
                            logger.warning("WebSocket任务异常，尝试重启")
                            ws_task = asyncio.create_task(self.ws_client.start())
                            self._tasks.append(ws_task)

                        if not stream_task or stream_task.done():
                            logger.warning("流监控器任务异常，尝试重启")
                            stream_task = asyncio.create_task(self.stream_monitor.start())
                            self._tasks.append(stream_task)
                    except Exception as recovery_e:
                        logger.error(f"异常恢复失败: {str(recovery_e)}", exc_info=True)

                await asyncio.sleep(2)  # 增加检查间隔，减少CPU占用

        except Exception as e:
            logger.error(f"监控服务运行异常: {str(e)}", exc_info=True)
            import traceback
            logger.error(f"监控服务异常详情: {traceback.format_exc()}")
            await self.stop()

    async def _event_loop_watchdog(self):
        """事件循环看门狗：检测调度延迟，帮助定位阻塞窗口。"""
        interval = 1.0
        last = time.monotonic()
        while self._running:
            await asyncio.sleep(interval)
            now = time.monotonic()
            drift = (now - last) - interval
            if drift > 2.0:  # 超过2秒认为存在阻塞或严重延迟
                logger.warning(f"事件循环延迟异常: drift={drift:.3f}s")
            last = now
            # 喂狗，表明事件循环仍然活跃
            if hasattr(self, '_stall_watchdog') and self._stall_watchdog:
                try:
                    self._stall_watchdog.pet()
                except Exception:
                    pass

    def _scheduler_listener(self, event):
        """APScheduler 事件监听（仅诊断）。"""
        try:
            code = getattr(event, 'code', None)
            job_id = getattr(event, 'job_id', None)
            msg = f"调度器事件: code={code}, job_id={job_id}"
            # 仅在错误时输出
            if code == EVENT_JOB_ERROR:
                logger.error(msg)
        except Exception:
            pass


    async def _init_drone_streams(self) -> None:
        """初始化所有配置的无人机视频流"""
        try:
            # 使用统一的共享状态管理器获取活跃无人机列表
            from app.utils.shared_state import shared_state_manager
            active_drone_ids = shared_state_manager.get_active_drone_ids()

            logger.info(f"初始化视频流 - 当前活跃的无人机列表: {active_drone_ids}")

            for drone_code in active_drone_ids:
                logger.info(f"开始初始化活跃无人机 {drone_code} 的视频流")
                try:
                    stream = await self.video_manager.get_stream(drone_code, "drone")
                    if stream:
                        logger.info(f"成功初始化无人机 {drone_code} 的视频流")
                    else:
                        logger.warning(f"无法获取无人机 {drone_code} 的视频流")

                    # 如果配置了处理机场视频流，也尝试获取
                    if config.video_stream.process_airport_stream:
                        airport_stream = await self.video_manager.get_stream(drone_code, "airport")
                        if airport_stream:
                            logger.info(f"成功初始化无人机 {drone_code} 的机场视频流")
                        else:
                            logger.warning(f"无法获取无人机 {drone_code} 的机场视频流")
                except Exception as e:
                    logger.error(f"初始化无人机 {drone_code} 视频流失败: {str(e)}")

        except Exception as e:
            logger.error(f"初始化无人机视频流失败: {str(e)}")
            import traceback
            logger.error(f"初始化视频流异常详情: {traceback.format_exc()}")

    async def stop(self) -> None:
        """停止监控服务"""
        if not self._running:
            return

        self._running = False
        logger.info("停止监控服务")

        try:
            # 停止调度器
            if self.scheduler.running:
                self.scheduler.shutdown()
                logger.info("监控服务调度器已停止")

            # 停止WebSocket客户端
            await self.ws_client.stop()
            logger.info("WebSocket客户端已停止")

            # 停止诊断看门狗
            try:
                if hasattr(self, '_stall_watchdog') and self._stall_watchdog:
                    self._stall_watchdog.stop()
            except Exception:
                pass

            # 停止流监控器
            await self.stream_monitor.stop()
            logger.info("流监控器已停止")

            # 取消所有后台任务
            for task in self._tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            self._tasks.clear()
            logger.info("监控服务已完全停止")

        except Exception as e:
            logger.error(f"停止监控服务时发生错误: {str(e)}")
            import traceback
            logger.error(f"停止监控服务错误详情: {traceback.format_exc()}")
        finally:
            # 确保状态重置
            self._running = False
