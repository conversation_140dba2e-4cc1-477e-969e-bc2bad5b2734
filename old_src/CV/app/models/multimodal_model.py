# app/models/multimodal_model.py

import requests
import cv2
import numpy as np
import base64
import json
import logging
from ..utils.config import config


class MultimodalModel:
    """
    用于与 Ollama 服务器进行交互的多模态模型封装示例。
    """
    def __init__(self):
        self.logger = logging.getLogger('app.models.multimodal_model')
        self.ollama_url = config.OLLAMA_URL
        self.local_model = config.LOCAL_MODEL
        self.logger.info(f"初始化多模态模型，使用模型: {self.local_model}")

    def _parse_model_response(self, response_text: str, target: str) -> dict:
        """
        解析模型响应
        :param response_text: 模型返回的原始文本
        :param target: 检测目标
        :return: 解析后的结果字典
        """
        try:
            # 查找 JSON 字符串的开始和结束位置
            start = response_text.find("{")
            end = response_text.rfind("}") + 1
            if start >= 0 and end > start:
                parsed_result = json.loads(response_text[start:end])
            else:
                # 如果直接解析失败，尝试提取关键信息
                self.logger.warning("JSON解析失败，尝试提取关键信息")
                
                # 使用更宽松的解析方式
                count = 0
                confidence = 0.0
                description = ""
                
                # 尝试从文本中提取信息
                if '"count"' in response_text:
                    # 寻找数字
                    import re
                    numbers = re.findall(r'\d+', response_text)
                    if numbers:
                        count = int(numbers[0])
                
                if '"confidence"' in response_text:
                    # 寻找浮点数
                    confidence_match = re.search(r'confidence":\s*([\d.]+)', response_text)
                    if confidence_match:
                        confidence = float(confidence_match.group(1))
                
                if '"description"' in response_text:
                    # 提取描述文本
                    desc_match = re.search(r'description":\s*"([^"]+)"', response_text)
                    if desc_match:
                        description = desc_match.group(1)
                    else:
                        # 如果没有找到格式化的描述，使用整个响应作为描述
                        description = response_text
                
                parsed_result = {
                    "count": count,
                    "confidence": confidence,
                    "description": description
                }
            
            # 构建检测结果
            return {
                "count": int(parsed_result.get("count", 0)),
                "confidence": float(parsed_result.get("confidence", 0.0)),
                "description": parsed_result.get("description", "")
            }
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.warning(f"解析响应失败: {str(e)}")
            raise

    def detect(self, image: np.ndarray, target: str) -> dict:
        """
        统一的图像检测方法，适用于单张图片或视频帧
        :param image: numpy.ndarray 格式的图像数据
        :param target: 目标对象
        :return: 检测结果字典
        """
        try:
            self.logger.debug(f"开始处理图像，目标: {target}")

            # 将图像转换为base64
            _, buffer = cv2.imencode('.jpg', image)
            image_base64 = base64.b64encode(buffer).decode('utf-8')

            # 构建规范化的 prompt，要求模型输出结构化信息
            prompt = (
                f"Please analyze this image and tell me about the {target}. "
                f"Respond in this exact JSON format:\n"
                "{\n"
                '  "count": <number of {target} found, integer>,\n'
                '  "confidence": <confidence level, float between 0 and 1>,\n'
                '  "description": <brief description of the {target}(s) location and characteristics in Chinese>\n'
                "}"
            )

            # 注意 model 名称、接口地址、字段结构需要与您实际的 Ollama 版本对应
            payload = {
                "model": self.local_model, 
                "prompt": prompt,
                "images": [image_base64],
                "stream": False
            }

            max_retries = 2  # 最大重试次数
            for attempt in range(max_retries):
                try:
                    # 向 Ollama 发送 POST 请求
                    response = requests.post(
                        url=f"{self.ollama_url}/api/generate",
                        json=payload,
                        headers={"Content-Type": "application/json"},
                        timeout=60
                    )
                    response.raise_for_status()
                    result = response.json()

                    # 解析模型响应
                    detection_result = self._parse_model_response(result.get("response", ""), target)
                    break  # 如果成功解析，跳出重试循环
                except (json.JSONDecodeError, ValueError) as e:
                    if attempt < max_retries - 1:  # 如果不是最后一次尝试
                        self.logger.debug(f"第 {attempt + 1} 次解析失败，准备重试: {str(e)}")
                        continue
                    else:
                        self.logger.error(f"所有重试都失败了: {str(e)}")
                        raise

            # 对于多模态模型，我们使用原始图像作为 annotated_image
            # 因为模型无法标注具体位置，但我们可以在图像上添加文字描述
            annotated_image = image.copy()
            description_text = f"{target}: {detection_result['count']} found"
            cv2.putText(
                annotated_image,
                description_text,
                (10, 30),  # 位置在左上角
                cv2.FONT_HERSHEY_SIMPLEX,
                1,
                (0, 255, 0),
                2
            )

            # 编码处理后的图像
            _, buffer = cv2.imencode('.jpg', annotated_image)
            encoded_image = base64.b64encode(buffer).decode('utf-8')

            detection_result = {
                "target": target,
                "detections": [{
                    "confidence": detection_result.get("confidence", 0.0),
                    "description": detection_result.get("description", "")
                }],
                "count": int(detection_result.get("count", 0)),
                "annotated_image": encoded_image
            }
            return detection_result
            
        except Exception as e:
            self.logger.error(f"图像处理失败: {str(e)}")
            return {
                "target": target,
                "detections": [],
                "count": 0,
                "annotated_image": None,
                "error": str(e)
            }

        