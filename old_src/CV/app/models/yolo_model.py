from ultralytics import YOLO
from PIL import Image
import cv2
import logging
import base64
import numpy as np
from typing import List, Dict, Optional
from app.utils.config import config

# 创建模块级别的logger，作为备用
module_logger = logging.getLogger('app.models.yolo_model')

class YOLOModel:
    def __init__(self, model_path=None):
        """
        初始化 YOLOv8 模型。
        如果提供了 model_path，则加载自定义权重，否则加载预训练模型。
        """
        self.logger = logging.getLogger('app.models.yolo_model')
        self.model_path = model_path
        self._load_model(model_path)
        
    def _load_model(self, model_path=None):
        """
        加载模型
        :param model_path: 模型路径，如果为None则加载预训练模型
        """
        if model_path:
            self.logger.info(f"加载自定义模型: {model_path}")
            self.model = YOLO(model_path)
        else:
            self.logger.info("加载预训练模型: yolov11x.pt")
            self.model = YOLO('yolov11x.pt')
            
    def reload_model(self, model_path=None):
        """
        重新加载模型，用于热更新
        :param model_path: 新模型路径，如果为None则使用初始化时的路径
        :return: 是否成功重新加载
        """
        try:
            if model_path is None:
                model_path = self.model_path
                
            self.logger.info(f"开始热更新模型: {model_path}")
            # 保存旧模型的引用，以防加载失败时回退
            old_model = self.model

            # 加载新模型
            new_model = YOLO(model_path)

            # 如果加载成功，更新模型引用
            self.model = new_model
            self.model_path = model_path

            self.logger.info(f"模型热更新成功: {model_path}")
            return True
        except Exception as e:
            self.logger.error(f"模型热更新失败: {str(e)}")
            # 如果有旧模型，恢复使用
            if 'old_model' in locals():
                self.model = old_model
                self.logger.info("已恢复使用旧模型")
            return False

    def detect_multiple(self, image: np.ndarray, targets: List[Dict]) -> Optional[dict]:
        """
        检测多个目标
        :param image: numpy.ndarray 格式的图像数据
        :param targets: 目标配置列表，每个目标包含 id, name, keyword, confidence_threshold 等信息
        :return: 检测到目标时返回结果字典，否则返回 None
        """
        try:
            original_image = image.copy()
            results = self.model(image)
            all_detections = []
            detected_targets = []
            
            for result in results:
                # 将控制台输出的性能信息记录到日志
                for box in result.boxes:
                    cls = self.model.names[int(box.cls)]
                    confidence = float(box.conf)
                    
                    # 检查是否是我们要找的目标之一
                    for target in targets:
                        if cls.lower() == target['keyword'].lower():
                            # 获取目标的置信度阈值，如果没有设置则默认为0.5
                            confidence_threshold = target.get('confidence_threshold', 0.5)
                            
                            # 只有当置信度超过阈值时才记录检测结果
                            if confidence >= confidence_threshold:
                                x1, y1, x2, y2 = map(int, box.xyxy.tolist()[0])
                                detection = {
                                    "target_id": target['id'],
                                    "target_name": target['name'],
                                    "box": [x1, y1, x2, y2],
                                    "confidence": confidence,
                                    "label": cls,
                                    "confidence_threshold": confidence_threshold
                                }
                                all_detections.append(detection)
                                detected_targets.append(target)
                                
                                # 绘制边框和标签
                                cv2.rectangle(original_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                cv2.putText(original_image, f"{target['name']} {confidence:.2f}",
                                          (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX,
                                          0.9, (36, 255, 12), 2)
                            else:
                                self.logger.debug(f"目标 {target['name']} 置信度 {confidence:.3f} 低于阈值 {confidence_threshold}")

            # 只有检测到目标时才返回结果
            if all_detections:
                # 将标识后的图像编码为Base64
                _, buffer = cv2.imencode('.jpg', original_image)
                annotated_image = base64.b64encode(buffer).decode('utf-8')

                return {
                    "detections": all_detections,
                    "count": len(all_detections),
                    "annotated_image": annotated_image
                }
            self.logger.debug(f"未检测到目标")
            return None

        except Exception as e:
            self.logger.error(f"多目标检测失败: {str(e)}")
            return None

    def detect(self, image: np.ndarray, target: str, confidence_threshold: float = 0.5) -> Optional[dict]:
        """
        检测单个目标
        :param image: numpy.ndarray 格式的图像数据
        :param target: 目标对象
        :param confidence_threshold: 置信度阈值，默认为0.5
        :return: 检测到目标时返回结果字典，否则返回 None
        """
        try:
            original_image = image.copy()
            results = self.model(image)
            target_results = []
            
            for result in results:
                # 将控制台输出的性能信息记录到日志
                result_info = str(result)
                if "Speed:" in result_info:
                    self.logger.info(f"YOLO性能信息: {result_info}")
                for box in result.boxes:
                    cls = self.model.names[int(box.cls)]
                    if cls.lower() == target.lower():
                        x1, y1, x2, y2 = map(int, box.xyxy.tolist()[0])
                        confidence = float(box.conf)
                        
                        # 只有当置信度超过阈值时才记录检测结果
                        if confidence >= confidence_threshold:
                            target_results.append({
                                "box": [x1, y1, x2, y2],
                                "confidence": confidence,
                                "label": cls
                            })
                            # 绘制边框和标签
                            cv2.rectangle(original_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                            cv2.putText(original_image, f"{cls} {confidence:.2f}",
                                      (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX,
                                      0.9, (36, 255, 12), 2)
                        else:
                            self.logger.debug(f"目标 {target} 置信度 {confidence:.3f} 低于阈值 {confidence_threshold}")

            # 只有检测到目标时才返回结果
            if target_results:
                # 将标识后的图像编码为Base64
                _, buffer = cv2.imencode('.jpg', original_image)
                annotated_image = base64.b64encode(buffer).decode('utf-8')
                
                return {
                    "target": target,
                    "detections": target_results,
                    "count": len(target_results),
                    "annotated_image": annotated_image
                }
            return None
            
        except Exception as e:
            self.logger.error(f"图像处理失败: {str(e)}")
            return None
   