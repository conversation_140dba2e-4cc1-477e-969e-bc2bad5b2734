import asyncio
import logging
import signal
import sys
from typing import List, Callable, Optional

logger = logging.getLogger(__name__)

class AppStateManager:
    """应用状态管理器，负责管理应用的生命周期和优雅关闭"""
    
    def __init__(self):
        self.is_running = False
        self.tasks: List[asyncio.Task] = []
        self.cleanup_callbacks: List[Callable] = []
        self._shutdown_event = asyncio.Event()
        self._graceful_shutdown_timeout = 30  # 优雅关闭超时时间（秒）
        
    def start(self):
        """启动应用状态管理器"""
        self.is_running = True
        logger.info("应用状态管理器已启动")
        
        # 设置信号处理器
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except Exception as e:
            logger.warning(f"设置信号处理器失败: {str(e)}")
    
    def stop(self):
        """停止应用状态管理器"""
        self.is_running = False
        logger.info("应用状态管理器已停止")
    
    def register_task(self, task: asyncio.Task):
        """注册异步任务"""
        if self.is_running:
            self.tasks.append(task)
            logger.debug(f"注册异步任务: {task.get_name() if hasattr(task, 'get_name') else 'unknown'}")
    
    def register_cleanup_callback(self, callback: Callable):
        """注册清理回调函数"""
        self.cleanup_callbacks.append(callback)
        logger.debug(f"注册清理回调函数: {callback.__name__ if hasattr(callback, '__name__') else 'unknown'}")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，开始优雅关闭")
        asyncio.create_task(self._graceful_shutdown())
    
    async def _graceful_shutdown(self):
        """优雅关闭应用"""
        if not self.is_running:
            return
            
        logger.info("开始优雅关闭应用...")
        self.is_running = False
        
        try:
            # 设置关闭事件
            self._shutdown_event.set()
            
            # 取消所有异步任务
            if self.tasks:
                logger.info(f"取消 {len(self.tasks)} 个异步任务")
                for task in self.tasks:
                    if not task.done():
                        task.cancel()
                
                # 等待任务取消完成
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*self.tasks, return_exceptions=True),
                        timeout=10.0
                    )
                except asyncio.TimeoutError:
                    logger.warning("等待任务取消超时")
            
            # 执行清理回调函数
            if self.cleanup_callbacks:
                logger.info(f"执行 {len(self.cleanup_callbacks)} 个清理回调函数")
                for callback in self.cleanup_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await asyncio.wait_for(callback(), timeout=5.0)
                        else:
                            callback()
                    except Exception as e:
                        logger.error(f"执行清理回调函数失败: {str(e)}")
            
            logger.info("优雅关闭完成")
            
        except Exception as e:
            logger.error(f"优雅关闭过程中出错: {str(e)}")
        finally:
            # 强制退出程序
            sys.exit(0)

# 全局应用状态管理器实例
app_state = AppStateManager() 