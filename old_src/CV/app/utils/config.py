import os
import yaml
import logging
from typing import List, Dict, Any, TypedDict

logger = logging.getLogger(__name__)

class DetectionTarget(TypedDict):
    id: int
    name: str
    category: str
    keyword: str
    distance: int
    type: str
    confidence_threshold: float

class Config:
    def __init__(self):
        """初始化配置"""
        self.config_data = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config.yaml')
        try:
            logger.info(f"尝试加载配置文件: {config_path}")
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 记录关键配置信息
            if 'api' in config_data and 'event_report' in config_data['api']:
                logger.info(f"事件上报URL: {config_data['api']['event_report'].get('url', '未配置')}")
            if 'api' in config_data and 'video_save' in config_data['api']:
                logger.info(f"视频保存URL: {config_data['api']['video_save'].get('url', '未配置')}")

            return config_data
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            logger.error(f"配置文件路径: {config_path}")
            logger.error(f"当前工作目录: {os.getcwd()}")
            logger.error(f"文件是否存在: {os.path.exists(config_path)}")
            import traceback
            logger.error(f"加载配置文件异常详情: {traceback.format_exc()}")
            raise

    @property
    def RUN_MODE(self) -> str:
        """获取运行模式"""
        return self.config_data['mode']['run_mode']

    @property
    def IS_DEV_MODE(self) -> bool:
        """是否为开发调试模式"""
        return self.RUN_MODE == 'dev'

    @property
    def IS_PROD_MODE(self) -> bool:
        """是否为生产模式"""
        return self.RUN_MODE == 'prod'

    @property
    def TEMP_VIDEO_DIR(self) -> str:
        """获取临时视频目录（固定路径）"""
        return self.config_data['storage']['temp']['videos']

    @property
    def TEMP_REPORT_DIR(self) -> str:
        """获取临时报告目录（固定路径）"""
        return self.config_data['storage']['temp']['reports']

    @property
    def TEMP_IMAGE_DIR(self) -> str:
        """获取临时图片目录（固定路径）"""
        return self.config_data['storage']['temp']['images']

    @property
    def TEMP_DIR(self) -> str:
        """获取临时目录（固定路径）"""
        return self.config_data['storage']['temp']['base']

    @property
    def REPORTS_DIR(self) -> str:
        """获取报告目录
        开发调试模式：使用本地app_data/reportFile目录
        生产模式：使用/mnt/droneData/reportFile目录
        """
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['storage']['mount_points'][mode]['reports']

    @property
    def RETENTION_DAYS(self) -> int:
        """获取文件保留天数"""
        # 兼容新结构 storage.retention.days
        retention = self.config_data['storage'].get('retention', {})
        return int(retention.get('days', 7))

    @property
    def CLEANUP_TIME(self) -> str:
        """获取清理时间（每天）"""
        # 兼容新结构 storage.retention.cleanup_time
        retention = self.config_data['storage'].get('retention', {})
        return retention.get('cleanup_time', '00:00')

    @property
    def NFS_SERVER(self) -> str:
        """获取NFS服务器地址（固定）"""
        return self.config_data['storage']['nfs']['server']

    @property
    def NFS_REMOTE_PATHS(self) -> Dict[str, str]:
        """获取NFS远程路径配置（固定）"""
        return self.config_data['storage']['nfs']['remote_paths']

    @property
    def IMAGE_DIR(self) -> str:
        """获取图片存储目录
        开发调试模式：使用本地app_data/imageFile目录
        生产模式：使用/mnt/droneData/imageFile目录
        """
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['storage']['mount_points'][mode]['image']

    @property
    def VIDEO_DIR(self) -> str:
        """获取视频存储目录
        开发调试模式：使用本地app_data/videoFile目录
        生产模式：使用/mnt/droneData/videoFile目录
        """
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['storage']['mount_points'][mode]['video']

    @property
    def TEMP_VIDEO_DIR(self) -> str:
        return self.config_data['storage']['temp']['videos']

    @property
    def TEMP_REPORT_DIR(self) -> str:
        return self.config_data['storage']['temp']['reports']

    @property
    def TEMP_IMAGE_DIR(self) -> str:
        """获取临时图片目录（固定路径）"""
        return self.config_data['storage']['temp']['images']

    @property
    def LOG_LEVEL(self) -> str:
        return self.config_data['logging']['level']

    @property
    def LOG_FILE_LEVEL(self) -> str:
        return self.config_data['logging']['file']['level']

    @property
    def LOG_FILE_PATH(self) -> str:
        return self.config_data['logging']['file']['path']

    @property
    def YOLO_MODEL_PATH(self) -> str:
        return self.config_data['models']['yolo']['model_path']

    @property
    def OLLAMA_URL(self) -> str:
        return self.config_data['models']['multimodal']['ollama_url']

    @property
    def LOCAL_MODEL(self) -> str:
        return self.config_data['models']['multimodal']['local_model']

    @property
    def PREPROCESS(self) -> bool:
        return self.config_data['image_processing']['preprocess']

    @property
    def IMAGE_PREPROCESS_METHODS(self) -> List[str]:
        return self.config_data['image_processing']['methods']

    @property
    def BRIGHTNESS_THRESHOLD(self) -> float:
        return float(self.config_data['image_processing']['brightness_threshold'])

    @property
    def DRONE_LIST(self) -> List[str]:
        return self.config_data['drone_list']

    @property
    def DRONE_CONFIG_LIST(self) -> List[Dict[str, str]]:
        """获取无人机配置列表（包含站点名称）"""
        drone_list = self.config_data['drone_list']
        # 兼容旧格式（字符串列表）和新格式（对象列表）
        if drone_list and isinstance(drone_list[0], str):
            # 旧格式，返回默认站点名称
            return [{'drone_code': code, 'site_name': 'DefaultSite'} for code in drone_list]
        else:
            # 新格式，直接返回
            return drone_list

    def get_site_name_by_drone_code(self, drone_code: str) -> str:
        """根据无人机代码获取站点名称"""
        for drone_config in self.DRONE_CONFIG_LIST:
            if drone_config['drone_code'] == drone_code:
                return drone_config['site_name']
        return 'UnknownSite'  # 默认站点名称

    def get_drone_codes(self) -> List[str]:
        """获取所有无人机代码列表"""
        return [config['drone_code'] for config in self.DRONE_CONFIG_LIST]

    @property
    def STREAM_MONITOR(self) -> Dict[str, Any]:
        return self.config_data['stream_monitor']

    @property
    def SR_MODEL_SCALE(self) -> int:
        return int(self.config_data['models']['super_resolution']['model_scale'])

    @property
    def SR_MODEL_PATH(self) -> str:
        return self.config_data['models']['super_resolution']['model_path']

    @property
    def REAL_ESRGAN_PATH(self) -> str:
        return self.config_data['models']['super_resolution']['real_esrgan_path']

    @property
    def ALLOW_ORIGINS(self) -> List[str]:
        return self.config_data['api']['allow_origins']

    @property
    def DETECTION_TARGETS(self) -> List[DetectionTarget]:
        """获取目标识别对象列表"""
        return self.config_data['detection']['targets']

    @property
    def CHECK_DISTANCE(self) -> bool:
        """是否检查距离"""
        return self.config_data['detection'].get('check_distance', True)  # 默认为True

    def get_target_by_id(self, target_id: int) -> DetectionTarget:
        """根据ID获取目标配置"""
        for target in self.DETECTION_TARGETS:
            if target['id'] == target_id:
                return target
        raise ValueError(f"未找到ID为{target_id}的目标配置")

    def get_target_by_name(self, target_name: str) -> DetectionTarget:
        """根据名称获取目标配置"""
        for target in self.DETECTION_TARGETS:
            if target['name'] == target_name:
                return target
        raise ValueError(f"未找到名称为{target_name}的目标配置")

    def get_targets_by_category(self, category: str) -> List[DetectionTarget]:
        """根据类别获取目标配置列表"""
        return [target for target in self.DETECTION_TARGETS
                if target['category'] == category]

    @property
    def EVENT_REPORT_URL(self) -> str:
        """获取事件上报API地址"""
        return self.config_data['api']['event_report']['url']

    @property
    def EVENT_REPORT_CAMERA_TYPE(self) -> str:
        """获取摄像头类型"""
        return self.config_data['api']['event_report']['camera_type']

    @property
    def EVENT_REPORT_IDENTIFY_TYPE(self) -> int:
        """获取识别类型"""
        return self.config_data['api']['event_report']['identify_type']

    @property
    def ENABLE_API(self) -> bool:
        """是否启用API"""
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['mode'][mode]['enable_api']

    @property
    def ENABLE_WEB(self) -> bool:
        """是否启用Web界面"""
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['mode'][mode]['enable_web']

    @property
    def PORT(self) -> int:
        """获取服务端口"""
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['mode'][mode]['port']

    @property
    def VIDEO_SAVE_URL(self) -> str:
        """获取视频保存API地址"""
        return self.config_data['api']['video_save']['url']

    # 已移除 API_KEY 与 allowed_ips 相关逻辑，按产品要求暂不启用
    # 保留占位以避免调用方报错（若存在），后续可删除调用方代码后再清理
    @property
    def API_KEY(self) -> str:
        """API密钥（已停用）"""
        return ""

    @property
    def ALLOWED_IPS(self) -> List[str]:
        """允许访问API的IP地址列表（已停用）"""
        return []

    @property
    def API_RATE_LIMIT_MAX_REQUESTS(self) -> int:
        """API请求频率限制的最大请求数（已停用）"""
        return 0

    @property
    def API_RATE_LIMIT_TIME_WINDOW(self) -> int:
        """API请求频率限制的时间窗口大小（秒）（已停用）"""
        return 0

    @property
    def HTTP_TIMEOUT_TOTAL(self) -> int:
        """获取HTTP请求总超时时间（秒）"""
        return self.config_data['api']['http_timeout'].get('total', 30)

    @property
    def HTTP_TIMEOUT_CONNECT(self) -> int:
        """获取HTTP连接超时时间（秒）"""
        return self.config_data['api']['http_timeout'].get('connect', 10)

    @property
    def HTTP_TIMEOUT_SOCK_READ(self) -> int:
        """获取HTTP读取超时时间（秒）"""
        return self.config_data['api']['http_timeout'].get('sock_read', 15)

    @property
    def VIDEO_STREAM_READ_FAIL_THRESHOLD(self) -> int:
        """获取视频流读取失败阈值"""
        return int(self.config_data['video_stream']['read_fail_threshold'])

    @property
    def VIDEO_STREAM_EMPTY_FRAME_THRESHOLD(self) -> int:
        """获取视频流空帧阈值"""
        return int(self.config_data['video_stream'].get('empty_frame_threshold', 200))

    @property
    def VIDEO_STREAM_FRAME_READ_RETRY(self) -> int:
        """获取帧读取重试次数"""
        return int(self.config_data['video_stream'].get('frame_read_retry', 3))

    @property
    def VIDEO_STREAM_FRAME_READ_RETRY_DELAY(self) -> float:
        """获取帧读取重试延迟（秒）"""
        return float(self.config_data['video_stream'].get('frame_read_retry_delay', 0.1))

    @property
    def VIDEO_STREAM_BUFFER_DURATION(self) -> int:
        """获取视频帧缓存时长（秒）"""
        return int(self.config_data['video_stream']['buffer_duration'])

    @property
    def VIDEO_STREAM_DETECTION_INTERVAL(self) -> float:
        """获取目标检测的时间间隔（秒）"""
        return float(self.config_data['video_stream']['detection_interval'])

    @property
    def VIDEO_STREAM_PROCESS_AIRPORT_STREAM(self) -> bool:
        """获取是否处理机场视频流"""
        return self.config_data['video_stream']['process_airport_stream']

    @property
    def OPENCV_FFMPEG_READ_ATTEMPTS(self) -> int:
        """获取OpenCV FFMPEG读取尝试次数"""
        return int(self.config_data['video_stream'].get('opencv_ffmpeg_read_attempts', 20000))

    @property
    def OPENCV_FFMPEG_CAPTURE_OPTIONS(self) -> str:
        """获取OpenCV FFMPEG捕获选项"""
        return self.config_data['video_stream'].get('opencv_ffmpeg_capture_options', 'rtsp_transport;tcp')

    @property
    def VIDEO_STREAM_RECONNECT_INTERVAL(self) -> int:
        """获取视频流定期重连间隔（分钟）"""
        return int(self.config_data['video_stream'].get('reconnect_interval', 60))

    @property
    def VIDEO_STREAM_RESET_THRESHOLD(self) -> int:
        """获取连续失败多少次后重置VideoCapture的阈值"""
        return int(self.config_data['video_stream'].get('reset_threshold', 5))

    @property
    def DATABASE_HOST(self) -> str:
        """获取数据库主机地址"""
        return self.config_data['database']['host']

    @property
    def DATABASE_PORT(self) -> int:
        """获取数据库端口"""
        return int(self.config_data['database']['port'])

    @property
    def DATABASE_USER(self) -> str:
        """获取数据库用户名"""
        return self.config_data['database']['user']

    @property
    def DATABASE_PASSWORD(self) -> str:
        """获取数据库密码"""
        return self.config_data['database']['password']

    @property
    def DATABASE_NAME(self) -> str:
        """获取数据库名称"""
        return self.config_data['database']['database']

    @property
    def DATABASE_TABLE(self) -> str:
        """获取数据库表名"""
        return self.config_data['database']['table']

    @property
    def NFS_CONFIG(self) -> Dict[str, Any]:
        """获取NFS配置"""
        return self.config_data['nfs']

    @property
    def NFS_MOUNT_POINTS(self) -> Dict[str, str]:
        """获取NFS挂载点配置"""
        return self.config_data['nfs']['mount_points']

    # 为了方便使用，可以添加具体路径的属性
    @property
    def NFS_IMAGE_PATH(self) -> str:
        """获取图片存储的NFS路径"""
        return self.config_data['nfs']['mount_points']['image']

    @property
    def NFS_VIDEO_PATH(self) -> str:
        """获取视频存储的NFS路径"""
        return self.config_data['nfs']['mount_points']['video']

    # 同样添加一个nfs属性，类似于之前的data属性
    @property
    def nfs(self):
        """获取NFS配置对象"""
        return type('NFSConfig', (), {
            'server': self.NFS_SERVER,
            'mount_points': self.NFS_MOUNT_POINTS,
            'remote_paths': self.NFS_REMOTE_PATHS,
            'image_path': self.NFS_IMAGE_PATH,
            'video_path': self.NFS_VIDEO_PATH
        })()

    @property
    def FRAME_RATE(self) -> int:
        """获取视频帧率"""
        return int(self.config_data['video_stream']['frame_rate'])

    @property
    def frame_rate(self) -> int:
        """获取视频帧率（小写别名）"""
        return self.FRAME_RATE

    @property
    def video_stream(self):
        """获取视频流配置对象"""
        return type('VideoStreamConfig', (), {
            'stream_type': self.config_data['video_stream']['stream_type'],
            'webrtc': type('WebRTCConfig', (), {
                'base_url': self.config_data['video_stream']['webrtc']['base_url'],
                'ice_servers': self.config_data['video_stream']['webrtc']['ice_servers']
            })(),
            'rtmp': type('RTMPConfig', (), {
                'base_url': self.config_data['video_stream']['rtmp']['base_url']
            })(),
            'read_fail_threshold': self.config_data['video_stream']['read_fail_threshold'],
            'buffer_duration': self.config_data['video_stream']['buffer_duration'],
            'detection_interval': self.config_data['video_stream']['detection_interval'],
            'process_airport_stream': self.config_data['video_stream']['process_airport_stream'],
            'frame_rate': self.config_data['video_stream']['frame_rate']
        })()

    @property
    def stream_monitor(self):
        """获取流监控配置对象"""
        return type('StreamMonitorConfig', (), {
            'check_interval': self.config_data['stream_monitor']['check_interval'],
            'retry_times': self.config_data['stream_monitor']['retry_times'],
            'retry_interval': self.config_data['stream_monitor']['retry_interval'],
            'only_monitor_config_drones': self.config_data['stream_monitor'].get('only_monitor_config_drones', True)
        })()

    @property
    def scheduler(self):
        """获取调度器配置对象"""
        return type('SchedulerConfig', (), {
            'reconnect_interval': self.config_data['scheduler']['reconnect_interval'],
            'status_check_interval': self.config_data['scheduler']['status_check_interval']
        })()

    @property
    def detection(self):
        """获取目标检测配置对象"""
        return type('DetectionConfig', (), {
            'targets': self.DETECTION_TARGETS,
            'check_distance': self.CHECK_DISTANCE,
            'target_ids': self.config_data['detection']['target_ids']
        })()

    # 检测限流与熔断配置
    @property
    def DETECTION_MAX_CONCURRENCY(self) -> int:
        """检测任务最大并发数"""
        limits = self.config_data.get('detection', {}).get('limits', {})
        return int(limits.get('max_concurrency', 2))

    @property
    def DETECTION_TIMEOUT(self) -> float:
        """单个检测任务超时时间（秒）"""
        limits = self.config_data.get('detection', {}).get('limits', {})
        return float(limits.get('timeout', 5.0))

    @property
    def DETECTION_CIRCUIT_BREAKER_THRESHOLD(self) -> int:
        """熔断阈值：连续失败次数达到该值后打开熔断"""
        limits = self.config_data.get('detection', {}).get('limits', {})
        return int(limits.get('circuit_breaker_threshold', 5))

    @property
    def DETECTION_CIRCUIT_BREAKER_COOLDOWN(self) -> float:
        """熔断冷却时间（秒）"""
        limits = self.config_data.get('detection', {}).get('limits', {})
        return float(limits.get('circuit_breaker_cooldown', 10.0))


    @property
    def database(self):
        """获取数据库配置对象"""
        return type('DatabaseConfig', (), {
            'host': self.DATABASE_HOST,
            'port': self.DATABASE_PORT,
            'user': self.DATABASE_USER,
            'password': self.DATABASE_PASSWORD,
            'database': self.DATABASE_NAME,
            'table': self.DATABASE_TABLE
        })()

    @property
    def rtmp(self):
        """[弃用] 获取RTMP配置对象（请使用 video_stream.rtmp）"""
        import warnings
        warnings.warn("config.rtmp 已弃用，请使用 config.video_stream.rtmp", DeprecationWarning)
        return type('RtmpConfig', (), {
            'base_url': self.config_data['video_stream']['rtmp']['base_url']
        })()

    @property
    def data(self):
        """获取数据相关配置对象"""
        return type('DataConfig', (), {
            'base_dir': self.BASE_DIR,
            'temp_dirs': {
                'videos': self.TEMP_VIDEO_DIR,
                'images': self.TEMP_IMAGE_DIR,
                'reports': self.TEMP_REPORT_DIR
            },
            'retention_days': self.RETENTION_DAYS,
            'cleanup_time': self.CLEANUP_TIME
        })()

    @property
    def api(self):
        """获取API相关配置对象"""
        return type('ApiConfig', (), {
            'event_report': {
                'url': self.EVENT_REPORT_URL,
                'camera_type': self.EVENT_REPORT_CAMERA_TYPE,
                'identify_type': self.EVENT_REPORT_IDENTIFY_TYPE
            },
            'video_save': {
                'url': self.VIDEO_SAVE_URL
            },
            'http_timeout': {
                'total': self.HTTP_TIMEOUT_TOTAL,
                'connect': self.HTTP_TIMEOUT_CONNECT,
                'sock_read': self.HTTP_TIMEOUT_SOCK_READ
            },
            'allow_origins': self.ALLOW_ORIGINS
        })()

    @property
    def models(self):
        """获取模型相关配置对象"""
        return type('ModelsConfig', (), {
            'yolo': {
                'model_path': self.YOLO_MODEL_PATH
            },
            'multimodal': {
                'ollama_url': self.OLLAMA_URL,
                'local_model': self.LOCAL_MODEL
            },
            'super_resolution': {
                'model_path': self.SR_MODEL_PATH,
                'model_scale': self.SR_MODEL_SCALE,
                'real_esrgan_path': self.REAL_ESRGAN_PATH
            }
        })()

    @property
    def image_processing(self):
        """获取图像处理相关配置对象"""
        return type('ImageProcessingConfig', (), {
            'preprocess': self.PREPROCESS,
            'methods': self.IMAGE_PREPROCESS_METHODS,
            'brightness_threshold': self.BRIGHTNESS_THRESHOLD
        })()

    @property
    def IMAGE_BASE64_OUTPUT(self) -> bool:
        """是否在API结果中包含Base64标注图（M4）"""
        return bool(self.config_data.get('image_processing', {}).get('base64_output', True))


    @property
    def mode(self):
        """获取运行模式相关配置对象"""
        mode_name = 'dev' if self.IS_DEV_MODE else 'prod'
        return type('ModeConfig', (), {
            'run_mode': self.RUN_MODE,
            'is_dev': self.IS_DEV_MODE,
            'enable_api': self.ENABLE_API,
            'enable_web': self.ENABLE_WEB,
            'port': self.PORT
        })()

    @property
    def websocket(self):
        """获取WebSocket相关配置对象"""
        return type('WebSocketConfig', (), {
            'url': self.config_data['websocket']['url'],
            'auth': type('WebSocketAuthConfig', (), {
                'username': self.config_data['websocket']['auth']['username'],
                'password': self.config_data['websocket']['auth']['password'],
                'flag': self.config_data['websocket']['auth']['flag'],
                'token_refresh_time': self.config_data['websocket']['auth']['token_refresh_time']
            })(),
            'active_mode_codes': self.config_data['websocket'].get('active_mode_codes', [2, 3, 4])
        })()

    @property
    def WEBSOCKET_URL(self) -> str:
        """获取WebSocket基础URL"""
        return self.config_data['websocket']['url']

    @property
    def WEBSOCKET_USERNAME(self) -> str:
        """获取WebSocket用户名"""
        return self.config_data['websocket']['auth']['username']

    @property
    def WEBSOCKET_PASSWORD(self) -> str:
        """获取WebSocket密码"""
        return self.config_data['websocket']['auth']['password']

    @property
    def WEBSOCKET_FLAG(self) -> int:
        """获取WebSocket标志"""
        return int(self.config_data['websocket']['auth']['flag'])

    @property
    def WEBSOCKET_TOKEN_REFRESH_HOUR(self) -> int:
        """获取WebSocket token刷新时间（小时）"""
        time_str = self.config_data['websocket']['auth']['token_refresh_time']
        return int(time_str.split(':')[0])

    @property
    def WEBSOCKET_TOKEN_REFRESH_MINUTE(self) -> int:
        """获取WebSocket token刷新时间（分钟）"""
        time_str = self.config_data['websocket']['auth']['token_refresh_time']
        return int(time_str.split(':')[1])

    @property
    def WEBSOCKET_ACTIVE_MODE_CODES(self) -> list:
        """获取表示无人机活跃状态的mode_code列表"""
        return self.config_data['websocket'].get('active_mode_codes', [1, 2, 3, 4])

    @property
    def WEBSOCKET_RECONNECT_CONFIG(self) -> dict:
        """获取WebSocket重连配置"""
        return self.config_data['websocket'].get('reconnect', {
            'max_attempts': 20,
            'base_delay': 10,
            'max_delay': 300,
            'max_consecutive_failures': 10,
            'health_check_interval': 60,
            'connection_timeout': 30,
            'no_message_timeout': 300,
            'reconnect_cooldown': 30,
            'night_mode': {
                'start_hour': 22,
                'end_hour': 6,
                'reconnect_interval': 120
            }
        })

    @property
    def WEBSOCKET_MAX_RECONNECT_ATTEMPTS(self) -> int:
        """获取最大重连尝试次数"""
        return self.WEBSOCKET_RECONNECT_CONFIG['max_attempts']

    @property
    def WEBSOCKET_BASE_RECONNECT_DELAY(self) -> int:
        """获取基础重连延迟时间（秒）"""
        return self.WEBSOCKET_RECONNECT_CONFIG['base_delay']

    @property
    def WEBSOCKET_MAX_RECONNECT_DELAY(self) -> int:
        """获取最大重连延迟时间（秒）"""
        return self.WEBSOCKET_RECONNECT_CONFIG['max_delay']

    @property
    def WEBSOCKET_MAX_CONSECUTIVE_FAILURES(self) -> int:
        """获取最大连续失败次数"""
        return self.WEBSOCKET_RECONNECT_CONFIG['max_consecutive_failures']

    @property
    def WEBSOCKET_HEALTH_CHECK_INTERVAL(self) -> int:
        """获取健康检查间隔（秒）"""
        return self.WEBSOCKET_RECONNECT_CONFIG['health_check_interval']

    @property
    def WEBSOCKET_NIGHT_MODE_CONFIG(self) -> dict:
        """获取夜间模式配置"""
        return self.WEBSOCKET_RECONNECT_CONFIG['night_mode']

    @property
    def WEBSOCKET_NIGHT_MODE_START_HOUR(self) -> int:
        """获取夜间模式开始时间（小时）"""
        return self.WEBSOCKET_NIGHT_MODE_CONFIG['start_hour']

    @property
    def WEBSOCKET_NIGHT_MODE_END_HOUR(self) -> int:
        """获取夜间模式结束时间（小时）"""
        return self.WEBSOCKET_NIGHT_MODE_CONFIG['end_hour']

    @property
    def WEBSOCKET_NIGHT_MODE_RECONNECT_INTERVAL(self) -> int:
        """获取夜间模式重连间隔（秒）"""
        return self.WEBSOCKET_NIGHT_MODE_CONFIG['reconnect_interval']

    @property
    def WEBSOCKET_MESSAGE_TIMEOUT(self) -> int:
        """获取WebSocket消息循环超时时间（秒）"""
        return self.WEBSOCKET_RECONNECT_CONFIG.get('message_timeout', 30)

    @property
    def WEBSOCKET_CONNECTION_TIMEOUT(self) -> int:
        """获取WebSocket连接超时时间（秒）"""
        return self.WEBSOCKET_RECONNECT_CONFIG.get('connection_timeout', 30)

    @property
    def WEBSOCKET_NO_MESSAGE_TIMEOUT(self) -> int:
        """获取无消息超时时间（秒）"""
        return self.WEBSOCKET_RECONNECT_CONFIG.get('no_message_timeout', 300)

    @property
    def WEBSOCKET_RECONNECT_COOLDOWN(self) -> int:
        """获取重连冷却时间（秒）"""
        return self.WEBSOCKET_RECONNECT_CONFIG.get('reconnect_cooldown', 30)

    @property
    def BASE_DIR(self) -> str:
        """获取基础数据目录"""
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['storage']['mount_points'][mode]['base']

    @property
    def TEMP_BASE_DIR(self) -> str:
        """获取临时文件基础目录"""
        return self.config_data['storage']['temp']['base']

    @property
    def storage(self):
        """获取存储配置对象"""
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return type('StorageConfig', (), {
            'temp': {
                'videos': self.TEMP_VIDEO_DIR,
                'reports': self.TEMP_REPORT_DIR
            },
            'nfs': {
                'server': self.NFS_SERVER,
                'remote_paths': self.NFS_REMOTE_PATHS,
                'mount_points': self.config_data['storage']['mount_points'][mode]
            },
            'retention': {
                'days': self.RETENTION_DAYS,
                'cleanup_time': self.CLEANUP_TIME
            }
        })()

    @property
    def MOUNT_POINTS(self) -> Dict[str, str]:
        """获取当前运行模式下的挂载点配置"""
        mode = 'dev' if self.IS_DEV_MODE else 'prod'
        return self.config_data['storage']['mount_points'][mode]

    @property
    def BASE_MOUNT_POINT(self) -> str:
        """获取基础目录挂载点"""
        return self.MOUNT_POINTS['base']

    @property
    def IMAGE_MOUNT_POINT(self) -> str:
        """获取图片目录挂载点"""
        return self.MOUNT_POINTS['image']

    @property
    def VIDEO_MOUNT_POINT(self) -> str:
        """获取视频目录挂载点"""
        return self.MOUNT_POINTS['video']

    @property
    def REPORTS_MOUNT_POINT(self) -> str:
        """获取报告目录挂载点"""
        return self.MOUNT_POINTS['reports']

    @property
    def NFS_BASE_PATH(self) -> str:
        """获取NFS基础目录路径"""
        return self.NFS_REMOTE_PATHS['base']

    @property
    def NFS_IMAGE_PATH(self) -> str:
        """获取NFS图片目录路径"""
        return self.NFS_REMOTE_PATHS['image']

    @property
    def NFS_VIDEO_PATH(self) -> str:
        """获取NFS视频目录路径"""
        return self.NFS_REMOTE_PATHS['video']

    @property
    def DATA_DIR(self) -> str:
        """获取基础数据目录"""
        return self.config_data['storage']['data_dir']

    @property
    def TEMP_BASE_DIR(self) -> str:
        """获取临时文件基础目录（修正键名为 base）"""
        return self.config_data['storage']['temp']['base']

    @property
    def webrtc(self):
        """获取WebRTC配置对象"""
        return type('WebRTCConfig', (), {
            'ice_servers': self.config_data['video_stream']['webrtc']['ice_servers']
        })()

    # 帧队列配置
    @property
    def FRAME_QUEUE_MAX_SIZE(self) -> int:
        """帧队列最大大小"""
        return self.config_data.get('frame_queue', {}).get('max_size', 100)

    @property
    def FRAME_QUEUE_TIMEOUT(self) -> float:
        """帧队列超时时间"""
        return self.config_data.get('frame_queue', {}).get('timeout', 1.0)

    @property
    def FRAME_QUEUE_DROP_OLD_FRAMES(self) -> bool:
        """队列满时是否丢弃旧帧"""
        return self.config_data.get('frame_queue', {}).get('drop_old_frames', True)

    # 生产者配置
    @property
    def PRODUCER_READ_TIMEOUT(self) -> float:
        """生产者读取超时时间"""
        return self.config_data.get('producer', {}).get('read_timeout', 2.0)

    @property
    def PRODUCER_MAX_CONSECUTIVE_FAILURES(self) -> int:
        """生产者最大连续失败次数"""
        return self.config_data.get('producer', {}).get('max_consecutive_failures', 5)

    @property
    def PRODUCER_MAX_CONSECUTIVE_EMPTY_FRAMES(self) -> int:
        """生产者最大连续空帧次数"""
        return self.config_data.get('producer', {}).get('max_consecutive_empty_frames', 50)

    @property
    def PRODUCER_FAILURE_SLEEP_TIME(self) -> float:
        """生产者失败后休眠时间"""
        return self.config_data.get('producer', {}).get('failure_sleep_time', 1.0)

    @property
    def PRODUCER_EMPTY_FRAME_SLEEP_TIME(self) -> float:
        """生产者空帧后休眠时间"""
        return self.config_data.get('producer', {}).get('empty_frame_sleep_time', 0.05)

    @property
    def PRODUCER_STATS_INTERVAL(self) -> float:
        """生产者统计信息输出间隔"""
        return self.config_data.get('producer', {}).get('stats_interval', 10.0)

    @property
    def PRODUCER_ENQUEUE_TIMEOUT(self) -> float:
        """生产者帧入队超时时间"""
        return self.config_data.get('producer', {}).get('enqueue_timeout', 0.5)

    @property
    def PRODUCER_ENQUEUE_MAX_RETRIES(self) -> int:
        """生产者帧入队最大重试次数"""
        return self.config_data.get('producer', {}).get('enqueue_max_retries', 2)

    # 连接管理器配置
    @property
    def CONNECTION_HEALTH_CHECK_INTERVAL(self) -> float:
        """连接健康检查间隔"""
        return self.config_data.get('connection_manager', {}).get('health_check_interval', 30.0)

    @property
    def CONNECTION_RECONNECT_MAX_ATTEMPTS(self) -> int:
        """连接重连最大尝试次数"""
        return self.config_data.get('connection_manager', {}).get('reconnect_max_attempts', 3)

    @property
    def CONNECTION_RECONNECT_DELAY(self) -> float:
        """连接重连延迟时间"""
        return self.config_data.get('connection_manager', {}).get('reconnect_delay', 2.0)

    @property
    def CONNECTION_OPEN_TIMEOUT(self) -> int:
        """连接打开超时时间（毫秒）"""
        return self.config_data.get('connection_manager', {}).get('open_timeout', 3000)

    @property
    def CONNECTION_READ_TIMEOUT(self) -> int:
        """连接读取超时时间（毫秒）"""
        return self.config_data.get('connection_manager', {}).get('read_timeout', 2000)

    @property
    def CONNECTION_BUFFER_SIZE(self) -> int:
        """连接缓冲区大小"""
        return self.config_data.get('connection_manager', {}).get('buffer_size', 3)

    # 组件监控配置
    @property
    def MONITOR_CHECK_INTERVAL(self) -> float:
        """监控检查间隔"""
        return self.config_data.get('component_monitor', {}).get('check_interval', 30.0)

    @property
    def MONITOR_MAX_ERROR_COUNT(self) -> int:
        """监控最大错误次数"""
        return self.config_data.get('component_monitor', {}).get('max_error_count', 3)

    @property
    def MONITOR_RECOVERY_ENABLED(self) -> bool:
        """是否启用自动恢复"""
        return self.config_data.get('component_monitor', {}).get('recovery_enabled', True)

    # 监控与健康检查
    @property
    def MONITOR_NFS_CHECK_INTERVAL(self) -> int:
        """NFS自检与重挂载间隔（秒）"""
        return int(self.config_data.get('monitor', {}).get('nfs_check_interval', 60))

    @property
    def MONITOR_KEY_STATS_INTERVAL(self) -> int:
        """关键统计日志输出间隔（秒）"""
        return int(self.config_data.get('monitor', {}).get('key_stats_interval', 120))


# 创建全局配置实例
config = Config()

