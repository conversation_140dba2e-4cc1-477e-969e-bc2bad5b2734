import logging
import sys
from datetime import datetime
import os
from pathlib import Path
import cv2

class CustomFormatter(logging.Formatter):
    """自定义日志格式器，支持彩色输出"""
    grey = "\x1b[38;21m"
    blue = "\x1b[34;21m"
    yellow = "\x1b[33;21m"
    red = "\x1b[31;21m"
    bold_red = "\x1b[31;1m"
    reset = "\x1b[0m"

    format_str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    FORMATS = {
        logging.DEBUG: grey + format_str + reset,
        logging.INFO: blue + format_str + reset,
        logging.WARNING: yellow + format_str + reset,
        logging.ERROR: red + format_str + reset,
        logging.CRITICAL: bold_red + format_str + reset
    }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt)
        return formatter.format(record)

def setup_logger(name, console_level=logging.INFO, file_level=logging.INFO, log_dir=None):
    """
    设置日志器
    :param name: 日志器名称
    :param console_level: 控制台日志级别
    :param file_level: 文件日志级别
    :param log_dir: 日志文件目录
    :return: Logger实例
    """
    from app.utils.config import config
    
    # 设置第三方库的日志级别，抑制不必要的信息
    logging.getLogger('matplotlib').setLevel(logging.WARNING)

    # 设置 OpenCV 日志级别，抑制 H.264 解码错误信息
    # 这些错误是FFmpeg层面的，需要通过环境变量来抑制
    try:
        import os
        # 设置FFmpeg日志级别为quiet，这是最有效的方法
        os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '-8'  # AV_LOG_QUIET
        os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'

        # 同时设置OpenCV的日志级别
        if hasattr(cv2, 'setLogLevel'):
            # 尝试使用OpenCV 4.5+ 的日志级别
            if hasattr(cv2, 'LOG_LEVEL_SILENT'):
                cv2.setLogLevel(cv2.LOG_LEVEL_SILENT)
            elif hasattr(cv2, 'LOG_LEVEL_ERROR'):
                cv2.setLogLevel(cv2.LOG_LEVEL_ERROR)
            else:
                # 使用静默模式（数值最小）
                cv2.setLogLevel(0)
            print("已设置 OpenCV 和 FFmpeg 日志级别，抑制 H.264 解码警告")
        else:
            # 老版本OpenCV不支持日志级别设置
            print("当前OpenCV版本不支持日志级别设置，但已设置FFmpeg环境变量")
    except Exception as e:
        print(f"设置 OpenCV/FFmpeg 日志级别失败: {e}")

    # 抑制其他可能产生大量日志的库
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    log_dir = config.LOG_FILE_PATH
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.setLevel(min(console_level, file_level))

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(CustomFormatter())
    console_handler.setLevel(console_level)
    root_logger.addHandler(console_handler)

    # 文件处理器
    log_dir = Path(log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    file_handler.setLevel(file_level)
    root_logger.addHandler(file_handler)

    # 获取指定名称的日志器
    logger = logging.getLogger(name)
    
    return logger