import os
import logging
import cv2
import asyncio
from datetime import datetime
from typing import Optional, Callable, List, Tuple
from app.utils.config import config

logger = logging.getLogger(__name__)

class FileManager:
    """文件管理工具"""
    
    def __init__(self):
        # 确保基础目录存在
        os.makedirs(config.DATA_DIR, exist_ok=True)
        
        # 创建临时目录（保留用于临时处理）
        self.temp_dirs = {
            'videos': config.TEMP_VIDEO_DIR,
            'reports': config.TEMP_REPORT_DIR,
            'images': config.TEMP_IMAGE_DIR
        }
        for dir_path in self.temp_dirs.values():
            os.makedirs(dir_path, exist_ok=True)
            
        # 创建NFS挂载点目录映射（用于正式存储）
        self.storage_dirs = {
            'videos': config.VIDEO_DIR,  # NFS挂载点视频目录
            'reports': config.REPORTS_DIR,  # 报告目录
            'images': config.IMAGE_DIR   # NFS挂载点图片目录
        }
        for dir_path in self.storage_dirs.values():
            os.makedirs(dir_path, exist_ok=True)
            
        # 记录日志
        logger.debug(f"文件管理器初始化完成，临时目录: {self.temp_dirs}")
        logger.debug(f"文件管理器初始化完成，存储目录: {self.storage_dirs}")
    
    def _get_date_path(self, timestamp: float) -> str:
        """
        根据时间戳获取日期路径
        :param timestamp: 时间戳（毫秒）
        :return: 日期路径（YYYYMMDD）
        """
        dt = datetime.fromtimestamp(timestamp / 1000.0)
        return dt.strftime('%Y%m%d')
    
    def _ensure_date_dir_exists(self, file_type: str, timestamp: float) -> Optional[str]:
        """
        确保日期目录存在，优先NFS目录，失败则回落到本地临时目录（不阻断主流程）
        :param file_type: 文件类型（'videos'/'reports'/'images'）
        :param timestamp: 时间戳（毫秒）
        :return: 日期目录路径，失败则返回None
        """
        if file_type not in self.storage_dirs:
            logger.error(f"未知的文件类型: {file_type}")
            return None

        # 计算日期路径
        date_path = self._get_date_path(timestamp)

        # 1) 优先使用NFS挂载点目录
        try:
            base_dir = self.storage_dirs[file_type]
            full_dir = os.path.join(base_dir, date_path)
            os.makedirs(full_dir, exist_ok=True)
            return full_dir
        except Exception as e:
            logger.warning(f"创建NFS日期目录失败，回落到本地临时目录: {str(e)}")

        # 2) 回落到本地临时目录
        try:
            fallback_base = self.temp_dirs.get(file_type)
            if not fallback_base:
                # 未配置对应临时目录时，回落到全局临时base
                fallback_base = config.TEMP_DIR
            fallback_dir = os.path.join(fallback_base, date_path)
            os.makedirs(fallback_dir, exist_ok=True)
            logger.info(f"已使用本地临时目录作为回落: {fallback_dir}")
            return fallback_dir
        except Exception as e:
            logger.error(f"创建临时目录失败: {str(e)}")
            return None
    
    def get_save_path(self, file_type: str, filename: str, timestamp: float) -> Optional[str]:
        """
        获取文件保存路径
        :param file_type: 文件类型（'videos'/'reports'）
        :param filename: 文件名
        :param timestamp: 时间戳（毫秒）
        :return: 完整的文件保存路径
        """
        try:
            # 确保日期目录存在
            full_dir = self._ensure_date_dir_exists(file_type, timestamp)
            if not full_dir:
                raise ValueError(f"无法创建日期目录: {file_type}")
            
            # 返回完整的文件路径
            return os.path.join(full_dir, filename)
            
        except Exception as e:
            logger.error(f"生成保存路径失败: {str(e)}")
            return None
    
    def generate_filename(self, prefix: str, timestamp: float, ext: str) -> str:
        """
        生成文件名（旧格式，保持兼容性）
        :param prefix: 文件名前缀（如无人机编号）
        :param timestamp: 时间戳（毫秒）
        :param ext: 文件扩展名（如'.mp4'）
        :return: 生成的文件名
        """
        dt = datetime.fromtimestamp(timestamp / 1000.0)
        return f"{prefix}_{dt.strftime('%Y%m%d%H%M%S')}{ext}"
    
    def generate_filename_with_site(self, drone_code: str, site_name: str, timestamp: float, ext: str) -> str:
        """
        生成包含站点名称的文件名
        格式: {timestamp}_{site_name}_{drone_code}{ext}
        :param drone_code: 无人机代码
        :param site_name: 站点名称
        :param timestamp: 时间戳（毫秒）
        :param ext: 文件扩展名（如'.mp4'）
        :return: 生成的文件名
        """
        dt = datetime.fromtimestamp(timestamp / 1000.0)
        return f"{dt.strftime('%Y%m%d%H%M%S')}_{site_name}_{drone_code}{ext}"
    
    def _write_file_safely(self, file_path: str, data: bytes) -> bool:
        """
        安全地写入文件到NFS
        :param file_path: 目标文件路径
        :param data: 要写入的二进制数据
        :return: 是否写入成功
        """
        try:
            # 先写入临时文件
            temp_path = f"{file_path}.tmp"
            with open(temp_path, 'wb') as f:
                f.write(data)
                f.flush()
                os.fsync(f.fileno())  # 确保数据写入磁盘
                
            # 重命名为最终文件名（原子操作）
            os.replace(temp_path, file_path)
            
            # 确保文件确实写入
            if not os.path.exists(file_path):
                raise Exception("文件写入后未找到")
                
            return True
        except Exception as e:
            logger.error(f"文件写入失败: {str(e)}")
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return False
    
    def save_file(self, file_type: str, prefix: str, timestamp: float, 
                 extension: str, save_func: Callable[[str], None]) -> Optional[str]:
        """
        保存文件（通用方法）
        :param file_type: 文件类型（如 'images', 'videos', 'reports'）
        :param prefix: 文件名前缀
        :param timestamp: 时间戳
        :param extension: 文件扩展名（如 '.jpg', '.mp4'）
        :param save_func: 保存函数，接收文件路径作为参数
        :return: 保存的文件路径，失败则返回None
        """
        try:
            # 构建文件名和路径
            filename = self.generate_filename(prefix, timestamp, extension)
            
            # 获取保存路径
            file_path = self.get_save_path(file_type, filename, timestamp)
            if not file_path:
                raise ValueError(f"无法获取保存路径: {file_type}")
                
            # 确保文件目录存在
            file_dir = os.path.dirname(file_path)
            os.makedirs(file_dir, exist_ok=True)
            
            # 检查目录是否可写
            if not os.access(file_dir, os.W_OK):
                logger.error(f"目录不可写: {file_dir}")
                raise ValueError(f"目录不可写: {file_dir}")
            
            # logger.debug(f"准备保存文件: {file_path}")
            
            # 对于图像文件，直接调用保存函数保存到最终路径
            if file_type == 'images':
                # 直接调用保存函数
                save_func(file_path)
                
                # 检查文件是否保存成功
                if not os.path.exists(file_path):
                    raise ValueError(f"文件保存失败: {file_path}")
                    
                logger.debug(f"图像文件保存成功: {file_path}")
                return file_path
            else:
                # 对于其他类型文件，使用临时文件
                temp_path = f"{file_path}.tmp"
                
                logger.debug(f"准备保存临时文件: {temp_path}")
                
                # 调用保存函数
                save_func(temp_path)
                
                # 如果是视频，需要确保文件完整性
                if file_type == 'videos':
                    with open(temp_path, 'rb') as f:
                        data = f.read()
                    if not self._write_file_safely(file_path, data):
                        raise Exception("文件安全写入失败")
                else:
                    # 对于其他类型文件，直接重命名
                    os.replace(temp_path, file_path)
                
                logger.debug(f"文件保存成功: {file_path}")
                return file_path
            
        except Exception as e:
            logger.error(f"保存{file_type}文件失败: {str(e)}")
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return None
        
    async def save_video(self, frames: List, filename: str, frame_size: Tuple[int, int],
                         frame_rate: float = 25.0, codec: str = 'mp4v') -> Optional[str]:
        """
        保存视频帧序列为视频文件
        :param frames: 视频帧列表
        :param filename: 文件名
        :param frame_size: 帧尺寸 (宽, 高)
        :param frame_rate: 帧率
        :param codec: 视频编码器
        :return: 保存的视频文件路径，失败则返回None
        """
        if not frames:
            logger.error("没有帧可以保存")
            return None

        try:
            # 获取当前时间戳（毫秒）
            timestamp = int(datetime.now().timestamp() * 1000)

            # 记录视频保存参数
            logger.debug(f"开始保存视频: 帧数={len(frames)}, 帧尺寸={frame_size}, 帧率={frame_rate}, 编码器={codec}")

            # 确保视频目录存在
            date_str = datetime.now().strftime('%Y%m%d')
            video_dir = os.path.join(self.temp_dirs['videos'], date_str)
            os.makedirs(video_dir, exist_ok=True)
            logger.debug(f"视频将保存到目录: {video_dir}")

            # 获取绝对路径
            abs_video_dir = os.path.abspath(video_dir)

            # 生成视频文件名 - 简化文件名以避免问题
            simple_prefix = os.path.splitext(filename)[0].split('_')[0]  # 只取第一部分
            dt = datetime.fromtimestamp(timestamp / 1000.0)
            video_filename = f"{simple_prefix}_{dt.strftime('%Y%m%d%H%M%S')}.mp4"
            abs_video_path = os.path.join(abs_video_dir, video_filename)

            logger.debug(f"视频将保存为: {abs_video_path}")

            # 检查目录是否可写
            if not os.access(abs_video_dir, os.W_OK):
                logger.error(f"视频目录不可写: {abs_video_dir}")
                return None

            # 将重度IO/CPU写入放到线程池，避免阻塞事件循环
            def _write_video(path: str) -> int:
                fourcc = cv2.VideoWriter_fourcc(*codec)
                out = cv2.VideoWriter(path, fourcc, frame_rate, frame_size)
                if not out.isOpened():
                    raise RuntimeError(f"无法创建视频写入器: {path}")
                cnt = 0
                for frame in frames:
                    out.write(frame)
                    cnt += 1
                    if cnt % 100 == 0:
                        # 线程中简单打印到日志
                        logger.debug(f"视频写入进度: {cnt}/{len(frames)} 帧")
                out.release()
                return cnt

            loop = asyncio.get_running_loop()
            frame_count = await loop.run_in_executor(None, _write_video, abs_video_path)
            logger.debug(f"视频写入完成: 共写入 {frame_count} 帧")

            # 检查文件是否创建成功
            if os.path.exists(abs_video_path):
                file_size = os.path.getsize(abs_video_path)
                expected_duration = frame_count / frame_rate
                logger.debug(f"视频保存成功: {abs_video_path}, 大小: {file_size} 字节, 预期时长: {expected_duration:.2f}秒")
                return abs_video_path
            else:
                logger.error(f"视频文件未创建: {abs_video_path}")
                return None

        except Exception as e:
            logger.error(f"保存视频失败: {str(e)}")
            import traceback
            logger.error(f"保存视频错误详情: {traceback.format_exc()}")
            return None

    async def save_detection_video(self, stream_processor, timestamp: float, duration: float = 50.0) -> Optional[str]:
        """
        保存检测到目标时的视频片段
        :param stream_processor: StreamProcessor实例
        :param timestamp: 检测时间戳（毫秒）
        :param duration: 视频片段总时长（秒），默认为5秒
        :return: 保存的视频文件路径
        """
        try:
            logger.debug(f"开始保存视频片段，时间戳: {timestamp}, 持续时间: {duration}秒")
            
            # 获取检测时间点前面的帧（从当前时间戳往前推duration秒）
            frames = stream_processor.get_frames_before_timestamp(timestamp, duration)
            
            if not frames:
                logger.warning(f"未找到时间戳 {timestamp} 前面的帧")
                return None
                
            # 记录找到的帧数和时间范围
            start_time = frames[0].timestamp
            end_time = frames[-1].timestamp
            actual_duration = (end_time - start_time) / 1000.0  # 转换为秒
            frame_count = len(frames)
            
            logger.debug(f"找到 {frame_count} 帧用于保存视频，时间范围: {start_time}-{end_time}, 实际持续时间: {actual_duration:.2f}秒")
            logger.debug(f"帧时间间隔统计: 最小={min([frames[i+1].timestamp - frames[i].timestamp for i in range(len(frames)-1)]) if len(frames) > 1 else 0:.2f}毫秒, "
                       f"最大={max([frames[i+1].timestamp - frames[i].timestamp for i in range(len(frames)-1)]) if len(frames) > 1 else 0:.2f}毫秒, "
                       f"平均={sum([frames[i+1].timestamp - frames[i].timestamp for i in range(len(frames)-1)]) / (len(frames)-1) if len(frames) > 1 else 0:.2f}毫秒")
            
            # 检查帧数量是否足够
            expected_frames = duration * stream_processor.frame_rate / 2 # 保存视频帧率减半
            # if frame_count < expected_frames * 0.5:  # 如果帧数少于预期的50%
            #     logger.warning(f"帧数量不足: 实际={frame_count}, 预期={expected_frames}, 可能导致视频时长不足")
                
            # 获取第一帧的尺寸
            height, width = frames[0].frame.shape[:2]
            # logger.debug(f"视频帧尺寸: {width}x{height}, 类型: {frames[0].frame.dtype}")
            
            # 生成UUID
            import uuid
            file_uuid = str(uuid.uuid4())
            
            # 生成视频文件名
            filename = f"{stream_processor.drone_code}_{stream_processor.stream_type}_{file_uuid}_{int(timestamp)}.mp4"
            logger.debug(f"生成视频文件名: {filename}")

            save_frame_rate = stream_processor.frame_rate/2 # 保存视频帧率减半
            
            # 保存视频文件
            video_path = await self.save_video(
                frames=[f.frame for f in frames],
                filename=filename,
                frame_size=(width, height),
                frame_rate=save_frame_rate
            )
            
            if video_path:
                file_size = os.path.getsize(video_path) if os.path.exists(video_path) else 0
                # logger.debug(f"已保存检测视频: {video_path}, 帧数: {frame_count}, 文件大小: {file_size} 字节")
                
                # 计算预期视频时长
                expected_duration = frame_count / stream_processor.frame_rate
                logger.debug(f"预期视频时长: {expected_duration:.2f}秒 (基于帧数={frame_count}和帧率={stream_processor.frame_rate})")
            else:
                logger.error(f"视频保存失败: {filename}")
                
            return video_path
            
        except Exception as e:
            logger.error(f"保存视频失败: {str(e)}")
            import traceback
            logger.error(f"保存视频错误详情: {traceback.format_exc()}")
            return None

    def save_file_with_drone_info(self, file_type: str, drone_code: str, site_name: str, 
                                 timestamp: float, extension: str, 
                                 save_func: Callable[[str], None]) -> Optional[str]:
        """
        保存文件（使用新的命名格式，包含站点信息）
        :param file_type: 文件类型（如 'images', 'videos', 'reports'）
        :param drone_code: 无人机代码
        :param site_name: 站点名称
        :param timestamp: 时间戳
        :param extension: 文件扩展名（如 '.jpg', '.mp4'）
        :param save_func: 保存函数，接收文件路径作为参数
        :return: 保存的文件路径，失败则返回None
        """
        try:
            # 构建文件名和路径
            filename = self.generate_filename_with_site(drone_code, site_name, timestamp, extension)
            
            # 获取保存路径
            file_path = self.get_save_path(file_type, filename, timestamp)
            if not file_path:
                raise ValueError(f"无法获取保存路径: {file_type}")
                
            # 确保文件目录存在
            file_dir = os.path.dirname(file_path)
            os.makedirs(file_dir, exist_ok=True)
            
            # 检查目录是否可写
            if not os.access(file_dir, os.W_OK):
                logger.error(f"目录不可写: {file_dir}")
                raise ValueError(f"目录不可写: {file_dir}")
            
            # logger.debug(f"准备保存文件: {file_path}")
            
            # 对于图像文件，直接调用保存函数保存到最终路径
            if file_type == 'images':
                # 直接调用保存函数
                save_func(file_path)
                
                # 检查文件是否保存成功
                if not os.path.exists(file_path):
                    raise ValueError(f"文件保存失败: {file_path}")
                    
                logger.debug(f"图像文件保存成功: {file_path}")
                return file_path
            else:
                # 对于其他类型文件，使用临时文件
                temp_path = f"{file_path}.tmp"
                
                logger.debug(f"准备保存临时文件: {temp_path}")
                
                # 调用保存函数
                save_func(temp_path)
                
                # 如果是视频，需要确保文件完整性
                if file_type == 'videos':
                    with open(temp_path, 'rb') as f:
                        data = f.read()
                    if not self._write_file_safely(file_path, data):
                        raise Exception("文件安全写入失败")
                else:
                    # 对于其他类型文件，直接重命名
                    os.replace(temp_path, file_path)
                
                logger.debug(f"文件保存成功: {file_path}")
                return file_path
            
        except Exception as e:
            logger.error(f"保存{file_type}文件失败: {str(e)}")
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return None

# 创建全局文件管理器实例
file_manager = FileManager() 