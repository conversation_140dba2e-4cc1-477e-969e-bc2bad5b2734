from dataclasses import dataclass
from typing import Any, Dict, Optional
import numpy as np
import time

@dataclass
class FrameData:
    """视频帧数据结构"""
    frame: np.ndarray
    timestamp: float
    frame_id: int
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        """验证帧数据的有效性"""
        if self.frame is None or self.frame.size == 0:
            raise ValueError("帧数据不能为空")
        if self.timestamp <= 0:
            raise ValueError("时间戳必须为正数")

@dataclass
class FrameStats:
    """帧统计信息"""
    total_frames: int = 0
    dropped_frames: int = 0
    queue_size: int = 0
    last_frame_time: float = 0.0
    producer_fps: float = 0.0
    consumer_fps: float = 0.0
    
    def update_producer_stats(self, frame_count: int, time_window: float):
        """更新生产者统计"""
        self.total_frames = frame_count
        self.producer_fps = frame_count / time_window if time_window > 0 else 0.0
        self.last_frame_time = time.time()
    
    def update_consumer_stats(self, processed_count: int, time_window: float):
        """更新消费者统计"""
        self.consumer_fps = processed_count / time_window if time_window > 0 else 0.0
    
    def update_queue_stats(self, current_size: int, dropped_count: int = 0):
        """更新队列统计"""
        self.queue_size = current_size
        self.dropped_frames += dropped_count 