import os
import cv2
import numpy as np
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('video_test')

def test_video_save():
    """测试直接保存视频文件"""
    try:
        # 创建一个简单的视频帧
        frames = []
        for i in range(10):
            # 创建一个彩色帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            # 添加一些文本
            cv2.putText(frame, f"Frame {i}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            # 添加一个矩形
            cv2.rectangle(frame, (100, 100), (200, 200), (0, 0, 255), 2)
            frames.append(frame)
        
        # 确保目录存在
        date_str = datetime.now().strftime('%Y%m%d')
        video_dir = f"./app_data/temp/videos/{date_str}"
        os.makedirs(video_dir, exist_ok=True)
        logger.info(f"视频目录: {video_dir}")
        
        # 生成视频文件名
        timestamp = int(datetime.now().timestamp())
        video_filename = f"test_{timestamp}.mp4"
        video_path = os.path.join(video_dir, video_filename)
        logger.info(f"视频文件路径: {video_path}")
        
        # 获取绝对路径
        abs_video_path = os.path.abspath(video_path)
        logger.info(f"绝对路径: {abs_video_path}")
        
        # 检查目录是否可写
        if not os.access(os.path.dirname(abs_video_path), os.W_OK):
            logger.error(f"目录不可写: {os.path.dirname(abs_video_path)}")
            return
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(abs_video_path, fourcc, 25.0, (640, 480))
        
        if not out.isOpened():
            logger.error(f"无法创建视频写入器: {abs_video_path}")
            return
        
        # 写入帧
        for frame in frames:
            out.write(frame)
        
        # 释放资源
        out.release()
        
        # 检查文件是否创建成功
        if os.path.exists(abs_video_path):
            file_size = os.path.getsize(abs_video_path)
            logger.info(f"视频保存成功: {abs_video_path}, 大小: {file_size} 字节")
        else:
            logger.error(f"视频文件未创建: {abs_video_path}")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    test_video_save() 