from fastapi import APIRouter, WebSocket
from typing import Dict
import logging
from app.processor.stream_processor import StreamMonitor

logger = logging.getLogger(__name__)
router = APIRouter()

# 存储所有活跃的监控器
monitors: Dict[str, StreamMonitor] = {}

@router.websocket("/ws/monitor/{client_id}")
async def monitor_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接端点，用于实时监控结果推送"""
    try:
        await websocket.accept()
        logger.info(f"WebSocket连接成功建立: {client_id}")

        # 创建监控器
        monitor = StreamMonitor()
        monitors[client_id] = monitor

        # 定义回调函数
        async def callback(result: dict):
            if websocket.client_state.CONNECTED:
                await websocket.send_json(result)

        # 开始监控
        await monitor.start_monitoring(callback)

        try:
            while True:
                data = await websocket.receive_json()
                if data.get("type") == "stop":
                    break
        except Exception as e:
            logger.error(f"WebSocket通信错误: {str(e)}")

    except Exception as e:
        logger.error(f"监控过程中出错: {str(e)}")
    finally:
        # 清理资源
        if client_id in monitors:
            monitors[client_id].stop_monitoring()
            del monitors[client_id]
        logger.info(f"WebSocket连接关闭: {client_id}")

@router.post("/monitor/start")
async def start_monitor():
    """启动监控服务"""
    try:
        client_id = "default"
        if client_id not in monitors:
            monitor = StreamMonitor()
            monitors[client_id] = monitor
            # 这里可以添加其他初始化逻辑
        return {"status": "success", "client_id": client_id}
    except Exception as e:
        logger.error(f"启动监控失败: {str(e)}")
        return {"status": "error", "message": str(e)}

@router.post("/monitor/stop/{client_id}")
async def stop_monitor(client_id: str):
    """停止监控服务"""
    try:
        if client_id in monitors:
            monitors[client_id].stop_monitoring()
            del monitors[client_id]
        return {"status": "success"}
    except Exception as e:
        logger.error(f"停止监控失败: {str(e)}")
        return {"status": "error", "message": str(e)} 