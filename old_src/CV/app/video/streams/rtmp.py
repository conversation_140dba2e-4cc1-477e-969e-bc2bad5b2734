import logging
import asyncio
import time
from typing import Optional, Any
from .base import BaseVideoStream
from .rtmp_connection_manager import RTMPConnectionManager
from .frame_producer import FrameProducer
from app.utils.config import config
from app.utils.async_frame_queue import AsyncFrameQueue
from app.utils.component_monitor import ComponentMonitor, ComponentState
from app.utils.thread_frame_queue import ThreadFrameQueue

logger = logging.getLogger(__name__)

class RTMPStream(BaseVideoStream):
    """RTMP视频流处理器"""

    def __init__(self):
        """初始化RTMP流"""
        super().__init__()
        self.rtmp_url: Optional[str] = None
        self.stream_key: Optional[str] = None
        self.connection_manager: Optional[RTMPConnectionManager] = None
        self.frame_queue: Optional[AsyncFrameQueue] = None
        self.thread_frame_queue: Optional[ThreadFrameQueue] = None
        self.frame_producer: Optional[FrameProducer] = None
        self.component_monitor: Optional[ComponentMonitor] = None
        self._bridge_task: Optional[asyncio.Task] = None
        self.is_running = False

        # 统计信息
        self.frame_count = 0
        self.start_time = 0.0

    async def start(self, stream_id: str) -> bool:
        """
        初始化RTMP流连接
        :param stream_id: 视频流ID
        :return: 是否成功初始化
        """
        try:
            # 构建RTMP URL和流标识符（统一使用 video_stream.rtmp.base_url，config.rtmp 已弃用）
            self.rtmp_url = f"{config.video_stream.rtmp.base_url}{stream_id}-81-0-0"
            self.stream_key = f"{stream_id}_rtmp"
            logger.info(f"初始化RTMP流连接: {self.rtmp_url}")

            # 创建帧队列
            self.frame_queue = AsyncFrameQueue(
                max_size=config.FRAME_QUEUE_MAX_SIZE,
                drop_old_frames=config.FRAME_QUEUE_DROP_OLD_FRAMES
            )

            # 创建连接管理器
            self.connection_manager = RTMPConnectionManager(self.rtmp_url)

            # 创建组件监控器
            self.component_monitor = ComponentMonitor()

            # 创建帧生产者
            self.frame_producer = FrameProducer(
                connection_manager=self.connection_manager,
                frame_queue=self.frame_queue,
                stream_key=self.stream_key,
                on_error=self._on_producer_error
            )

            # 设置事件循环
            try:
                current_loop = asyncio.get_running_loop()
                self.frame_producer.set_event_loop(current_loop)
                logger.debug(f"使用当前运行的事件循环: {self.stream_key}")
            except RuntimeError:
                # 如果没有运行中的事件循环，这是一个错误状态
                logger.error(f"没有运行中的事件循环，无法启动帧生产者: {self.stream_key}")
                raise RuntimeError("必须在异步上下文中启动RTMP流")

            # 启动组件监控器
            await self.component_monitor.start()

            # 注册组件到监控器
            self.component_monitor.register_component(
                f"connection_{self.stream_key}",
                recovery_callback=self._recover_connection,
                shutdown_callback=self._shutdown_connection
            )

            self.component_monitor.register_component(
                f"producer_{self.stream_key}",
                recovery_callback=self._recover_producer,
                shutdown_callback=self._shutdown_producer
            )

            # 设置连接管理器为活跃状态（FrameProducer会处理实际连接）
            self.connection_manager.set_active(True)

            # 更新连接状态（初始状态，实际连接由FrameProducer管理）
            self.component_monitor.update_component_state(
                f"connection_{self.stream_key}",
                ComponentState.RUNNING
            )

            # 启动帧生产者
            producer_success = self.frame_producer.start_producing()
            if not producer_success:
                logger.error(f"无法启动帧生产者: {self.stream_key}")
                await self.stop()
                return False

            # 标记为运行中并记录开始时间（在启动桥接任务之前）
            self.is_running = True
            self.start_time = time.time()

            # 创建线程队列并启动桥接任务
            self.thread_frame_queue = ThreadFrameQueue(
                max_size=config.FRAME_QUEUE_MAX_SIZE,
                drop_old_frames=config.FRAME_QUEUE_DROP_OLD_FRAMES
            )
            self._bridge_task = asyncio.create_task(self._bridge_frames())
            logger.debug(f"桥接任务已启动: {self.stream_key}")

            # 更新生产者状态
            self.component_monitor.update_component_state(
                f"producer_{self.stream_key}",
                ComponentState.RUNNING
            )

            logger.info(f"成功启动RTMP流: {self.rtmp_url}")
            return True

        except Exception as e:
            logger.error(f"初始化RTMP流连接失败: {self.rtmp_url}, 错误: {str(e)}")
            await self.stop()
            return False

    async def stop(self) -> None:
        """停止RTMP视频流"""
        if not self.is_running:
            return

        logger.info(f"停止RTMP流: {self.rtmp_url}")
        self.is_running = False

        try:
            # 停止帧生产者
            if self.frame_producer:
                self.frame_producer.stop_producing()

            # 设置连接管理器为非活跃状态
            if self.connection_manager:
                self.connection_manager.set_active(False)

            # 停止桥接任务
            if self._bridge_task:
                try:
                    self._bridge_task.cancel()
                    await asyncio.sleep(0)  # 让取消生效
                except Exception:
                    pass
                self._bridge_task = None

            # 停止组件监控器
            if self.component_monitor:
                await self.component_monitor.stop()

            # 关闭帧队列
            if self.frame_queue:
                await self.frame_queue.close()
            if self.thread_frame_queue:
                self.thread_frame_queue.close()

            # 输出最终统计信息
            if self.frame_producer:
                stats = self.frame_producer.get_stats()
                logger.info(f"RTMP流最终统计: {stats}")

        except Exception as e:
            logger.error(f"停止RTMP流时出错: {self.rtmp_url}, 错误: {str(e)}")

        # logger.info(f"已停止RTMP流: {self.rtmp_url}")

    async def check_stream(self) -> bool:
        """
        检查RTMP流是否活跃
        :return: 是否活跃
        """
        if not self.is_running:
            return False

        try:
            # 检查是否刚启动（给启动过程一些缓冲时间）
            current_time = time.time()
            startup_grace_period = 10.0  # 10秒启动缓冲期
            if current_time - self.start_time < startup_grace_period:
                logger.debug(f"RTMP流在启动缓冲期内，假设健康: {self.rtmp_url}")
                return True

            # 检查组件状态
            if not self.component_monitor:
                logger.warning(f"组件监控器不可用: {self.rtmp_url}")
                # 如果组件监控器不可用，但流标记为运行，则检查生产者
                return self.frame_producer and self.frame_producer.is_running()

            # 检查生产者状态（主要检查项）
            producer_running = self.frame_producer and self.frame_producer.is_running()

            # 检查连接管理器活跃状态
            connection_active = False
            if self.connection_manager:
                connection_active = self.connection_manager.is_active()

            # 更新组件状态
            if connection_active:
                self.component_monitor.update_component_state(
                    f"connection_{self.stream_key}",
                    ComponentState.RUNNING
                )
            else:
                self.component_monitor.update_component_state(
                    f"connection_{self.stream_key}",
                    ComponentState.ERROR,
                    error_message="连接未激活"
                )

            if producer_running:
                self.component_monitor.update_component_state(
                    f"producer_{self.stream_key}",
                    ComponentState.RUNNING
                )
            else:
                self.component_monitor.update_component_state(
                    f"producer_{self.stream_key}",
                    ComponentState.ERROR,
                    error_message="生产者未运行"
                )

            # 放宽健康检查条件：只要生产者在运行就认为健康
            # 连接管理器的状态可能因为临时网络问题而变化
            is_healthy = producer_running

            if is_healthy:
                logger.debug(f"RTMP流检查成功: {self.rtmp_url}")
            else:
                logger.warning(f"RTMP流检查失败，生产者状态: {producer_running}: {self.rtmp_url}")

            return is_healthy

        except Exception as e:
            logger.error(f"检查RTMP流时出错: {self.rtmp_url}, 错误: {str(e)}")
            return False

    async def get_frame(self) -> Optional[Any]:
        """
        从帧队列获取视频帧
        :return: 视频帧，如果读取失败则返回None
        """
        if not self.is_running or self.frame_queue is None:
            return None

        try:
            # 从帧队列获取帧数据
            frame_data = await self.frame_queue.get_frame(timeout=config.FRAME_QUEUE_TIMEOUT)

            if frame_data is None:
                return None

            # 更新帧计数
            self.frame_count += 1

            # 定期输出统计信息和收集性能指标
            if self.frame_count % 100 == 0:
                queue_stats = self.frame_queue.get_stats()
                logger.debug(f"获取RTMP帧成功: {self.rtmp_url}, "
                           f"帧计数: {self.frame_count}, "
                           f"队列大小: {self.frame_queue.get_queue_size()}, "
                           f"丢帧数: {queue_stats.dropped_frames}")

                # 收集性能指标
                # if self.performance_monitor and self.frame_producer:
                #     current_time = time.time()
                #     elapsed_time = current_time - self.start_time
                #     current_fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0

                #     metrics = PerformanceMetrics(
                #         timestamp=current_time,
                #         fps=current_fps,
                #         queue_size=self.frame_queue.get_queue_size(),
                #         dropped_frames=queue_stats.dropped_frames,
                #         memory_usage=0.0,  # 将由监控器更新
                #         cpu_usage=0.0      # 将由监控器更新
                #     )

                #     self.performance_monitor.add_metrics(self.stream_key, metrics)

            return frame_data.frame

        except Exception as e:
            logger.error(f"获取RTMP帧时出错: {self.rtmp_url}, 错误: {str(e)}")
            return None


    async def _bridge_frames(self):
        """将异步帧队列的数据桥接到线程队列，供阻塞读取使用。"""
        bridged = 0
        last_log = time.time()
        # logger.debug(f"启动帧桥接: self.is_ running: {self.is_running}, self.frame_queue: {self.frame_queue}, self.thread_frame_queue: {self.thread_frame_queue}")
        while self.is_running and (self.frame_queue is not None) and (self.thread_frame_queue is not None):
            # logger.debug(f"正在桥接帧: {self.stream_key}")
            try:
                # logger.debug(f"正在获取帧: {self.stream_key}")
                frame_data = await self.frame_queue.get_frame(timeout=config.FRAME_QUEUE_TIMEOUT)
                # logger.debug(f"已获取帧: {len(frame_data.frame)}")
                if frame_data is None:
                    logger.debug(f"获取帧失败: {self.stream_key}")
                    continue
                self.thread_frame_queue.put(frame_data)
                bridged += 1
                # 每秒或每50帧输出一次桥接状态
                now = time.time()
                if bridged % 50 == 0 or (now - last_log) >= 1.0:
                    try:
                        qsize = self.frame_queue.get_queue_size()
                    except Exception:
                        qsize = -1
                    # logger.debug(f"桥接状态: {self.stream_key}, 已桥接帧={bridged}, async_q={qsize}, thread_q={self.thread_frame_queue.size()}")
                    last_log = now
            except asyncio.CancelledError:
                break
            except Exception:
                logger.exception("桥接帧时发生异常")
                await asyncio.sleep(0)

    def get_frame_blocking(self, timeout: float = 1.0) -> Optional[Any]:
        """线程友好、阻塞式获取一帧。"""
        if not self.is_running or not self.thread_frame_queue:
            return None
        try:
            frame_data = self.thread_frame_queue.get(timeout=timeout)
            return frame_data.frame if frame_data is not None else None
        except Exception:
            return None

    def _on_producer_error(self, error: Exception):
        """处理生产者错误的回调函数"""
        logger.error(f"生产者错误: {self.stream_key}, 错误: {str(error)}")

        # 更新组件状态
        if self.component_monitor:
            self.component_monitor.update_component_state(
                f"producer_{self.stream_key}",
                ComponentState.ERROR,
                error_message=str(error)
            )

    async def _recover_connection(self) -> bool:
        """恢复连接的回调函数（现在连接由FrameProducer管理）"""
        try:
            logger.info(f"尝试恢复连接: {self.stream_key}")

            if not self.connection_manager:
                return False

            # 重新激活连接管理器（实际重连由FrameProducer处理）
            self.connection_manager.set_active(True)

            # 检查生产者是否正在运行，如果没有则尝试重启
            if self.frame_producer and not self.frame_producer.is_running():
                success = self.frame_producer.start_producing()
                if success:
                    logger.info(f"连接恢复成功（重启生产者）: {self.stream_key}")
                    return True
                else:
                    logger.error(f"连接恢复失败（无法重启生产者）: {self.stream_key}")
                    return False
            else:
                logger.info(f"连接恢复成功（生产者已运行）: {self.stream_key}")
                return True

        except Exception as e:
            logger.error(f"恢复连接时出错: {self.stream_key}, 错误: {str(e)}")
            return False

    async def _shutdown_connection(self):
        """关闭连接的回调函数"""
        try:
            logger.info(f"关闭连接: {self.stream_key}")

            if self.connection_manager:
                self.connection_manager.set_active(False)

        except Exception as e:
            logger.error(f"关闭连接时出错: {self.stream_key}, 错误: {str(e)}")

    async def _recover_producer(self) -> bool:
        """恢复生产者的回调函数"""
        try:
            logger.info(f"尝试恢复生产者: {self.stream_key}")

            if not self.frame_producer:
                return False

            # 如果生产者未运行，尝试重新启动
            if not self.frame_producer.is_running():
                # 确保事件循环已设置
                try:
                    current_loop = asyncio.get_running_loop()
                    self.frame_producer.set_event_loop(current_loop)
                except RuntimeError:
                    logger.warning(f"恢复生产者时没有运行中的事件循环: {self.stream_key}")

                success = self.frame_producer.start_producing()

                if success:
                    logger.info(f"生产者恢复成功: {self.stream_key}")
                    return True
                else:
                    logger.error(f"生产者恢复失败: {self.stream_key}")
                    return False
            else:
                # 生产者已在运行
                return True

        except Exception as e:
            logger.error(f"恢复生产者时出错: {self.stream_key}, 错误: {str(e)}")
            return False

    async def _shutdown_producer(self):
        """关闭生产者的回调函数"""
        try:
            logger.info(f"关闭生产者: {self.stream_key}")

            if self.frame_producer:
                self.frame_producer.stop_producing()

        except Exception as e:
            logger.error(f"关闭生产者时出错: {self.stream_key}, 错误: {str(e)}")

    def get_stream_stats(self) -> dict:
        """获取流统计信息"""
        stats = {
            "stream_key": self.stream_key,
            "rtmp_url": self.rtmp_url,
            "is_running": self.is_running,
            "frame_count": self.frame_count,
            "start_time": self.start_time
        }

        # 添加生产者统计
        if self.frame_producer:
            stats["producer"] = self.frame_producer.get_stats()

        # 添加队列统计
        if self.frame_queue:
            queue_stats = self.frame_queue.get_stats()
            stats["queue"] = {
                "size": self.frame_queue.get_queue_size(),
                "dropped_frames": queue_stats.dropped_frames,
                "total_frames": queue_stats.total_frames
            }

        # 添加连接统计
        if self.connection_manager:
            stats["connection"] = {
                "url": self.connection_manager.get_url(),
                "is_active": self.connection_manager.is_active()
            }

        # 添加组件监控统计
        if self.component_monitor:
            stats["monitor"] = self.component_monitor.get_monitor_stats()

        # 添加性能监控统计
        # if self.performance_monitor:
        #     stats["performance"] = self.performance_monitor.get_stream_metrics_summary(self.stream_key)

        return stats