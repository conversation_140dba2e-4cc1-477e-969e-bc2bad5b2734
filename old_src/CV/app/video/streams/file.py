import logging
from typing import Optional, Any
import cv2

from .base import BaseVideoStream

logger = logging.getLogger(__name__)


class FileStream(BaseVideoStream):
    """Simple video file stream for testing and offline playback."""

    def __init__(self, path: Optional[str] = None):
        self.path = path
        self.cap: Optional[cv2.VideoCapture] = None

    async def start(self, stream_id: str) -> bool:
        try:
            # Treat stream_id as a file path when using file protocol
            self.path = self.path or stream_id
            self.cap = cv2.VideoCapture(self.path)
            if not self.cap.isOpened():
                logger.error(f"Failed to open video file: {self.path}")
                return False
            logger.info(f"FileStream started: {self.path}")
            return True
        except Exception as e:
            logger.error(f"FileStream start error: {e}")
            return False

    async def stop(self) -> None:
        try:
            if self.cap:
                self.cap.release()
        except Exception:
            pass
        finally:
            self.cap = None

    async def get_frame(self) -> Optional[Any]:
        if not self.cap:
            return None
        ok, frame = self.cap.read()
        if not ok:
            return None
        return frame

    async def check_stream(self) -> bool:
        return self.cap is not None and self.cap.isOpened()

