<!DOCTYPE html>
<html>
<head>
    <title>流媒体识别 Demo</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <h1>流媒体识别 Demo</h1>
    <a href="/">返回图像识别</a><br><br>
    
    <form id="stream-form">
        <label for="stream-url">RTMP流地址:</label>
        <input type="text" id="stream-url" name="stream_url" required><br><br>
        
        <label for="model-type">选择模型:</label>
        <select id="model-type" name="model_type">
            <option value="cv">CV模型（YOLO等）</option>
            <option value="multimodal">多模态模型（Ollama）</option>
        </select><br><br>
        
        <label for="prompt" id="prompt-label">目标/提示:</label>
        <input type="text" id="prompt" name="prompt" required><br><br>
        
        <button type="submit" id="start-btn">开始检测</button>
        <button type="button" id="stop-btn" disabled>停止检测</button>
    </form>
    
    <div id="stream-display">
        <img id="stream-image" style="max-width: 800px; margin-top: 20px;" />
    </div>
    
    <div id="detection-results">
        <h3>检测结果：</h3>
        <pre id="results-text"></pre>
    </div>

    <script>
        let ws = null;
        const form = document.getElementById('stream-form');
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        const streamImage = document.getElementById('stream-image');
        const resultsText = document.getElementById('results-text');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            try {
                // 启动流处理
                const response = await fetch('/api/stream', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                if (data.error) {
                    alert('错误: ' + data.error);
                    return;
                }

                // 使用返回的client_id建立WebSocket连接
                const client_id = data.client_id;
                const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${wsProtocol}//${window.location.host}/api/ws/stream/${client_id}`;
                console.log('尝试连接WebSocket:', wsUrl);

                ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                };
                
                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    if (data.image) {
                        streamImage.src = `data:image/jpeg;base64,${data.image}`;
                    }
                    if (data.result) {
                        resultsText.textContent = JSON.stringify(data.result, null, 2);
                    }
                };
                
                ws.onclose = (event) => {
                    console.log('WebSocket连接已关闭, code:', event.code, 'reason:', event.reason);
                    resetUI();
                };
                
                ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    alert(`WebSocket连接错误: ${error.message || '未知错误'}`);
                    resetUI();
                };
                
            } catch (error) {
                console.error('Error:', error);
                alert('请求失败: ' + error.message);
            }
        });

        stopBtn.addEventListener('click', async () => {
            try {
                await fetch('/api/stream/stop', {
                    method: 'POST'
                });
                if (ws) {
                    ws.close();
                }
                resetUI();
            } catch (error) {
                console.error('Error:', error);
                alert('停止失败: ' + error.message);
            }
        });

        function resetUI() {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            streamImage.src = '';
            resultsText.textContent = '';
        }
    </script>
</body>
</html>