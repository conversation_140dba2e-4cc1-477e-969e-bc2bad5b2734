<!DOCTYPE html>
<html>
<head>
    <title>WebRTC Stream Test</title>
    <style>
        .container {
            margin: 20px;
            text-align: center;
        }
        #videoElement {
            width: 640px;
            height: 480px;
            border: 1px solid #ccc;
        }
        .controls {
            margin: 10px;
        }
        .status {
            margin: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .connected {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .disconnected {
            background-color: #f2dede;
            color: #a94442;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>WebRTC Stream Test</h2>
        <video id="videoElement" autoplay playsinline></video>
        <div class="controls">
            <input type="text" id="streamUrl" placeholder="WebRTC Stream URL" style="width: 300px;">
            <button id="startBtn">开始</button>
            <button id="stopBtn" disabled>停止</button>
        </div>
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <script>
        const videoElement = document.getElementById('videoElement');
        const streamUrlInput = document.getElementById('streamUrl');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusDiv = document.getElementById('status');
        let peerConnection = null;
        let connectionTimer = null;
        
        async function startStream() {
            try {
                const streamUrl = streamUrlInput.value;
                if (!streamUrl) {
                    alert('请输入流地址');
                    return;
                }
                
                statusDiv.textContent = '正在连接...';
                startBtn.disabled = true;  // 禁用开始按钮
                
                console.log('开始创建PeerConnection...');
                // 创建新的RTCPeerConnection
                peerConnection = new RTCPeerConnection({
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' }
                    ]
                });
                
                // 存储开始时间
                peerConnection.startTime = Date.now();
                
                // 设置连接超时检查
                connectionTimer = setTimeout(() => {
                    console.log('连接已持续30秒');
                    statusDiv.textContent = '连接时间较长，可以继续等待或手动停止';
                    stopBtn.disabled = false;  // 启用停止按钮
                }, 30000);
                
                // 添加视频接收器
                const videoTransceiver = peerConnection.addTransceiver('video', {
                    direction: 'recvonly'
                });
                
                // 处理ICE连接状态
                peerConnection.oniceconnectionstatechange = () => {
                    console.log('ICE连接状态:', peerConnection.iceConnectionState);
                    updateConnectionStatus();
                };
                
                // 处理连接状态
                peerConnection.onconnectionstatechange = () => {
                    console.log('连接状态:', peerConnection.connectionState);
                    updateConnectionStatus();
                };
                
                // 处理远程流
                peerConnection.ontrack = (event) => {
                    console.log('收到远程轨道:', event);
                    if (event.streams && event.streams[0]) {
                        console.log('设置视频源...');
                        videoElement.srcObject = event.streams[0];
                        clearTimeout(connectionTimer);  // 连接成功，清除计时器
                        updateConnectionStatus();
                    }
                };
                
                // 处理ICE候选
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        console.log('新的ICE候选:', event.candidate);
                    }
                };
                
                // 创建offer
                console.log('正在创建offer...');
                const offer = await peerConnection.createOffer();
                console.log('Offer SDP:', offer.sdp);
                
                await peerConnection.setLocalDescription(offer);
                console.log('本地描述设置完成');
                
                // 发送offer到服务器
                console.log('正在发送offer到服务器...');
                const response = await fetch('/api/webrtc/offer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        client_id: 'test-client-' + Date.now(),
                        sdp: offer.sdp
                    })
                });

                if (!response.ok) {
                    throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
                }

                const answer = await response.json();
                console.log('收到服务器answer:', answer);
                
                // 设置远程描述
                console.log('正在设置远程描述...');
                console.log('Answer SDP:', answer.sdp);
                await peerConnection.setRemoteDescription(
                    new RTCSessionDescription({
                        type: 'answer',
                        sdp: answer.sdp
                    })
                );
                console.log('远程描述设置完成');
                
            } catch (error) {
                console.error('流启动错误:', error);
                clearTimeout(connectionTimer);
                updateConnectionStatus('error', error.message);
            }
        }
        
        // 更新连接状态
        function updateConnectionStatus(state, message) {
            if (state === 'error') {
                statusDiv.textContent = '连接失败: ' + message;
                statusDiv.className = 'status disconnected';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                return;
            }
            
            if (!peerConnection) {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                return;
            }
            
            const iceState = peerConnection.iceConnectionState;
            const connState = peerConnection.connectionState;
            const connectionDuration = (Date.now() - peerConnection.startTime) / 1000;
            
            console.log(`更新状态 - ICE: ${iceState}, Connection: ${connState}, Duration: ${connectionDuration}s`);
            
            if (connState === 'connected' || iceState === 'connected') {
                clearTimeout(connectionTimer);
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else if (connState === 'failed' || iceState === 'failed') {
                clearTimeout(connectionTimer);
                statusDiv.textContent = '连接失败';
                statusDiv.className = 'status disconnected';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }
        
        // 停止流
        async function stopStream() {
            try {
                clearTimeout(connectionTimer);  // 清除计时器
                
                if (peerConnection) {
                    const clientId = peerConnection.clientId;
                    
                    // 关闭视频元素
                    if (videoElement.srcObject) {
                        const tracks = videoElement.srcObject.getTracks();
                        tracks.forEach(track => track.stop());
                        videoElement.srcObject = null;
                    }
                    
                    // 关闭连接
                    peerConnection.close();
                    peerConnection = null;
                    
                    // 通知服务器
                    try {
                        const response = await fetch(`/api/webrtc/stop/${clientId}`, {
                            method: 'POST'
                        });
                        
                        if (!response.ok) {
                            console.error('Failed to notify server about stream stop');
                        }
                    } catch (error) {
                        console.error('Error notifying server:', error);
                    }
                    
                    // 刷新页面
                    window.location.reload();
                }
            } catch (error) {
                console.error('停止流失败:', error);
                updateConnectionStatus('error', '停止失败: ' + error.message);
            }
        }
        
        startBtn.onclick = startStream;
        stopBtn.onclick = stopStream;
    </script>
</body>
</html> 