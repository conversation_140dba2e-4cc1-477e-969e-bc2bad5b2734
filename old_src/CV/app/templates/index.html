<!-- index.html（示例） -->
<!DOCTYPE html>
<html>
<head>
    <title>图像识别 Demo</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <h1>图像识别 Demo</h1>
    <a href="/api/video">视频识别</a><br><br>
    <a href="/api/stream">RTMP流识别</a><br><br>

    <form id="upload-form" enctype="multipart/form-data">
        <label for="model-type">选择模型:</label>
        <select id="model-type" name="model_type">
            <option value="cv">CV模型（YOLO等）</option>
            <option value="multimodal">多模态模型（Ollama）</option>
        </select>
        <br><br>

        <label for="file">选择图片:</label>
        <input type="file" id="file" name="file" accept="image/*" required><br><br>
        
        <label for="prompt" id="prompt-label">目标/提示:</label>
        <input type="text" id="prompt" name="prompt" required><br><br>
        
        <button type="submit">上传并检测</button>
    </form>

    <div id="result"></div>
    <div id="image-result">
        <h2>标识后的图像:</h2>
        <img id="annotated-image" src="" alt="Annotated Image" style="max-width: 100%; height: auto;">
    </div>

    <script>
        const form = document.getElementById('upload-form');
        const modelSelect = document.getElementById('model-type');
        const promptLabel = document.getElementById('prompt-label');
        const resultDiv = document.getElementById('result');
        const annotatedImage = document.getElementById('annotated-image');

        // 根据选择的模型类型更新提示文本
        modelSelect.addEventListener('change', () => {
            const isCV = modelSelect.value === 'cv';
            promptLabel.textContent = isCV ? '目标对象:' : '分析提示:';
        });

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData();
            formData.append('file', document.getElementById('file').files[0]);
            formData.append('prompt', document.getElementById('prompt').value);
            formData.append('model_type', modelSelect.value);

            try {
                const response = await fetch('/api/image', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.error) {
                    resultDiv.innerText = `错误: ${result.error}`;
                    return;
                }

                // 显示检测结果
                let resultText = `检测到 ${result.count} 个目标\n\n`;
                if (result.detections && result.detections.length > 0) {
                    resultText += '详细信息:\n';
                    result.detections.forEach((detection, index) => {
                        resultText += `\n项目 ${index + 1}:\n`;
                        if (detection.confidence) {
                            resultText += `置信度: ${(detection.confidence * 100).toFixed(2)}%\n`;
                        }
                        if (detection.description) {
                            resultText += `描述: ${detection.description}\n`;
                        }
                    });
                }
                
                resultDiv.innerText = resultText;

                // 显示标注后的图像
                if (result.annotated_image) {
                    annotatedImage.src = `data:image/jpeg;base64,${result.annotated_image}`;
                    annotatedImage.style.display = 'block';
                } else {
                    annotatedImage.style.display = 'none';
                }
            } catch (error) {
                resultDiv.innerText = '请求失败: ' + error;
            }
        });
    </script>
</body>
</html>
