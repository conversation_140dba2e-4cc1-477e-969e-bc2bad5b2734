import sys
import os
import requests
import base64
import uuid
from datetime import datetime
from tqdm import tqdm
import torch
import cv2
import numpy as np
import logging
from typing import Tuple, Optional, List, Dict
from enum import Enum
import time
import asyncio
from app.utils.config import config
from app.utils.file_manager import file_manager
# 添加 Real-ESRGAN 的路径
realesrgan_path = config.REAL_ESRGAN_PATH
if realesrgan_path not in sys.path:
    sys.path.append(realesrgan_path)

# 按需导入 Real-ESRGAN 依赖，避免未使用时的加载开销
try:
    from basicsr.archs.rrdbnet_arch import RRDBNet
    from realesrgan import RealESRGANer
except Exception:
    RRDBNet = None
    RealESRGANer = None

# 在模块级别初始化logger
logger = logging.getLogger('app.processor.image_processor')


class PreprocessMethod(Enum):
    RESIZE = "resize"
    HISTOGRAM_EQUALIZATION = "histogram_equalization"
    SHARPEN = "sharpen"
    DENOISE = "denoise"
    NORMALIZE = "normalize"
    SUPER_RESOLUTION = "super_resolution"

class ImagePreprocessor:
    # 模型权重下载URL配置
    MODEL_URLS = {
        2: 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth',
        4: 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth'
    }

    def __init__(self, target_size: Tuple[int, int] = (640, 640), model_type: str = "cv"):
        """
        初始化图像处理器
        :param model_type: 'cv' 或 'multimodal'
        """
        self.logger = logger
        self.target_size = target_size
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_type = model_type
        self.model = self._initialize_model()
        self.preprocess = config.PREPROCESS
        # self.temp_dir = config.TEMP_IMAGE_DIR
        # os.makedirs(self.temp_dir, exist_ok=True)

        # 仅在需要时初始化超分辨率模型
        self.upsampler = None
        self.preprocess_methods = config.IMAGE_PREPROCESS_METHODS
        if self.model_type == 'cv':
            logger.info(f"初始化图像处理器，使用模型: {self.model.model.model_name}")
        elif self.model_type == 'multimodal':   
            logger.info(f"初始化图像处理器，使用模型: {self.model.local_model}")

    def _save_image(self, image: np.ndarray, prefix: str, timestamp: Optional[float] = None, 
                   drone_code: Optional[str] = None, site_name: Optional[str] = None) -> Optional[str]:
        """
        保存图像文件
        :param image: 图像数据
        :param prefix: 文件名前缀
        :param timestamp: 时间戳（毫秒），如果为None则使用当前时间
        :param drone_code: 无人机代码（可选）
        :param site_name: 站点名称（可选）
        :return: 保存的文件路径
        """
        try:
            # 如果是归一化后的图像，需要转换回uint8格式
            if image.dtype == np.float32:
                image = (image * 255).astype(np.uint8)
                
            # 如果没有提供时间戳，使用当前时间
            if timestamp is None:
                timestamp = int(time.time() * 1000)
                
            def save_func(file_path: str):
                # 确保文件扩展名是OpenCV支持的格式
                # 检查文件扩展名是否有效
                _, ext = os.path.splitext(file_path)
                if not ext or ext.lower() not in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                    # 如果扩展名无效，强制使用.png格式
                    file_path = os.path.splitext(file_path)[0] + '.png'
                    logger.warning(f"检测到无效的图像扩展名，已更改为: {file_path}")
                
                success = cv2.imwrite(file_path, image)
                if not success:
                    logger.error(f"cv2.imwrite保存图像失败: {file_path}")
                    raise IOError(f"无法保存图像到: {file_path}")
            
            # 如果提供了无人机信息，使用新的保存方法
            if drone_code and site_name:
                return file_manager.save_file_with_drone_info('images', drone_code, site_name, timestamp, '.png', save_func)
            else:
                # 使用旧的保存方法保持兼容性
                return file_manager.save_file('images', prefix, timestamp, '.png', save_func)
            
        except Exception as e:
            logger.error(f"保存图像失败: {str(e)}")
            return None

    def _download_model(self, url: str, save_path: str) -> bool:
        """
        下载模型权重文件
        """
        try:
            print(f"开始下载模型权重文件到: {save_path}")
            response = requests.get(url, stream=True)
            total_size = int(response.headers.get('content-length', 0))
            
            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'wb') as file, tqdm(
                desc="下载进度",
                total=total_size,
                unit='iB',
                unit_scale=True,
                unit_divisor=1024,
            ) as pbar:
                for data in response.iter_content(chunk_size=1024):
                    size = file.write(data)
                    pbar.update(size)
            
            print("模型权重下载完成！")
            return True
        except Exception as e:
            print(f"模型权重下载失败: {str(e)}")
            return False

    def _initialize_sr_model(self):
        """
        初始化超分辨率模型，如果需要则下载权重
        """
        scale = config.SR_MODEL_SCALE
        model_path = config.SR_MODEL_PATH

        # 检查权重文件是否存在，不存在则下载
        if not os.path.exists(model_path):
            if scale not in self.MODEL_URLS:
                raise ValueError(f"不支持的放大倍数: {scale}，仅支持 {list(self.MODEL_URLS.keys())}")
            
            success = self._download_model(self.MODEL_URLS[scale], model_path)
            if not success:
                raise RuntimeError("模型权重下载失败")

        # 根据 scale 选择模型
        if scale == 2:
            model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=2)
        else:  # scale == 4
            model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
        
        # 初始化 RealESRGANer（若依赖缺失则跳过）
        if RealESRGANer is None or RRDBNet is None:
            self.logger.warning("Real-ESRGAN 依赖未加载，跳过超分辨率初始化")
            self.upsampler = None
            return
        self.upsampler = RealESRGANer(
            scale=scale,
            model_path=model_path,
            model=model,
            tile=0,  # 设置为0表示不使用分块处理
            tile_pad=10,
            pre_pad=0,
            half=True if self.device == 'cuda' else False,
            device=self.device
        )

    def _initialize_model(self):
        """根据model_type初始化对应的模型"""
        if self.model_type == 'cv':
            from app.models.yolo_model import YOLOModel
            model_path = config.YOLO_MODEL_PATH
            self.logger.info(f"初始化YOLO模型，路径: {model_path}")
            return YOLOModel(model_path)
        elif self.model_type == 'multimodal':
            from app.models.multimodal_model import MultimodalModel
            return MultimodalModel()
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
    def _resize(self, image: np.ndarray) -> np.ndarray:
        """调整图像尺寸"""
        return cv2.resize(image, self.target_size)
    
    def _histogram_equalization(self, image: np.ndarray) -> np.ndarray:
        """直方图均衡化"""
        img_y_cr_cb = cv2.cvtColor(image, cv2.COLOR_BGR2YCrCb)
        y_channel, cr, cb = cv2.split(img_y_cr_cb)
        y_channel = cv2.equalizeHist(y_channel)
        img_y_cr_cb = cv2.merge((y_channel, cr, cb))
        return cv2.cvtColor(img_y_cr_cb, cv2.COLOR_YCrCb2BGR)
    
    def _sharpen(self, image: np.ndarray) -> np.ndarray:
        """图像锐化"""
        kernel = np.array([[0, -1, 0],
                          [-1, 5,-1],
                          [0, -1, 0]])
        return cv2.filter2D(image, -1, kernel)
    
    def _denoise(self, image: np.ndarray, kernel_size: Tuple[int, int] = (3, 3)) -> np.ndarray:
        """去噪处理"""
        return cv2.GaussianBlur(image, kernel_size, 0)
    
    def _normalize(self, image: np.ndarray) -> np.ndarray:
        """归一化处理"""
        return image.astype(np.float32) / 255.0
    
    def _super_resolution(self, image: np.ndarray) -> np.ndarray:
        """使用RealESRGAN进行超分辨率处理"""
        try:
            # 确保输入图像是BGR格式
            if len(image.shape) == 2:  # 如果是灰度图
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
            
            # 延迟初始化 upsampler
            if self.upsampler is None:
                self._initialize_sr_model()
            if self.upsampler is None:
                return image
            # 使用 RealESRGANer 进行超分辨率处理
            output, _ = self.upsampler.enhance(image, outscale=None)
            
            # 如果需要调整到目标尺寸
            if self.target_size:
                output = cv2.resize(output, self.target_size)
            
            return output
            
        except Exception as e:
            print(f"超分辨率处理失败: {str(e)}")
            return image  # 如果处理失败，返回原图
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        根据配置的方法列表处理图像
        methods: 预处理方法列表，来自环境变量配置
        """
        processed_image = image.copy()
        self._save_image(processed_image, "original", 0)
        # 1. 在此做自适应分析，比如根据亮度决定要不要均衡化
        gray = cv2.cvtColor(processed_image, cv2.COLOR_BGR2GRAY)
        mean_brightness = np.mean(gray)
        # 您也可以在 .env 中放一个 BRIGHTNESS_THRESHOLD=50 或别的值
        brightness_threshold = float(os.getenv('BRIGHTNESS_THRESHOLD', '50'))

        # 2. 根据图像情况动态决定是否执行 "histogram_equalization"
        #    仅在 methods 中包含 histogram_equalization 且亮度过暗时才执行
        if mean_brightness < brightness_threshold and PreprocessMethod.HISTOGRAM_EQUALIZATION.value in self.preprocess_methods:
            processed_image = self._histogram_equalization(processed_image)

        # 3. 其他方法：对于 resize、normalize、sharpen 等
        
        for method in self.preprocess_methods:
            if method == PreprocessMethod.RESIZE.value:
                processed_image = self._resize(processed_image)
            elif method == PreprocessMethod.HISTOGRAM_EQUALIZATION.value:
                processed_image = self._histogram_equalization(processed_image)
            elif method == PreprocessMethod.SHARPEN.value:
                processed_image = self._sharpen(processed_image)
            elif method == PreprocessMethod.DENOISE.value:
                processed_image = self._denoise(processed_image)
            elif method == PreprocessMethod.NORMALIZE.value:
                processed_image = self._normalize(processed_image)
            elif method == PreprocessMethod.SUPER_RESOLUTION.value:
                processed_image = self._super_resolution(processed_image)

        # 保存最终处理结果
        if processed_image.dtype == np.float32:
            save_img = (processed_image * 255).astype(np.uint8)
            self._save_image(save_img, "preprocessed", 0)
        else:
            self._save_image(processed_image, "preprocessed", 0)
                
        return processed_image
    
    def detect(self, image: np.ndarray, target: str, confidence_threshold: float = 0.5) -> Optional[dict]:
        """
        检测单个目标
        :param image: numpy.ndarray 格式的图像数据
        :param target: 目标对象
        :param confidence_threshold: 置信度阈值，默认为0.5
        :return: 检测到目标时返回结果字典，否则返回 None
        """
        # 预处理图像
        if self.preprocess:
            image = self._preprocess_image(image)
            
        # 调用模型进行检测
        result = self.model.detect(image, target, confidence_threshold)
        return result

    async def save_detection_images(self, frame: np.ndarray, detection_result: dict, timestamp: float,
                                   drone_code: Optional[str] = None) -> tuple[str, str]:
        """
        保存检测的原始图片和标注后的图片
        :param frame: 原始图像帧
        :param detection_result: 检测结果
        :param timestamp: 时间戳（毫秒）
        :param drone_code: 无人机代码（可选）
        :return: (原始图片路径, 标注后图片路径)
        """
        try:
            # 如果提供了无人机代码，获取站点名称
            site_name = None
            if drone_code:
                from app.utils.config import config
                site_name = config.get_site_name_by_drone_code(drone_code)
            
            # 生成UUID作为文件前缀
            file_uuid = str(uuid.uuid4())
            
            # 保存原始图片
            def save_original(file_path: str):
                # 确保文件扩展名是OpenCV支持的格式
                _, ext = os.path.splitext(file_path)
                # logger.info(f"保存原始图片: {file_path}, 扩展名: {ext}")
                
                # 检查扩展名是否有效
                if not ext or ext.lower() not in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                    logger.warning(f"检测到无效的图像扩展名: {ext}")
                
                success = cv2.imwrite(file_path, frame)
                if not success:
                    logger.error(f"cv2.imwrite保存图像失败: {file_path}")
                    raise IOError(f"无法保存图像到: {file_path}")
            
            # 使用新的保存方法或旧方法（在后台线程执行，避免阻塞事件循环）
            loop = asyncio.get_running_loop()
            if drone_code and site_name:
                init_image_path = await loop.run_in_executor(
                    None,
                    lambda: file_manager.save_file_with_drone_info(
                        'images', drone_code, site_name, timestamp, '.png', save_original
                    )
                )
            else:
                init_image_path = await loop.run_in_executor(
                    None,
                    lambda: file_manager.save_file(
                        'images', f"{file_uuid}", timestamp, '.png', save_original
                    )
                )

            # 在图片上标注检测结果
            marked_frame = frame.copy()
            logger.info(f"开始标注图片，检测结果包含 {len(detection_result.get('detections', []))} 个目标")
            
            # 检查检测结果是否有效
            if not detection_result or 'detections' not in detection_result or not detection_result['detections']:
                logger.warning("检测结果为空或没有检测到目标，将保存原始图像作为标注图像")
                
            for detection in detection_result.get("detections", []):
                # 这里添加标注逻辑，比如画框、添加标签等
                bbox = None
                
                # 检查不同可能的边界框字段名
                if "bbox" in detection:
                    bbox = detection["bbox"]
                    logger.debug(f"使用bbox字段: {bbox}")
                elif "box" in detection:
                    bbox = detection["box"]
                    logger.debug(f"使用box字段: {bbox}")
                
                if bbox:
                    x1, y1, x2, y2 = bbox
                    logger.info(f"标注目标: 类别={detection.get('target_category', detection.get('target_name', '未知'))}, 边界框={bbox}")
                    cv2.rectangle(marked_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                    
                    # 获取标签和置信度
                    label = detection.get("label", "unknown")
                    confidence = detection.get("confidence", 0.0)
                    
                    # 组合显示文本
                    display_text = f"{label} {confidence:.2f}"
                    
                    # 使用OpenCV绘制文本（带背景）
                    # 获取文本大小
                    text_size, _ = cv2.getTextSize(display_text, cv2.FONT_HERSHEY_SIMPLEX, 0.9, 2)
                    text_width, text_height = text_size
                    
                    # 确定文本位置 - 如果边框靠近上边缘，则将文本放在边框下方
                    margin = 10  # 边缘安全距离
                    if y1 < text_height + margin:
                        # 边框靠近上边缘，文本放在边框下方
                        text_y = int(y2) + text_height + 5
                        rect_y1 = int(y2)
                        rect_y2 = int(y2) + text_height + 10
                        text_y_pos = int(y2) + text_height
                        logger.debug(f"边框靠近上边缘，文本放在下方: y1={y1}, text_height={text_height}")
                    else:
                        # 边框距离上边缘足够，文本放在边框上方
                        text_y = int(y1) - 5
                        rect_y1 = int(y1) - text_height - 10
                        rect_y2 = int(y1)
                        text_y_pos = int(y1) - 5
                        logger.debug(f"边框距离上边缘足够，文本放在上方: y1={y1}, text_height={text_height}")
                    
                    # 绘制文本背景
                    cv2.rectangle(
                        marked_frame,
                        (int(x1), rect_y1),
                        (int(x1) + text_width, rect_y2),
                        (0, 0, 0),
                        -1  # 填充矩形
                    )
                    
                    # 绘制文本
                    cv2.putText(
                        marked_frame,
                        display_text,
                        (int(x1), text_y_pos),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.9,
                        (0, 255, 0),
                        2
                    )
                else:
                    logger.warning(f"检测结果中缺少边界框信息: {detection}")
            
            # 保存标注后的图片
            def save_marked(file_path: str):
                # 确保文件扩展名是OpenCV支持的格式
                _, ext = os.path.splitext(file_path)
                logger.debug(f"保存标注图片: {file_path}, 扩展名: {ext}")
                
                # 检查扩展名是否有效
                if not ext or ext.lower() not in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                    logger.warning(f"检测到无效的图像扩展名: {ext}")
                
                success = cv2.imwrite(file_path, marked_frame)
                if not success:
                    logger.error(f"cv2.imwrite保存图像失败: {file_path}")
                    raise IOError(f"无法保存图像到: {file_path}")
            
            # 使用新的保存方法或旧方法保存标注图片（后台线程执行）
            loop = asyncio.get_running_loop()
            if drone_code and site_name:
                # 为标注图片生成不同的时间戳，避免文件名冲突
                marked_timestamp = timestamp + 1
                res_image_path = await loop.run_in_executor(
                    None,
                    lambda: file_manager.save_file_with_drone_info(
                        'images', f"{drone_code}_predict", site_name, marked_timestamp, '.png', save_marked
                    )
                )
            else:
                res_image_path = await loop.run_in_executor(
                    None,
                    lambda: file_manager.save_file(
                        'images', f"{file_uuid}_predict", timestamp, '.png', save_marked
                    )
                )

            return init_image_path, res_image_path
            
        except Exception as e:
            logger.error(f"保存检测图片失败: {str(e)}")
            return None, None

    async def detect_targets(self, image: np.ndarray, targets: List[Dict],
                           drone_code: Optional[str] = None) -> Optional[dict]:
        """
        检测多个目标
        :param image: numpy.ndarray 格式的图像数据
        :param targets: 目标配置列表，每个目标包含 id, name, keyword 等信息
        :param drone_code: 无人机代码（可选）
        :return: 检测到目标时返回结果字典，否则返回 None
        """
        # 预处理图像
        if self.preprocess:
            image = self._preprocess_image(image)

        # 将CPU密集型推理放到线程池，避免阻塞事件循环
        loop = asyncio.get_running_loop()
        logger.debug(f"开始多目标检测，目标数={len(targets)}, 图像shape={getattr(image, 'shape', None)}")
        result = await loop.run_in_executor(None, lambda: self.model.detect_multiple(image, targets))
        logger.debug(f"多目标检测完成，result_is_none={result is None}, has_dets={bool(result and result.get('detections'))}")

        # 如果检测到目标，保存图片
        if result and result.get("detections"):
            init_path, res_path = await self.save_detection_images(
                image, result, time.time() * 1000, drone_code
            )
            if init_path and res_path:
                result["image_paths"] = {
                    "init": init_path,
                    "res": res_path
                }
        if result is not None:
            logger.info(f"检测结束，检测结果为：{result.get('detections')}")
        return result

    def reload_model(self, model_path=None):
        """
        重新加载模型，用于热更新
        :param model_path: 新模型路径，如果为None则使用配置中的路径
        :return: 是否成功重新加载
        """
        try:
            if self.model_type != 'cv':
                self.logger.warning(f"当前只支持YOLO模型的热更新，当前模型类型: {self.model_type}")
                return False
                
            if model_path is None:
                model_path = config.YOLO_MODEL_PATH
                
            self.logger.info(f"图像处理器开始热更新模型: {model_path}")
            
            # 调用模型的reload_model方法
            success = self.model.reload_model(model_path)
            
            if success:
                self.logger.info(f"图像处理器模型热更新成功: {model_path}")
            else:
                self.logger.warning(f"图像处理器模型热更新失败")
                
            return success
        except Exception as e:
            self.logger.error(f"图像处理器模型热更新异常: {str(e)}")
            return False