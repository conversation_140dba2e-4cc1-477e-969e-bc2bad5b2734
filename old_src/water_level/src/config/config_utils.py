import os
import yaml
import numpy as np
import re
from typing import Dict, Any, Optional, Union, Tuple

class ConfigManager:
    """配置管理器，负责加载和提供配置信息"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        """单例模式确保全局唯一实例"""
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._config = None
        return cls._instance
    
    def load_config(self, config_path: str = "config.yaml") -> Dict[str, Any]:
        """加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict: 包含配置信息的字典
        """
        if self._config is None:
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f)
                
                # 将列表类型的配置转换为numpy数组（如HSV阈值）
                if 'color_threshold' in self._config:
                    if 'hsv_lower' in self._config['color_threshold']:
                        self._config['color_threshold']['hsv_lower'] = np.array(
                            self._config['color_threshold']['hsv_lower']
                        )
                    if 'hsv_upper' in self._config['color_threshold']:
                        self._config['color_threshold']['hsv_upper'] = np.array(
                            self._config['color_threshold']['hsv_upper']
                        )
            except FileNotFoundError:
                print(f"配置文件 {config_path} 未找到，将使用默认配置")
                self._config = self._get_default_config()
            except Exception as e:
                print(f"加载配置文件时出错: {e}，将使用默认配置")
                self._config = self._get_default_config()
        
        return self._config
    
    def get_config(self, section: Optional[str] = None) -> Dict[str, Any]:
        """获取配置信息
        
        Args:
            section: 配置部分名称，如果为None则返回整个配置
            
        Returns:
            Dict: 包含配置信息的字典
        """
        if self._config is None:
            self.load_config()
        
        if section is None:
            return self._config
        
        return self._config.get(section, {})
    
    def update_config_roi(self, roi: Union[Dict[str, Any], Tuple[int, int, int, int]], config_path: str = "config.yaml") -> bool:
        """
        更新配置文件中的默认ROI，保留原始注释和格式
        
        Args:
            roi: 水尺检测结果字典或ROI元组 (x, y, width, height)
            config_path: 配置文件路径
            
        Returns:
            bool: 更新是否成功
        """
        if roi is None:
            return False
            
        # 兼容字典格式的检测结果
        if isinstance(roi, dict):
            roi_tuple = roi.get("updated_roi")
            if roi_tuple is None:
                return False
        else:
            roi_tuple = roi
        
        try:
            # 读取原始配置文件内容
            with open(config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            # 构建ROI字符串
            roi_str = f"[{roi_tuple[0]}, {roi_tuple[1]}, {roi_tuple[2]}, {roi_tuple[3]}]"
            
            # 使用正则表达式匹配并替换ROI值，保留注释
            pattern = r'(default_roi\s*:\s*)\[[^\]]*\](.*)'
            replacement = f"\\1{roi_str}\\2"
            updated_content = re.sub(pattern, replacement, config_content)
            
            # 写回配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            # 更新内存中的配置
            if self._config and 'main' in self._config:
                self._config['main']['default_roi'] = list(roi_tuple)
            
            print(f"成功更新配置文件中的ROI为: {roi_str}，并保留了原始注释")
            return True
        except Exception as e:
            print(f"更新配置文件时出错: {e}")
            return False
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置
        
        Returns:
            Dict: 包含默认配置的字典
        """
        return {
            'image_preprocessing': {
                'blur_kernel_size': 5,
                'morph_kernel_size': 3
            },
            'gradient': {
                'grad_thresh': 25
            },
            'hough': {
                'thresh': 70,
                'min_line_len_factor': 0.3,
                'max_gap': 20
            },
            'color_threshold': {
                'hsv_lower': np.array([35, 40, 40]),
                'hsv_upper': np.array([85, 255, 255]),
                'morph_kernel_size': 5,
                'min_area_factor': 0.01
            },
            'transparency': {
                'alpha_thresh': 50,
                'canny_low': 30,
                'canny_high': 100,
                'blur_ksize': 5,
                'morph_kernel_size': 5,
                'min_area_factor': 0.01,
                'brightness_thresh': 80
            },
            'water_rule': {
                'blur_ksize': 5,
                'white_threshold': 180,
                'dilation_kernel_size': 3,
                'closing_kernel_size': 5,
                'sky_area_factor': 0.1,
                'roi_width_half': 100
            },
            'main': {
                'grid_size': 100,
                'default_method': 'transparency',
                'default_roi': [1700, 1500, 200, 3000],
                'debug': True,
                'brightness_threshold': 150,
                'image_file': 'water_level.jpg'
            }
        }

    def load_config_from_file(self, config_path: str = "config.yaml") -> Dict[str, Any]:
        """
        从文件直接加载配置，不使用缓存
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict: 配置信息
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            print(f"无法从文件加载配置: {e}")
            return {}

# 创建全局实例
config_manager = ConfigManager() 