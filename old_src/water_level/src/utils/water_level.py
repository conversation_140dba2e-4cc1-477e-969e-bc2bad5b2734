import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

import cv2
import numpy as np
from src.config.config_utils import config_manager
from src.utils.image_preprocessing_utils import _preprocess_roi_for_waterline
from src.utils.gradient_utils import waterline_grad_local
from src.utils.hough_utils import waterline_hough_local
from src.utils.color_threshold_utils import waterline_color_threshold_local
from src.utils.transparency_utils import waterline_transparency_local
from src.utils.water_rule_utils import detect_water_rule, visualize_water_rule_detection

def detect_water_surface(
    image: np.ndarray,
    roi: tuple,
    detection_method: str = None,
    brightness_thresh: int = None,
    debug: bool = False
) -> int:
    """
    检测水面位置
    
    Args:
        image: 输入图像
        roi: ROI区域，格式为(x, y, w, h)
        detection_method: 水面检测方法，可选 'gradient', 'hough', 'color_threshold', 'transparency'
        brightness_thresh: 亮度阈值，用于透明度检测方法
        debug: 是否开启调试模式
        
    Returns:
        int: 水面在图像中的y坐标（全局坐标），如果未检测到则返回None
    """
    # 从配置获取参数
    main_config = config_manager.get_config('main')
    if detection_method is None:
        detection_method = main_config.get('default_method', 'transparency')
    if brightness_thresh is None:
        brightness_thresh = main_config.get('brightness_threshold', 120)
    contrast_params = config_manager.get_config('transparency').get('contrast_change', {})
    
    if roi is None:
        if debug:
            print("未提供ROI，无法检测水面。")
        return None

    x_roi, y_roi, w_roi, h_roi = roi
    if not (0 <= y_roi < image.shape[0] and 0 <= y_roi + h_roi <= image.shape[0] and \
            0 <= x_roi < image.shape[1] and 0 <= x_roi + w_roi <= image.shape[1]):
        if debug:
            print(f"错误：ROI {roi} 超出图像边界 {image.shape[:2]}。")
        return None
    if w_roi <= 0 or h_roi <= 0:
        if debug:
            print(f"错误：ROI {roi} 宽度或高度无效。")
        return None
        
    roi_area_bgr = image[y_roi:y_roi+h_roi, x_roi:x_roi+w_roi]
    
    y_final_local = None
    if detection_method == "gradient":
        preprocessed_v_channel = _preprocess_roi_for_waterline(roi_area_bgr)
        y_final_local = waterline_grad_local(preprocessed_v_channel)
        if debug:
            print(f"水面检测 (ROI内局部坐标) - 梯度法_y={y_final_local}")
    elif detection_method == "hough":
        preprocessed_v_channel = _preprocess_roi_for_waterline(roi_area_bgr)
        y_final_local = waterline_hough_local(preprocessed_v_channel)
        if debug:
            print(f"水面检测 (ROI内局部坐标) - 霍夫法_y={y_final_local}")
    elif detection_method == "color_threshold":
        y_final_local = waterline_color_threshold_local(
            roi_area_bgr,  # 使用原始ROI，而不是预处理后的V通道
            debug=debug
        )
        if debug:
            print(f"水面检测 (ROI内局部坐标) - 颜色阈值法_y={y_final_local}")
    elif detection_method == "transparency":
        # 从配置获取透明度方案
        transparency_config = config_manager.get_config('transparency')
        transparency_scheme = transparency_config.get('scheme', 'adaptive_water')
        
        y_final_local = waterline_transparency_local(
            roi_area_bgr,
            brightness_thresh=brightness_thresh,
            transparency_scheme=transparency_scheme,
            contrast_params=contrast_params,
            debug=debug
        )
        if debug:
            print(f"水面检测 (ROI内局部坐标) - 透明度检测法_y={y_final_local}")
    else:
        if debug:
            print(f"错误：未知的检测方法 '{detection_method}'。支持的方法为 'gradient', 'hough', 'color_threshold', 'transparency'。")
        return None

    if y_final_local is not None:
        water_surface_y_global = y_roi + y_final_local
        if debug:
            print(f"使用 {detection_method} 方法检测到水面，位于 Y={water_surface_y_global} (全局坐标)")
        return water_surface_y_global
    else:
        if debug:
            print(f"使用 {detection_method} 方法在ROI内未检测到明确的水面。")
        return None

def calculate_water_depth(
    image: np.ndarray,
    water_roi: tuple,
    rule_roi: tuple = None,
    water_detection_method: str = None,
    brightness_thresh: int = None,
    debug: bool = False
) -> dict:
    """
    计算水深
    
    Args:
        image: 输入图像
        water_roi: 水面ROI区域，格式为(x, y, w, h)
        rule_roi: 水尺ROI区域，格式为(x, y, w, h)，如果为None则自动检测
        water_detection_method: 水面检测方法
        brightness_thresh: 亮度阈值，用于透明度检测方法
        debug: 是否开启调试模式
        
    Returns:
        dict: 包含水深计算结果的字典
    """
    # 从配置获取水尺总长度（米）
    total_ruler_length_m = config_manager.get_config('water_rule').get('length', 5)
    total_ruler_length_cm = total_ruler_length_m * 100  # 转换为厘米
    
    print(f"水尺总长度: {total_ruler_length_m}米 ({total_ruler_length_cm}厘米)")
    
    # 如果未提供水尺ROI，则使用与水面相同的ROI
    if rule_roi is None:
        rule_roi = water_roi
    
    # 1. 检测水尺并获取所有E字母信息
    ruler_detection = detect_water_rule(
        image,
        debug=debug
    )
    
    # 检查是否成功检测到水尺
    if not ruler_detection or not ruler_detection.get("ruler_top") or not ruler_detection.get("all_e_letters"):
        print("未能成功检测到水尺或E字母，无法计算水深。")
        return {
            "success": False,
            "message": "未能成功检测到水尺",
            "water_surface_y": None,
            "ruler_top": None,
            "pixels_per_cm": None,
            "exposed_length_pixels": None,
            "exposed_length_cm": None,
            "water_depth_cm": None
        }
    
    # 获取水尺顶部坐标和所有E字母信息
    ruler_top = ruler_detection["ruler_top"]
    all_e_letters = ruler_detection["all_e_letters"]
    
    # 如果检测到了水尺ROI，更新配置文件中的default_roi
    detected_roi = ruler_detection.get("roi")
    if detected_roi:
        print(f"检测到水尺ROI: {detected_roi}")
        # 更新配置文件中的default_roi
        update_success = config_manager.update_config_roi(detected_roi)
        if update_success:
            print("已成功更新配置文件中的default_roi")
        # 使用检测到的ROI作为水面检测的ROI
        water_roi = detected_roi
        print(f"使用检测到的水尺ROI进行水面检测: {water_roi}")
    else:
        print("未检测到水尺ROI，使用原始water_roi进行水面检测")

    # 2. 检测水面位置
    water_surface_y = detect_water_surface(
        image, 
        water_roi, 
        detection_method=water_detection_method,
        brightness_thresh=brightness_thresh,
        debug=debug
    )
    
    if water_surface_y is None:
        print("未能检测到水面位置，无法计算水深。")
        return {
            "success": False,
            "message": "未能检测到水面位置",
            "water_surface_y": None,
            "ruler_top": None,
            "pixels_per_cm": None,
            "exposed_length_pixels": None,
            "exposed_length_cm": None,
            "water_depth_cm": None
        }
    
    # 3. 计算像素/厘米比率（使用新的权重计算方法）
    from src.utils.water_rule_utils import calculate_pixel_cm_ratio
    
    ratio_results = calculate_pixel_cm_ratio(
        all_e_letters_with_type=all_e_letters,
        water_surface_y=water_surface_y,
        real_e_height_cm=5.0,
        debug=debug
    )
    
    # 选择使用哪种比率计算方法（可以根据配置或需要调整）
    # 这里默认使用综合权重比率，如果为None则使用基础比率
    pixels_per_cm = (ratio_results.get("combined_weighted_ratio") or 
                     ratio_results.get("basic_ratio"))
    
    if pixels_per_cm is None or pixels_per_cm <= 0:
        print("无法计算有效的像素/厘米比率，无法计算水深。")
        return {
            "success": False,
            "message": "无法计算有效的像素/厘米比率",
            "water_surface_y": water_surface_y,
            "ruler_top": ruler_top,
            "pixels_per_cm": None,
            "exposed_length_pixels": None,
            "exposed_length_cm": None,
            "water_depth_cm": None
        }
    
    if debug:
        print(f"比率计算结果:")
        print(f"  基础比率: {ratio_results.get('basic_ratio'):.2f}")
        print(f"  密集度权重比率: {ratio_results.get('density_weighted_ratio'):.2f}")
        print(f"  距离权重比率: {ratio_results.get('distance_weighted_ratio'):.2f}")
        print(f"  综合权重比率: {ratio_results.get('combined_weighted_ratio'):.2f}")
        print(f"  选用的比率: {pixels_per_cm:.2f}")
        print(f"  过滤后字母数量: {ratio_results.get('filtered_letters_count')}")
        print(f"  总字母数量: {ratio_results.get('total_letters_count')}")
    
    # 4. 计算水面到水尺顶部的像素距离（露出水面的水尺长度）
    # 注意：水面的y坐标通常大于水尺顶部的y坐标（坐标系从上到下增加）
    exposed_length_pixels = water_surface_y - ruler_top[1]
    
    # 5. 将像素距离转换为厘米（露出水面的水尺长度，单位厘米）
    exposed_length_cm = exposed_length_pixels / pixels_per_cm if pixels_per_cm > 0 else 0
    
    # 6. 计算实际水深 = 水尺总长度 - 露出水面的长度
    water_depth_cm = total_ruler_length_cm - exposed_length_cm
    
    print(f"水面位置: y={water_surface_y}")
    print(f"水尺顶部位置: y={ruler_top[1]}")
    print(f"像素/厘米比例: {pixels_per_cm:.2f}")
    print(f"露出水面的水尺长度 (像素): {exposed_length_pixels:.2f}")
    print(f"露出水面的水尺长度 (厘米): {exposed_length_cm:.2f}")
    print(f"水深 (厘米): {water_depth_cm:.2f}")
    
    if debug:
        # 在图像上可视化水深测量结果
        vis_img = image.copy()
        
        # 绘制水面线
        cv2.line(vis_img, (0, water_surface_y), (vis_img.shape[1], water_surface_y), (0, 255, 255), 2)
        cv2.putText(vis_img, f"Water Surface (y={water_surface_y})", (10, water_surface_y - 10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # 绘制水尺顶部
        cv2.circle(vis_img, ruler_top, 5, (255, 0, 255), -1)
        cv2.putText(vis_img, f"Ruler Top (y={ruler_top[1]})", (ruler_top[0] + 10, ruler_top[1] - 10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
        
        # 绘制露出水面的水尺长度
        cv2.line(vis_img, (ruler_top[0], ruler_top[1]), (ruler_top[0], water_surface_y), (0, 255, 0), 2)
        cv2.putText(vis_img, f"Exposed: {exposed_length_cm:.2f} cm", (ruler_top[0] + 10, (ruler_top[1] + water_surface_y) // 2 - 20), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 绘制水深说明
        cv2.putText(vis_img, f"Water Depth: {water_depth_cm:.2f} cm", (ruler_top[0] + 10, (ruler_top[1] + water_surface_y) // 2 + 20), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # 保存可视化结果
        cv2.imwrite("debug_water_depth_measurement.jpg", vis_img)
    
    # 返回计算结果
    return {
        "success": True,
        "message": "水深计算成功",
        "water_surface_y": water_surface_y,
        "ruler_top": ruler_top,
        "pixels_per_cm": pixels_per_cm,
        "exposed_length_pixels": exposed_length_pixels,
        "exposed_length_cm": exposed_length_cm,
        "water_depth_cm": water_depth_cm
    }

def process_image_for_water_depth(
    image_path: str = None,
    water_roi: tuple = None,
    rule_roi: tuple = None,
    water_detection_method: str = None,
    brightness_thresh: int = None,
    debug: bool = None
) -> dict:
    """
    处理图像并计算水深
    
    Args:
        image_path: 图像路径
        water_roi: 水面ROI区域，格式为(x, y, w, h)
        rule_roi: 水尺ROI区域，格式为(x, y, w, h)，如果为None则自动检测
        water_detection_method: 水面检测方法
        brightness_thresh: 亮度阈值，用于透明度检测方法
        debug: 是否开启调试模式
        
    Returns:
        dict: 包含水深计算结果的字典
    """
    # 从配置获取参数
    main_config = config_manager.get_config('main')
    if image_path is None:
        image_path = main_config.get('image_file', 'water_level.jpg')
    if water_roi is None:
        water_roi = tuple(main_config.get('default_roi', [1700, 1500, 200, 3000]))
    if water_detection_method is None:
        water_detection_method = main_config.get('default_method', 'transparency')
    if brightness_thresh is None:
        brightness_thresh = main_config.get('brightness_threshold', 150)
    if debug is None:
        debug = main_config.get('debug', True)
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        return {
            "success": False,
            "message": f"无法读取图像: {image_path}"
        }
    
    # 计算水深
    result = calculate_water_depth(
        image,
        water_roi,
        rule_roi,
        water_detection_method,
        brightness_thresh,
        debug
    )
    
    # 生成输出图像
    if result["success"]:
        output_image = image.copy()
        
        # 绘制水面ROI
        x, y, w, h = water_roi
        cv2.rectangle(output_image, (x, y), (x+w, y+h), (255, 0, 0), 2)
        cv2.putText(output_image, "Water ROI", (x, y-10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        
        # 绘制水面线
        water_surface_y = result["water_surface_y"]
        cv2.line(output_image, (0, water_surface_y), (output_image.shape[1], water_surface_y), (0, 255, 255), 2)
        cv2.putText(output_image, "Water Surface", (10, water_surface_y - 10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # 绘制水尺顶部
        ruler_top = result["ruler_top"]
        cv2.circle(output_image, ruler_top, 5, (255, 0, 255), -1)
        cv2.putText(output_image, "Ruler Top", (ruler_top[0] + 10, ruler_top[1] - 10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
        
        # 绘制露出水面的水尺长度
        cv2.line(output_image, (ruler_top[0], ruler_top[1]), (ruler_top[0], water_surface_y), (0, 255, 0), 2)
        cv2.putText(output_image, f"Exposed: {result['exposed_length_cm']:.2f} cm", 
                    (ruler_top[0] + 10, (ruler_top[1] + water_surface_y) // 2 - 20), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 绘制水深说明
        cv2.putText(output_image, f"Water Depth: {result['water_depth_cm']:.2f} cm", 
                    (ruler_top[0] + 10, (ruler_top[1] + water_surface_y) // 2 + 20), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # 保存输出图像
        output_path = f"output_water_depth_{water_detection_method}.jpg"
        cv2.imwrite(output_path, output_image)
        print(f"水深测量结果已保存为 {output_path}")
    
    return result

if __name__ == "__main__":
    # 获取配置
    config = config_manager.get_config()
    main_config = config.get('main', {})
    
    # 从配置中获取参数
    image_file = main_config.get('image_file', 'water_level.jpg')
    water_roi = tuple(main_config.get('default_roi', [1700, 1500, 200, 3000]))
    water_detection_method = main_config.get('default_method', 'transparency')
    brightness_threshold = main_config.get('brightness_threshold', 150)
    debug_mode = main_config.get('debug', True)

    if not image_file or not water_roi:
        print("请确保 image_file 和 water_roi 已正确设置。")
    else:
        result = process_image_for_water_depth(
            image_file, 
            water_roi=water_roi, 
            water_detection_method=water_detection_method, 
            brightness_thresh=brightness_threshold,
            debug=debug_mode
        )
        
        if result["success"]:
            print(f"水深计算结果: {result['water_depth_cm']:.2f} 厘米")
        else:
            print(f"水深计算失败: {result['message']}")
