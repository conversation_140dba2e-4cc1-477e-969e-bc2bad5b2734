import cv2
import numpy as np
import matplotlib.pyplot as plt
import csv
from src.config.config_utils import config_manager

# 从配置获取默认参数
def get_default_grad_thresh():
    """从配置文件获取梯度阈值"""
    config = config_manager.get_config('gradient')
    return config.get('grad_thresh', 25)

# 梯度法相关常量
DEFAULT_GRAD_THRESH = get_default_grad_thresh()

def _visualize_gradient_results(preprocessed_channel_for_vis: np.ndarray, col_pixels: np.ndarray, gradient_values: np.ndarray, detected_y_local: int | None, mid_col_idx: int, roi_img_filename: str, profile_plot_filename: str, profile_plot_title: str):
    # ... (内容来自 test2.py 第15-88行)
    if detected_y_local is not None:
        vis_image = cv2.cvtColor(preprocessed_channel_for_vis, cv2.COLOR_GRAY2BGR)
        cv2.line(vis_image, (0, detected_y_local), (vis_image.shape[1] - 1, detected_y_local), (0, 0, 255), 1)
        cv2.line(vis_image, (mid_col_idx, 0), (mid_col_idx, vis_image.shape[0] -1), (255,0,0),1)
        cv2.imwrite(roi_img_filename, vis_image)
        print(f"梯度法检测可视化图像已保存到 {roi_img_filename} (检测到的y={detected_y_local} at X={mid_col_idx})")
    else:
        vis_image_no_detection = cv2.cvtColor(preprocessed_channel_for_vis, cv2.COLOR_GRAY2BGR)
        cv2.line(vis_image_no_detection, (mid_col_idx, 0), (mid_col_idx, vis_image_no_detection.shape[0] -1), (255,0,0),1)
        cv2.imwrite(roi_img_filename, vis_image_no_detection)
        print(f"梯度法在X={mid_col_idx}未检测到水线，预处理ROI已保存为 {roi_img_filename}")

    if plt and col_pixels is not None and gradient_values is not None:
        try:
            plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP']
            plt.rcParams['axes.unicode_minus'] = False
            fig, ax1 = plt.subplots(figsize=(10, 6))
            color = 'tab:blue'
            ax1.set_xlabel('Y 坐标 (像素)')
            ax1.set_ylabel('像素强度 (V通道)', color=color)
            ax1.plot(col_pixels.astype(np.float32), color=color, label='像素强度')
            ax1.tick_params(axis='y', labelcolor=color)
            if detected_y_local is not None:
                ax1.axvline(x=detected_y_local, color='r', linestyle='--', label=f'检测到的水线 Y={detected_y_local}')
            ax2 = ax1.twinx()
            color = 'tab:green'
            ax2.set_ylabel('梯度绝对值', color=color)
            ax2.plot(gradient_values, color=color, alpha=0.7, label='梯度绝对值')
            ax2.tick_params(axis='y', labelcolor=color)
            lines, labels = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax2.legend(lines + lines2, labels + labels2, loc='upper right')
            fig.tight_layout()
            plt.title(profile_plot_title)
            plt.savefig(profile_plot_filename)
            plt.close(fig)
            print(f"梯度剖面图已保存到 {profile_plot_filename}")
        except Exception as e:
            print(f"为列 X={mid_col_idx} 生成梯度剖面图时发生错误: {e}")
    elif plt and col_pixels is not None and gradient_values is None:
        print(f"列 X={mid_col_idx} 的梯度数组为空或无效，无法绘制梯度剖面图。")
    elif plt and col_pixels is None:
        print(f"列 X={mid_col_idx} 的像素数据为空，无法绘制梯度剖面图。")

def _visualize_top_n_gradients_on_roi(
    preprocessed_channel_for_vis: np.ndarray,
    primary_detected_y_local: int | None,
    mid_col_idx: int,
    top_n_grad_indices: list[int] | None
):
    # ... (内容来自 test2.py 第90-127行)
    vis_image = cv2.cvtColor(preprocessed_channel_for_vis, cv2.COLOR_GRAY2BGR)
    cv2.line(vis_image, (mid_col_idx, 0), (mid_col_idx, vis_image.shape[0] -1), (255,0,0),1)
    if top_n_grad_indices:
        for y_idx in top_n_grad_indices:
            if primary_detected_y_local is not None and y_idx == primary_detected_y_local:
                cv2.line(vis_image, (0, y_idx), (vis_image.shape[1] - 1, y_idx), (0, 0, 255), 2)
            else:
                cv2.line(vis_image, (0, y_idx), (vis_image.shape[1] - 1, y_idx), (0, 255, 255), 1)
    elif primary_detected_y_local is not None:
         cv2.line(vis_image, (0, primary_detected_y_local), (vis_image.shape[1] - 1, primary_detected_y_local), (0, 0, 255), 2)
    output_filename = "debug_top_n_gradients_on_roi.jpg"
    cv2.imwrite(output_filename, vis_image)
    if top_n_grad_indices or primary_detected_y_local is not None:
        print(f"Top N梯度可视化图像已保存到 {output_filename} (主线y={primary_detected_y_local}, TopN数量={len(top_n_grad_indices) if top_n_grad_indices else 0})")
    else:
        print(f"Top N梯度可视化: 未检测到显著梯度点，图像已保存到 {output_filename}")

def waterline_grad_local(preprocessed_channel: np.ndarray, grad_thresh: int = None) -> int | None:
    # 从配置获取梯度阈值
    if grad_thresh is None:
        grad_thresh = DEFAULT_GRAD_THRESH
    
    roi_h, roi_w = preprocessed_channel.shape
    y_grad_primary_center = None
    all_columns_gradients_data = {}
    max_grad_len = 0
    if roi_w == 0:
        print("预处理通道宽度为0，无法进行梯度分析。")
        _visualize_top_n_gradients_on_roi(preprocessed_channel.copy(), None, 0, None)
        return None
    num_cols_to_analyze = min(5, roi_w)
    if num_cols_to_analyze == 0:
        print("ROI宽度不足以选择任何列进行分析。")
        _visualize_top_n_gradients_on_roi(preprocessed_channel.copy(), None, 0, None)
        return None
    if num_cols_to_analyze == 1:
        col_indices = [roi_w // 2]
    else:
        col_indices = np.linspace(0, roi_w - 1, num_cols_to_analyze, dtype=int)
    center_roi_x_idx = roi_w // 2
    closest_col_idx_to_center = col_indices[np.argmin(np.abs(col_indices - center_roi_x_idx))]
    print(f"将在以下X列索引处分析梯度: {col_indices}")
    for i, col_x_idx in enumerate(col_indices):
        col_pixels = preprocessed_channel[:, col_x_idx].astype(np.int16)
        current_y_grad = None
        current_grad_values = None
        if len(col_pixels) < 2:
            print(f"列 X={col_x_idx} 的像素数据不足 (长度 {len(col_pixels)})，跳过梯度计算。")
            all_columns_gradients_data[col_x_idx] = []
            _visualize_gradient_results(
                preprocessed_channel.copy(),
                col_pixels if len(col_pixels) > 0 else None,
                None, None, col_x_idx,
                roi_img_filename=f"debug_grad_roi_col_{col_x_idx}.jpg",
                profile_plot_filename=f"debug_grad_profile_col_{col_x_idx}.png",
                profile_plot_title=f"列 X={col_x_idx} 像素与梯度"
            )
            continue
        grad = np.abs(np.diff(col_pixels))
        current_grad_values = grad
        if grad.size > 0:
            max_grad_len = max(max_grad_len, len(grad))
            all_columns_gradients_data[col_x_idx] = grad.tolist()
            if grad.max() >= grad_thresh:
                current_y_grad = int(np.argmax(grad))
                print(f"列 X={col_x_idx}: 检测到满足阈值的水线y={current_y_grad} (局部, 梯度={grad.max():.2f})")
            else:
                print(f"列 X={col_x_idx}: 所有梯度值均小于阈值 {grad_thresh}。")
        else:
            print(f"列 X={col_x_idx}: 梯度数组为空。")
            all_columns_gradients_data[col_x_idx] = []
        _visualize_gradient_results(
            preprocessed_channel.copy(),
            col_pixels,
            current_grad_values if current_grad_values is not None and current_grad_values.size > 0 else None,
            current_y_grad,
            col_x_idx,
            roi_img_filename=f"debug_grad_roi_col_{col_x_idx}.jpg",
            profile_plot_filename=f"debug_grad_profile_col_{col_x_idx}.png",
            profile_plot_title=f"列 X={col_x_idx} 像素与梯度 (检测y={current_y_grad})"
        )
        if col_x_idx == closest_col_idx_to_center:
            y_grad_primary_center = current_y_grad
            top_n_indices_for_center_col_viz = None
            if current_grad_values is not None and current_grad_values.size > 0:
                sorted_center_col_grad_indices = np.argsort(current_grad_values)[::-1]
                top_n_candidates_center = []
                for y_idx_grad_array in sorted_center_col_grad_indices:
                    top_n_candidates_center.append(y_idx_grad_array)
                    if len(top_n_candidates_center) >= 20:
                        break
                if top_n_candidates_center:
                    top_n_indices_for_center_col_viz = top_n_candidates_center
            _visualize_top_n_gradients_on_roi(
                preprocessed_channel.copy(),
                y_grad_primary_center,
                closest_col_idx_to_center,
                top_n_indices_for_center_col_viz
            )
            if not top_n_indices_for_center_col_viz and y_grad_primary_center is None:
                 print(f"中心列 (X={closest_col_idx_to_center}) 未检测到主水线或TopN梯度，TopN可视化可能为空。")
    if all_columns_gradients_data and max_grad_len > 0 :
        csv_filename = "debug_gradient_data_all_cols.csv"
        header = ['y_coordinate'] + [f'gradient_col_{x_idx}' for x_idx in col_indices]
        rows = []
        for y in range(max_grad_len):
            row_data = [y]
            for x_idx in col_indices:
                grad_list_for_col = all_columns_gradients_data.get(x_idx, [])
                if y < len(grad_list_for_col):
                    row_data.append(grad_list_for_col[y])
                else:
                    row_data.append(np.nan)
            rows.append(row_data)
        try:
            with open(csv_filename, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(header)
                writer.writerows(rows)
            print(f"所有选定列的梯度数据已保存到 {csv_filename}")
        except IOError as e:
            print(f"无法写入CSV文件 {csv_filename}: {e}")
    elif not all_columns_gradients_data:
        print("没有收集到任何列的梯度数据，不生成CSV。")
    elif max_grad_len == 0:
        print("所有列的梯度数据均为空（例如，列太短或梯度计算失败），不生成CSV。")
    return y_grad_primary_center 