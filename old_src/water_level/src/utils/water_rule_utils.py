import cv2
import numpy as np
import csv
from src.config.config_utils import config_manager

# 从配置获取默认参数
def get_default_water_rule_params():
    """从配置文件获取水尺检测参数"""
    config = config_manager.get_config('water_rule') or {}
    return {
        'blur_ksize': config.get('blur_ksize', 5),
        'white_threshold': config.get('white_threshold', 180),  # 白色阈值
        'dilation_kernel_size': config.get('dilation_kernel_size', 3),  # 膨胀操作核大小
        'closing_kernel_size': config.get('closing_kernel_size', 5),  # 闭运算核大小
        'sky_area_factor': config.get('sky_area_factor', 0.1),  # 天空区域面积因子（相对于图像面积）
        'roi_width_half': config.get('roi_width_half', 100),  # ROI宽度的一半（像素）
    }

# 默认参数值
params = get_default_water_rule_params()
DEFAULT_BLUR_KSIZE = params['blur_ksize']
DEFAULT_WHITE_THRESHOLD = params['white_threshold']
DEFAULT_DILATION_KERNEL_SIZE = params['dilation_kernel_size']
DEFAULT_CLOSING_KERNEL_SIZE = params['closing_kernel_size']
DEFAULT_SKY_AREA_FACTOR = params['sky_area_factor']
DEFAULT_ROI_WIDTH_HALF = params['roi_width_half']

def calculate_pixel_cm_ratio(
    all_e_letters_with_type: list,
    water_surface_y: int = None,
    real_e_height_cm: float = 5.0,
    debug: bool = False
) -> dict:
    """
    计算像素/厘米比率，支持多种权重策略
    
    Args:
        all_e_letters_with_type: 所有检测到的E字母列表，格式为[(x, y, w, h, type), ...]
        water_surface_y: 水面Y坐标，用于过滤低于水面的字母
        real_e_height_cm: E字母的真实高度（厘米）
        debug: 是否开启调试模式
        
    Returns:
        dict: 包含不同策略计算的比率结果
    """
    if not all_e_letters_with_type:
        return {
            "basic_ratio": None,
            "density_weighted_ratio": None,
            "distance_weighted_ratio": None,
            "combined_weighted_ratio": None,
            "filtered_letters_count": 0,
            "total_letters_count": len(all_e_letters_with_type)
        }
    
    # 1. 过滤掉低于水面的字母（如果提供了水面Y坐标）
    filtered_letters = all_e_letters_with_type
    if water_surface_y is not None:
        filtered_letters = [
            (x, y, w, h, etype) for x, y, w, h, etype in all_e_letters_with_type
            if y < water_surface_y  # y坐标小于水面Y坐标的字母（在水面上方）
        ]
        if debug:
            print(f"水面Y坐标: {water_surface_y}")
            print(f"过滤前字母数量: {len(all_e_letters_with_type)}")
            print(f"过滤后字母数量: {len(filtered_letters)}")
    
    if not filtered_letters:
        return {
            "basic_ratio": None,
            "density_weighted_ratio": None,
            "distance_weighted_ratio": None,
            "combined_weighted_ratio": None,
            "filtered_letters_count": 0,
            "total_letters_count": len(all_e_letters_with_type)
        }
    
    # 2. 基础比率计算（当前的计算流程）
    ratios = [h / real_e_height_cm for _, _, _, h, _ in filtered_letters]
    mean_ratio = np.mean(ratios)
    std_ratio = np.std(ratios)
    filtered_ratios = [r for r in ratios if abs(r - mean_ratio) <= std_ratio]
    basic_ratio = np.mean(filtered_ratios) if filtered_ratios else mean_ratio
    
    # 3. 基于密集度的权重计算
    density_weighted_ratio = _calculate_density_weighted_ratio(
        filtered_letters, real_e_height_cm, debug
    )
    
    # 4. 基于距离水面远近的权重计算
    distance_weighted_ratio = _calculate_distance_weighted_ratio(
        filtered_letters, water_surface_y, real_e_height_cm, debug
    )
    
    # 5. 综合权重计算（同时考虑密集度和距离）
    combined_weighted_ratio = _calculate_combined_weighted_ratio(
        filtered_letters, water_surface_y, real_e_height_cm, debug
    )
    
    if debug:
        print(f"基础比率: {basic_ratio:.2f}")
        print(f"密集度权重比率: {density_weighted_ratio:.2f}")
        print(f"距离权重比率: {distance_weighted_ratio:.2f}")
        print(f"综合权重比率: {combined_weighted_ratio:.2f}")
    
    return {
        "basic_ratio": basic_ratio,
        "density_weighted_ratio": density_weighted_ratio,
        "distance_weighted_ratio": distance_weighted_ratio,
        "combined_weighted_ratio": combined_weighted_ratio,
        "filtered_letters_count": len(filtered_letters),
        "total_letters_count": len(all_e_letters_with_type)
    }

def _calculate_density_weighted_ratio(
    letters: list,
    real_e_height_cm: float,
    debug: bool = False
) -> float:
    """
    基于Y区间密集度计算权重比率
    字母在某一个Y区间里越多，权重越低；越少则权重越高
    """
    if not letters:
        return None
    
    # 将字母按Y坐标分组到区间中（每个区间高度为50像素）
    interval_height = 50
    y_coords = [y for _, y, _, _, _ in letters]
    min_y, max_y = min(y_coords), max(y_coords)
    
    # 创建区间字典
    intervals = {}
    for x, y, w, h, etype in letters:
        interval_key = y // interval_height
        if interval_key not in intervals:
            intervals[interval_key] = []
        intervals[interval_key].append((x, y, w, h, etype))
    
    # 计算每个区间的密集度权重
    weighted_ratios = []
    weights = []
    
    for interval_key, interval_letters in intervals.items():
        count = len(interval_letters)
        # 密集度权重：字母越多权重越低
        density_weight = 1.0 / count if count > 0 else 1.0
        
        # 计算该区间内字母的比率
        interval_ratios = [h / real_e_height_cm for _, _, _, h, _ in interval_letters]
        interval_mean_ratio = np.mean(interval_ratios)
        
        weighted_ratios.append(interval_mean_ratio * density_weight)
        weights.append(density_weight)
        
        if debug:
            print(f"Y区间 {interval_key*interval_height}-{(interval_key+1)*interval_height}: "
                  f"字母数={count}, 密集度权重={density_weight:.3f}, 平均比率={interval_mean_ratio:.2f}")
    
    # 计算加权平均
    total_weight = sum(weights)
    if total_weight > 0:
        return sum(weighted_ratios) / total_weight
    else:
        return np.mean([h / real_e_height_cm for _, _, _, h, _ in letters])

def _calculate_distance_weighted_ratio(
    letters: list,
    water_surface_y: int,
    real_e_height_cm: float,
    debug: bool = False
) -> float:
    """
    基于距离水面远近计算权重比率
    距离水面越远权重越低，越近权重越高
    """
    if not letters or water_surface_y is None:
        # 如果没有水面信息，返回基础平均值
        return np.mean([h / real_e_height_cm for _, _, _, h, _ in letters])
    
    weighted_ratios = []
    weights = []
    
    for x, y, w, h, etype in letters:
        # 计算距离水面的距离（像素）
        distance_to_water = abs(water_surface_y - y)
        
        # 距离权重：距离越远权重越低，使用指数衰减
        # 使用 exp(-distance/scale) 形式，scale=200像素
        distance_weight = np.exp(-distance_to_water / 200.0)
        
        ratio = h / real_e_height_cm
        weighted_ratios.append(ratio * distance_weight)
        weights.append(distance_weight)
        
        if debug:
            print(f"字母位置Y={y}, 距水面距离={distance_to_water}, "
                  f"距离权重={distance_weight:.3f}, 比率={ratio:.2f}")
    
    # 计算加权平均
    total_weight = sum(weights)
    if total_weight > 0:
        return sum(weighted_ratios) / total_weight
    else:
        return np.mean([h / real_e_height_cm for _, _, _, h, _ in letters])

def _calculate_combined_weighted_ratio(
    letters: list,
    water_surface_y: int,
    real_e_height_cm: float,
    debug: bool = False
) -> float:
    """
    综合考虑密集度和距离的权重比率计算
    """
    if not letters:
        return None
    
    # 将字母按Y坐标分组到区间中
    interval_height = 50
    intervals = {}
    for x, y, w, h, etype in letters:
        interval_key = y // interval_height
        if interval_key not in intervals:
            intervals[interval_key] = []
        intervals[interval_key].append((x, y, w, h, etype))
    
    weighted_ratios = []
    weights = []
    
    for interval_key, interval_letters in intervals.items():
        count = len(interval_letters)
        # 密集度权重
        density_weight = 1.0 / count if count > 0 else 1.0
        
        # 计算该区间的平均Y坐标
        avg_y = np.mean([y for _, y, _, _, _ in interval_letters])
        
        # 距离权重（如果有水面信息）
        if water_surface_y is not None:
            distance_to_water = abs(water_surface_y - avg_y)
            distance_weight = np.exp(-distance_to_water / 200.0)
        else:
            distance_weight = 1.0
        
        # 综合权重：密集度权重 × 距离权重
        combined_weight = density_weight * distance_weight
        
        # 计算该区间内字母的比率
        interval_ratios = [h / real_e_height_cm for _, _, _, h, _ in interval_letters]
        interval_mean_ratio = np.mean(interval_ratios)
        
        weighted_ratios.append(interval_mean_ratio * combined_weight)
        weights.append(combined_weight)
        
        if debug:
            print(f"Y区间 {interval_key*interval_height}-{(interval_key+1)*interval_height}: "
                  f"字母数={count}, 平均Y={avg_y:.1f}, "
                  f"密集度权重={density_weight:.3f}, 距离权重={distance_weight:.3f}, "
                  f"综合权重={combined_weight:.3f}, 平均比率={interval_mean_ratio:.2f}")
    
    # 计算加权平均
    total_weight = sum(weights)
    if total_weight > 0:
        return sum(weighted_ratios) / total_weight
    else:
        return np.mean([h / real_e_height_cm for _, _, _, h, _ in letters])

def detect_water_rule(
    image: np.ndarray,
    roi_width_half: int = None,
    debug: bool = False
) -> dict:
    """
    通过直接检测"E"形字母来定位水尺。

    Args:
        image: 输入的BGR格式图像。
        roi_width_half: ROI宽度的一半（像素），用于确定水尺区域的宽度。
        debug: 是否开启调试模式。

    Returns:
        dict: 包含水尺ROI、标尺顶端坐标、所有E字母信息的结果字典。
              如果未找到"E"字母，则返回包含None值的字典。
    """
    # 使用默认参数
    if roi_width_half is None:
        roi_width_half = DEFAULT_ROI_WIDTH_HALF
    
    # 获取图像尺寸
    height, width = image.shape[:2]
    
    print(f"图像尺寸={width}x{height}")
    print(f"ROI宽度半径={roi_width_half}")
    
    # ===== 直接基于E字母检测定位水尺 =====
    
    try:
        from src.utils.shape_recognition_utils import detect_e_shapes
        
        # 在整个图像中统一检测所有E形字母
        all_e_letters_with_type, centerline_data = detect_e_shapes(
            image=image,
            min_area=100,
            max_area=10000,
            aspect_ratio_range=(0.5, 2.0),
            solidity_range=(0.4, 0.9),
            horizontal_segments_count=3,
            roi=None,
            debug=debug
        )
        
        # 如果没有检测到任何E字母，则返回失败
        if not all_e_letters_with_type:
            print("在图像中未找到正向或反向的E字母。")
            return {
                "roi": None,
                "ruler_top": None,
                "all_e_letters": [],
                "e_letters_count": 0,
                "reversed_e_letters_count": 0,
            }

        # 按y坐标排序，找到最高的E
        all_e_letters_with_type.sort(key=lambda e: e[1])
        top_e = all_e_letters_with_type[0]
        top_x, top_y, top_w, top_h, _ = top_e
        
        # 计算所有E字母中心x坐标的平均值，作为水尺的中轴线
        e_centers_x = [x + w / 2 for x, y, w, h, _ in all_e_letters_with_type]
        avg_center_x = np.mean(e_centers_x)
        
        # 根据E字母的平均中心和最高位置定义水尺ROI
        roi_x = max(0, int(avg_center_x - roi_width_half))
        roi_w = min(width - roi_x, roi_width_half * 2)
        roi_y = 0 # top_y
        roi_h = height # - top_y
        
        final_roi = (roi_x, roi_y, roi_w, roi_h)
        
        # 根据配置选择水尺顶部检测方法
        water_rule_config = config_manager.get_config('water_rule') or {}
        top_detection_method = water_rule_config.get('top_detection_method', 'e_letter')
        
        if top_detection_method == 'e_letter':
            # 传统方法：使用最高的E字母位置
            ruler_top = (top_x + top_w//2, top_y)
        else:
            # 使用其他检测方法
            from src.utils.shape_recognition_utils import (
                detect_ruler_top_by_edge_detection,
                detect_ruler_top_by_texture_similarity, 
                detect_ruler_top_by_line_detection,
                detect_ruler_top_by_pattern_matching,
                detect_ruler_top_multi_method
            )
            
            centerline_x = int(avg_center_x)  # 中轴线位置
            
            if top_detection_method == 'edge_detection':
                result = detect_ruler_top_by_edge_detection(image, final_roi, centerline_data, debug=debug)
            elif top_detection_method == 'texture_similarity':
                result = detect_ruler_top_by_texture_similarity(image, centerline_x, final_roi, centerline_data, debug=debug)
            elif top_detection_method == 'line_detection':
                result = detect_ruler_top_by_line_detection(image, final_roi, centerline_data, debug=debug)
            elif top_detection_method == 'pattern_matching':
                result = detect_ruler_top_by_pattern_matching(image, centerline_x, final_roi, centerline_data, debug=debug)
            elif top_detection_method == 'multi_method':
                multi_result = detect_ruler_top_multi_method(image, centerline_x, final_roi, centerline_data, debug=debug)
                result = multi_result.get('final_result')
            else:
                print(f"未知的顶部检测方法: {top_detection_method}，使用E字母方法")
                result = None
            
            # 如果检测失败，回退到E字母方法
            if result is None:
                print("顶部检测方法失败，回退到E字母方法")
                ruler_top = (top_x + top_w//2, top_y)
            else:
                ruler_top = result
        
        e_letters_count = sum(1 for *_, type in all_e_letters_with_type if type == 'E')
        reversed_e_letters_count = len(all_e_letters_with_type) - e_letters_count

        if debug:
            roi_vis = image.copy()
            x, y, w, h = final_roi
            cv2.rectangle(roi_vis, (x, y), (x+w, y+h), (0, 255, 255), 2)
            cv2.putText(roi_vis, f"Final ROI ({w}x{h})", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            
            for ex, ey, ew, eh, etype in all_e_letters_with_type:
                color = (0, 255, 0) if etype == 'E' else (0, 0, 255)
                cv2.rectangle(roi_vis, (ex, ey), (ex+ew, ey+eh), color, 2)
            
            cv2.circle(roi_vis, ruler_top, 5, (255, 0, 255), -1)
            cv2.putText(roi_vis, "Ruler Top", (ruler_top[0], ruler_top[1]-20), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
            
            cv2.imwrite("debug_final_roi_with_e_letters.jpg", roi_vis)
            print(f"最终ROI: {final_roi}")
            print(f"检测到的正向E数量: {e_letters_count}")
            print(f"检测到的反向E数量: {reversed_e_letters_count}")
            print(f"标尺顶端坐标: {ruler_top}")

        return {
            "roi": final_roi,
            "ruler_top": ruler_top,
            "all_e_letters": all_e_letters_with_type,
            "e_letters_count": e_letters_count,
            "reversed_e_letters_count": reversed_e_letters_count
        }
        
    except ImportError as e:
        print(f"无法导入形状识别工具: {e}")
    except Exception as e:
        print(f"E字母检测过程中出错: {e}")
    
    # 如果检测失败，返回空结果
    return {
        "roi": None,
        "ruler_top": None,
        "all_e_letters": [],
        "e_letters_count": 0,
        "reversed_e_letters_count": 0
    }

def visualize_water_rule_detection(
    image: np.ndarray,
    detection_result: dict,
    pixel_cm_ratio: float = None,
    debug: bool = False
) -> np.ndarray:
    """
    可视化水尺检测结果
    
    Args:
        image: 原始图像
        detection_result: detect_water_rule返回的结果字典
        pixel_cm_ratio: 像素/厘米比率（可选）
        debug: 是否保存调试图像
        
    Returns:
        np.ndarray: 标注了水尺位置的图像
    """
    result_img = image.copy()
    
    # 处理字典格式的检测结果
    if isinstance(detection_result, dict):
        final_roi = detection_result.get("roi")
        ruler_top = detection_result.get("ruler_top")
        all_e_letters = detection_result.get("all_e_letters", [])
        e_letters_count = detection_result.get("e_letters_count", 0)
        reversed_e_letters_count = detection_result.get("reversed_e_letters_count", 0)
        
        if final_roi:
            x, y, w, h = final_roi
            # 绘制水尺矩形区域
            cv2.rectangle(result_img, (x, y), (x+w, y+h), (0, 0, 255), 3)
            
            # 添加标签
            cv2.putText(
                result_img, 
                f"Water Rule ROI ({w}x{h})", 
                (x, y-10), 
                cv2.FONT_HERSHEY_SIMPLEX, 
                0.7, 
                (0, 0, 255), 
                2
            )
            
            # 显示E字母数量信息
            cv2.putText(
                result_img, 
                f"E Letters: {e_letters_count}, Reversed: {reversed_e_letters_count}", 
                (x, y-40), 
                cv2.FONT_HERSHEY_SIMPLEX, 
                0.7, 
                (0, 0, 255), 
                2
            )
            
            # 如果有像素/厘米比率，显示它
            if pixel_cm_ratio:
                cv2.putText(
                    result_img, 
                    f"Ratio: {pixel_cm_ratio:.2f} px/cm", 
                    (x, y-70), 
                    cv2.FONT_HERSHEY_SIMPLEX, 
                    0.7, 
                    (0, 0, 255), 
                    2
                )
            
            # 绘制所有检测到的E字母
            for ex, ey, ew, eh, etype in all_e_letters:
                color = (0, 255, 0) if etype == 'E' else (0, 0, 255)
                cv2.rectangle(result_img, (ex, ey), (ex+ew, ey+eh), color, 2)
            
            # 如果有标尺顶端坐标，标记它
            if ruler_top:
                cv2.circle(result_img, ruler_top, 5, (255, 0, 255), -1)
                cv2.putText(
                    result_img, 
                    "Ruler Top", 
                    (ruler_top[0], ruler_top[1]-20), 
                    cv2.FONT_HERSHEY_SIMPLEX, 
                    0.7, 
                    (255, 0, 255), 
                    2
                )
    
    if debug:
        cv2.imwrite("debug_water_rule_visualization.jpg", result_img)
    
    return result_img 