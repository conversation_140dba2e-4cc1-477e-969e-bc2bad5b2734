import cv2
import numpy as np
from src.config.config_utils import config_manager

def _preprocess_roi_for_waterline(roi_bgr_image: np.ndarray) -> np.ndarray:
    """
    对ROI区域的BGR图像进行水线检测预处理。

    参数:
        roi_bgr_image (np.ndarray): BGR格式的ROI图像区域。

    返回:
        np.ndarray: 预处理后的单通道 (V通道) 图像。
    """
    # 从配置文件获取参数
    config = config_manager.get_config('image_preprocessing')
    blur_kernel_size = config.get('blur_kernel_size', 5)
    morph_kernel_size = config.get('morph_kernel_size', 3)
    
    hsv = cv2.cvtColor(roi_bgr_image, cv2.COLOR_BGR2HSV)
    v_channel = hsv[:, :, 2]  #提取V通道 (亮度)
    v_eq = cv2.equalizeHist(v_channel)  # 直方图均衡化
    v_blur = cv2.GaussianBlur(v_eq, (blur_kernel_size, blur_kernel_size), 0) # 高斯模糊去噪
    # 可选: 形态学开运算去除小的噪点
    kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
    v_morph = cv2.morphologyEx(v_blur, cv2.MORPH_OPEN, kernel)
    # cv2.imwrite("debug_preprocessed_v_channel.jpg", v_morph) # 保留此行用于调试，但默认注释掉
    return v_morph 