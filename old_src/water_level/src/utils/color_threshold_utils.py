import cv2
import numpy as np
import os
from src.config.config_utils import config_manager

# 从配置获取默认参数
def get_default_color_threshold_params():
    """从配置文件获取颜色阈值参数"""
    config = config_manager.get_config('color_threshold')
    return {
        'hsv_lower': config.get('hsv_lower', np.array([35, 40, 40])),
        'hsv_upper': config.get('hsv_upper', np.array([85, 255, 255])),
        'morph_kernel_size': config.get('morph_kernel_size', 5),
        'min_area_factor': config.get('min_area_factor', 0.01)
    }

# 默认参数值
params = get_default_color_threshold_params()
DEFAULT_HSV_LOWER = params['hsv_lower']   # 水体的HSV下限（绿色调范围）
DEFAULT_HSV_UPPER = params['hsv_upper']   # 水体的HSV上限
DEFAULT_MORPH_KERNEL_SIZE = params['morph_kernel_size']  # 形态学操作的核大小
DEFAULT_MIN_AREA_FACTOR = params['min_area_factor']  # 最小区域因子（相对于ROI面积）

def waterline_color_threshold_local(
    preprocessed_roi: np.ndarray,
    hsv_lower: np.array = None,
    hsv_upper: np.array = None,
    morph_kernel_size: int = None,
    min_area_factor: float = None,
    debug: bool = False
) -> int:
    """
    使用颜色空间分析与阈值法检测水面线
    
    Args:
        preprocessed_roi: 预处理后的ROI图像
        hsv_lower: HSV空间的下限阈值
        hsv_upper: HSV空间的上限阈值
        morph_kernel_size: 形态学操作的核大小
        min_area_factor: 最小区域因子（相对于ROI面积）
        debug: 是否保存调试图像
        
    Returns:
        水面线在ROI中的y坐标（局部坐标），如果未检测到则返回None
    """
    # 使用配置文件的默认值
    if hsv_lower is None:
        hsv_lower = DEFAULT_HSV_LOWER
    if hsv_upper is None:
        hsv_upper = DEFAULT_HSV_UPPER
    if morph_kernel_size is None:
        morph_kernel_size = DEFAULT_MORPH_KERNEL_SIZE
    if min_area_factor is None:
        min_area_factor = DEFAULT_MIN_AREA_FACTOR
        
    # 转换到HSV颜色空间
    hsv_roi = cv2.cvtColor(preprocessed_roi, cv2.COLOR_BGR2HSV)
    
    # 根据HSV阈值创建掩码
    water_mask = cv2.inRange(hsv_roi, hsv_lower, hsv_upper)
    
    if debug:
        cv2.imwrite("debug_hsv_mask_raw.jpg", water_mask)
    
    # 形态学操作，先开运算（去噪），再闭运算（填充小孔）
    kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
    water_mask = cv2.morphologyEx(water_mask, cv2.MORPH_OPEN, kernel)
    water_mask = cv2.morphologyEx(water_mask, cv2.MORPH_CLOSE, kernel)
    
    if debug:
        cv2.imwrite("debug_hsv_mask_morph.jpg", water_mask)
    
    # 找到所有轮廓
    contours, _ = cv2.findContours(water_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 如果没有轮廓，返回None
    if not contours:
        return None
    
    # 过滤掉太小的轮廓
    min_area = preprocessed_roi.shape[0] * preprocessed_roi.shape[1] * min_area_factor
    valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
    
    if not valid_contours:
        return None
    
    # 创建调试图像（如果需要）
    if debug:
        contour_img = preprocessed_roi.copy()
        cv2.drawContours(contour_img, valid_contours, -1, (0, 255, 0), 2)
        cv2.imwrite("debug_contours.jpg", contour_img)
    
    # 找到最大面积的轮廓
    max_contour = max(valid_contours, key=cv2.contourArea)
    
    # 获取轮廓的最上边缘作为水面线
    top_points = max_contour[max_contour[:, :, 1].argmin()][0]
    water_line_y = top_points[1]
    
    # 或者，可以找到轮廓的边界框，使用其顶部作为水面线
    x, y, w, h = cv2.boundingRect(max_contour)
    water_line_y = y
    
    # 可视化轮廓和水面线
    if debug:
        result_img = preprocessed_roi.copy()
        cv2.drawContours(result_img, [max_contour], -1, (0, 255, 0), 2)
        cv2.rectangle(result_img, (x, y), (x+w, y+h), (255, 0, 0), 2)
        cv2.line(result_img, (0, water_line_y), (result_img.shape[1], water_line_y), (0, 0, 255), 2)
        cv2.imwrite("debug_water_line.jpg", result_img)
    
    return water_line_y

def visualize_water_detection(
    roi_image: np.ndarray,
    hsv_lower: np.array = None,
    hsv_upper: np.array = None,
    morph_kernel_size: int = None,
    min_area_factor: float = None
) -> np.ndarray:
    """
    可视化水面检测过程的每个步骤
    
    Args:
        roi_image: ROI图像
        hsv_lower: HSV空间的下限阈值
        hsv_upper: HSV空间的上限阈值
        morph_kernel_size: 形态学操作的核大小
        min_area_factor: 最小区域因子（相对于ROI面积）
        
    Returns:
        包含可视化结果的图像
    """
    # 使用配置文件的默认值
    if hsv_lower is None:
        hsv_lower = DEFAULT_HSV_LOWER
    if hsv_upper is None:
        hsv_upper = DEFAULT_HSV_UPPER
    if morph_kernel_size is None:
        morph_kernel_size = DEFAULT_MORPH_KERNEL_SIZE
    if min_area_factor is None:
        min_area_factor = DEFAULT_MIN_AREA_FACTOR
        
    # 创建可视化结果图像
    h, w = roi_image.shape[:2]
    result_img = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
    
    # 1. 原始ROI图像
    result_img[0:h, 0:w] = roi_image.copy()
    cv2.putText(result_img, "1. Original ROI", (10, 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 2. HSV转换
    hsv_roi = cv2.cvtColor(roi_image, cv2.COLOR_BGR2HSV)
    hsv_display = cv2.cvtColor(hsv_roi, cv2.COLOR_HSV2BGR)  # 转回BGR用于显示
    result_img[0:h, w:w*2] = hsv_display
    cv2.putText(result_img, "2. HSV Conversion", (w+10, 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 3. 阈值掩码
    water_mask = cv2.inRange(hsv_roi, hsv_lower, hsv_upper)
    water_mask_bgr = cv2.cvtColor(water_mask, cv2.COLOR_GRAY2BGR)
    result_img[h:h*2, 0:w] = water_mask_bgr
    cv2.putText(result_img, "3. HSV Threshold Mask", (10, h+20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 4. 形态学处理后的掩码
    kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
    morph_mask = cv2.morphologyEx(water_mask, cv2.MORPH_OPEN, kernel)
    morph_mask = cv2.morphologyEx(morph_mask, cv2.MORPH_CLOSE, kernel)
    morph_mask_bgr = cv2.cvtColor(morph_mask, cv2.COLOR_GRAY2BGR)
    result_img[h:h*2, w:w*2] = morph_mask_bgr
    cv2.putText(result_img, "4. After Morphology", (w+10, h+20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 找到轮廓
    contours, _ = cv2.findContours(morph_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 过滤小轮廓
    min_area = roi_image.shape[0] * roi_image.shape[1] * min_area_factor
    valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
    
    # 在原始图像上绘制水面线
    final_result = roi_image.copy()
    
    if valid_contours:
        # 找到最大轮廓
        max_contour = max(valid_contours, key=cv2.contourArea)
        cv2.drawContours(final_result, [max_contour], -1, (0, 255, 0), 2)
        
        # 获取轮廓的边界框
        x, y, w_rect, h_rect = cv2.boundingRect(max_contour)
        cv2.rectangle(final_result, (x, y), (x+w_rect, y+h_rect), (255, 0, 0), 2)
        
        # 绘制水面线
        cv2.line(final_result, (0, y), (w, y), (0, 0, 255), 2)
        cv2.putText(final_result, f"Water line at y={y}", (10, y-10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    
    # 保存完整的可视化结果
    cv2.imwrite("debug_visualization_steps.jpg", result_img)
    cv2.imwrite("debug_final_detection.jpg", final_result)
    
    return result_img 