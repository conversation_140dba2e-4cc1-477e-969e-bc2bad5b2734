import cv2
import numpy as np
from typing import List, Tuple
import pytesseract
# import yaml - 不再需要，使用config_manager代替

# 导入配置管理器
from src.config.config_utils import config_manager

def _create_color_mask(image: np.ndarray, debug: bool = False, debug_filename_prefix: str = "detection") -> np.ndarray:
    """
    根据红色和蓝色范围创建颜色掩码。
    假定输入图像是BGR格式。

    Args:
        image (np.ndarray): 输入的BGR彩色图像。
        debug (bool, optional): 是否保存调试图像. Defaults to False.
        debug_filename_prefix (str, optional): 调试图像文件名的前缀. Defaults to "detection".

    Returns:
        np.ndarray: 生成的二值化掩码（前景为白色，背景为黑色）。
    """
    if len(image.shape) < 3 or image.shape[2] != 3:
        if len(image.shape) == 2:
            gray = image
        else:
            gray = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
        
        _, binary_mask = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        return binary_mask

    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # 基于提供的示例颜色和"颜色较深"的描述，定义颜色范围
    # 红色示例: 85514F, 763039, 662727, 54382D, 593536
    # 蓝色示例: 151225, 2A284D, 1F1839, 29253C, 282947
    
    # 定义红色范围 (H值在OpenCV中环绕)
    lower_red1 = np.array([0, 40, 40])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([170, 40, 40])
    upper_red2 = np.array([179, 255, 255])
    
    # 定义蓝色范围
    lower_blue = np.array([100, 30, 30])
    upper_blue = np.array([150, 255, 255])
    
    mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)
    
    mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)
    
    combined_mask = cv2.bitwise_or(mask_red, mask_blue)

    if debug:
        cv2.imwrite(f"debug_{debug_filename_prefix}_color_mask.jpg", combined_mask)
        
    return combined_mask

def _draw_contours_with_indices(image: np.ndarray, contours_with_indices: List[Tuple[int, np.ndarray]], output_filename: str):
    """辅助函数，用于绘制带编号的轮廓以供调试。"""
    debug_image = image.copy()
    for idx, contour in contours_with_indices:
        cv2.drawContours(debug_image, [contour], -1, (0, 255, 0), 2)
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            cv2.putText(debug_image, str(idx), (cx, cy), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    cv2.imwrite(output_filename, debug_image)

def _run_recognition_pass(
    contours_with_indices: List[Tuple[int, np.ndarray]],
    image_roi: np.ndarray,
    binary_roi: np.ndarray,
    roi_offset: Tuple[int, int],
    params: dict,
    debug_prefix: str = ""
) -> List[Tuple[int, int, int, int, str]]:
    """
    对给定的轮廓列表执行一轮完整的识别（宽高比、坚实度、形状分析）。
    这是一个辅助函数，用于支持两遍识别流程。
    """
    debug = params.get('debug', False)
    roi_x, roi_y = roi_offset

    # 过滤步骤2：基于宽高比
    aspect_ratio_filtered = []
    for idx, contour in contours_with_indices:
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        if params['aspect_ratio_range'][0] <= aspect_ratio <= params['aspect_ratio_range'][1]:
            aspect_ratio_filtered.append((idx, contour))
    if debug and image_roi is not None:
        _draw_contours_with_indices(image_roi, aspect_ratio_filtered, f"debug_e_detection_{debug_prefix}_2_aspect_ratio_filter.jpg")

    # 过滤步骤3：基于坚实度
    solidity_filtered = []
    for idx, contour in aspect_ratio_filtered:
        area = cv2.contourArea(contour)
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        solidity = float(area) / hull_area if hull_area > 0 else 0
        if params['solidity_range'][0] <= solidity <= params['solidity_range'][1]:
            solidity_filtered.append((idx, contour))
    if debug and image_roi is not None:
        _draw_contours_with_indices(image_roi, solidity_filtered, f"debug_e_detection_{debug_prefix}_3_solidity_filter.jpg")

    # 最终形状分析
    detected_shapes = []
    debug_image = None
    if debug and image_roi is not None:
        debug_image = image_roi.copy()
    
    for idx, contour in solidity_filtered:
        x, y, w, h = cv2.boundingRect(contour)
        roi_contour = binary_roi[y:y+h, x:x+w]

        horizontal_projection = np.sum(roi_contour, axis=1)
        if np.max(horizontal_projection) == 0: continue
        normalized_projection = horizontal_projection / np.max(horizontal_projection)

        peaks = []
        for i in range(1, len(normalized_projection) - 1):
            if (normalized_projection[i] > 0.5 and 
                normalized_projection[i] >= normalized_projection[i-1] and 
                normalized_projection[i] >= normalized_projection[i+1]):
                peaks.append(i)
        
        merged_peaks = []
        if peaks:
            current_group = [peaks[0]]
            for i in range(1, len(peaks)):
                if peaks[i] - peaks[i-1] <= 5:
                    current_group.append(peaks[i])
                else:
                    merged_peaks.append(sum(current_group) // len(current_group))
                    current_group = [peaks[i]]
            if current_group:
                merged_peaks.append(sum(current_group) // len(current_group))

        if 2 <= len(merged_peaks) <= 4:
            vertical_projection = np.sum(roi_contour, axis=0)
            if np.max(vertical_projection) == 0: continue
            normalized_v_projection = vertical_projection / np.max(vertical_projection)
            
            left_side_strength = np.mean(normalized_v_projection[:w//4])
            right_side_strength = np.mean(normalized_v_projection[3*w//4:])
            
            shape_type = None
            if left_side_strength > 0.55: shape_type = 'E'
            elif right_side_strength > 0.55: shape_type = 'RE'

            if shape_type:
                abs_x, abs_y = roi_x + x, roi_y + y
                # 返回结果中包含原始轮廓索引
                detected_shapes.append((abs_x, abs_y, w, h, shape_type, idx))
                if debug and debug_image is not None:
                    color = (0, 255, 0) if shape_type == 'E' else (0, 0, 255)
                    cv2.rectangle(debug_image, (x, y), (x+w, y+h), color, 2)
                    cv2.putText(debug_image, shape_type, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
    
    if debug and debug_image is not None and detected_shapes:
        cv2.imwrite(f"debug_e_detection_{debug_prefix}_4_final_results.jpg", debug_image)
        
    return detected_shapes

def _perform_second_pass_recognition(
    first_pass_results: List[Tuple[int, int, int, int, str, int]],
    area_filtered_contours: List[Tuple[int, np.ndarray]],
    image_roi: np.ndarray,
    binary_roi: np.ndarray,
    roi_offset: Tuple[int, int],
    roi_shape: Tuple[int, int],
    params: dict,
    overlap_threshold: float,
    centerline_data: dict,
    debug: bool = False
) -> List[Tuple[int, int, int, int, str]]:
    """
    执行第二轮识别：基于第一轮结果生成虚拟轮廓，用于指导识别更多字母
    
    Args:
        first_pass_results: 第一轮识别结果，包含原始轮廓索引
        area_filtered_contours: 面积过滤后的原始轮廓列表
        image_roi: ROI区域图像
        binary_roi: ROI区域二值化图像
        roi_offset: ROI偏移坐标 (roi_x, roi_y)
        roi_shape: ROI形状 (roi_h, roi_w)
        params: 识别参数
        overlap_threshold: 重叠阈值
        centerline_data: 中轴线数据字典，包含m_axis, c_axis, get_centerline_x等
        debug: 是否开启调试模式
        
    Returns:
        List[Tuple[int, int, int, int, str]]: 第二轮识别结果
    """
    roi_x, roi_y = roi_offset
    roi_h, roi_w = roi_shape
    
    if not first_pass_results:
        if debug: print("没有可靠形状传入，跳过第二轮识别。")
        return []
    
    # --- 上下文分析与虚拟轮廓生成 ---
    # 传入的first_pass_results已经是经过面积一致性筛选的可靠形状
    reliable_shapes = first_pass_results
    reliable_contour_indices = {s[5] for s in reliable_shapes}
    
    # 从第一轮传入的中轴线数据中获取参数
    m_axis = centerline_data['m_axis']
    c_axis = centerline_data['c_axis']
    get_centerline_x = centerline_data['get_centerline_x']
    median_h = centerline_data['median_h']
    median_w = centerline_data['median_w']
    all_centers = centerline_data['all_centers']
    
    # 1. 计算垂直步长 - 直接使用字母的高度
    vertical_step = median_h
    
    if debug:
        debug_step = image_roi.copy()
        for s in reliable_shapes:
            color = (0, 255, 0) if s[4] == 'E' else (0, 0, 255)
            cv2.rectangle(debug_step, (s[0], s[1]), (s[0]+s[2], s[1]+s[3]), color, 2)
            # 标注每个字母的高度
            cv2.putText(debug_step, f"H:{s[3]}", (s[0], s[1]+s[3]+15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        cv2.putText(debug_step, f"Vertical step: {vertical_step:.1f} (median height of letters)", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.imwrite("debug_virtual_step1_vertical_step.jpg", debug_step)
    
    # 2. 基于字母高度推演虚拟字母位置
    raw_predictions = []
    prediction_steps = []  # 记录推演步骤用于调试
    
    # 对每个可靠形状进行推演
    for shape_idx, shape in enumerate(reliable_shapes):
        start_x, start_y, start_w, start_h, start_type = shape[:5]
        start_center_y = start_y + start_h / 2
        
        # 向上推演
        current_y = start_center_y
        current_type = start_type
        up_predictions = []
        step_counter = 0
        while current_y > -median_h and step_counter < 10:  # 限制步数防止无限循环
            current_y -= vertical_step
            current_type = 'RE' if current_type == 'E' else 'E'
            step_counter += 1
            
            # 根据中线计算X位置
            centerline_x = get_centerline_x(current_y)
            
            # 根据类型确定相对于中线的位置
            if current_type == 'E':
                # 正向E在中线左侧
                pred_x = centerline_x - median_w
            else:
                # 反向E在中线右侧
                pred_x = centerline_x
            
            pred_y = current_y - median_h / 2
            prediction = (pred_x, pred_y, median_w, median_h)
            raw_predictions.append(prediction)
            up_predictions.append((prediction, current_type, step_counter, 'UP'))

        # 向下推演  
        current_y = start_center_y
        current_type = start_type
        down_predictions = []
        step_counter = 0
        while current_y < roi_h + median_h and step_counter < 10:  # 修正图像边界检查
            current_y += vertical_step
            current_type = 'RE' if current_type == 'E' else 'E'
            step_counter += 1
            
            # 根据中线计算X位置
            centerline_x = get_centerline_x(current_y)
            
            # 根据类型确定相对于中线的位置
            if current_type == 'E':
                # 正向E在中线左侧
                pred_x = centerline_x - median_w
            else:
                # 反向E在中线右侧  
                pred_x = centerline_x
            
            pred_y = current_y - median_h / 2
            prediction = (pred_x, pred_y, median_w, median_h)
            raw_predictions.append(prediction)
            down_predictions.append((prediction, current_type, step_counter, 'DOWN'))
        
        # 记录此形状的推演步骤
        prediction_steps.append({
            'shape_idx': shape_idx,
            'start_shape': shape,
            'up_predictions': up_predictions,
            'down_predictions': down_predictions,
            'vertical_step': vertical_step
        })

    if debug:
        # 绘制原始推演结果
        debug_raw_pred = image_roi.copy()
        # 绘制可靠形状
        for s in reliable_shapes:
            color = (0, 255, 0) if s[4] == 'E' else (0, 0, 255)
            cv2.rectangle(debug_raw_pred, (s[0], s[1]), (s[0]+s[2], s[1]+s[3]), color, 3)
        # 绘制所有原始推演
        for step_info in prediction_steps:
            start_shape = step_info['start_shape']
            shape_idx = step_info['shape_idx']
            
            # 绘制起始形状编号
            cv2.putText(debug_raw_pred, f"S{shape_idx}", 
                       (start_shape[0], start_shape[1]-15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
            
            # 绘制向上推演
            for pred, pred_type, step_num, direction in step_info['up_predictions']:
                color = (200, 100, 100) if pred_type == 'E' else (100, 100, 200)
                cv2.rectangle(debug_raw_pred, (int(pred[0]-roi_x), int(pred[1]-roi_y)), 
                             (int(pred[0]+pred[2]-roi_x), int(pred[1]+pred[3]-roi_y)), color, 1)
                cv2.putText(debug_raw_pred, f"U{step_num}", 
                           (int(pred[0]-roi_x), int(pred[1]-roi_y)), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
            
            # 绘制向下推演
            for pred, pred_type, step_num, direction in step_info['down_predictions']:
                color = (200, 100, 100) if pred_type == 'E' else (100, 100, 200)
                cv2.rectangle(debug_raw_pred, (int(pred[0]-roi_x), int(pred[1]-roi_y)), 
                             (int(pred[0]+pred[2]-roi_x), int(pred[1]+pred[3]-roi_y)), color, 1)
                cv2.putText(debug_raw_pred, f"D{step_num}", 
                           (int(pred[0]-roi_x), int(pred[1]-roi_y)), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
        
        cv2.putText(debug_raw_pred, f"Raw predictions: {len(raw_predictions)}", 
                   (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.imwrite("debug_virtual_step2_raw_predictions.jpg", debug_raw_pred)

    # 3. 过滤掉与任何可靠形状重叠的预测
    all_predictions = []
    for pred in raw_predictions:
        is_overlapping = False
        for reliable_shape in reliable_shapes:
            px, py, pw, ph = pred
            rx, ry, rw, rh = reliable_shape[:4]
            if not (px + pw < rx or px > rx + rw or py + ph < ry or py > ry + rh):
                is_overlapping = True
                break
        if not is_overlapping:
            all_predictions.append(pred)
    
    if debug:
        debug_filtered = image_roi.copy()
        # 绘制可靠形状
        for s in reliable_shapes:
            color = (0, 255, 0) if s[4] == 'E' else (0, 0, 255)
            cv2.rectangle(debug_filtered, (s[0], s[1]), (s[0]+s[2], s[1]+s[3]), color, 3)
        # 绘制过滤后的预测
        for pred in all_predictions:
            cv2.rectangle(debug_filtered, (int(pred[0]-roi_x), int(pred[1]-roi_y)), 
                         (int(pred[0]+pred[2]-roi_x), int(pred[1]+pred[3]-roi_y)), (255, 180, 180), 2)
        cv2.putText(debug_filtered, f"Filtered predictions: {len(all_predictions)} (removed {len(raw_predictions)-len(all_predictions)} overlapping)", 
                   (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.imwrite("debug_virtual_step3_filtered_predictions.jpg", debug_filtered)

    # 4. 直接使用过滤后的预测作为虚拟字母轮廓
    expected_bboxes = all_predictions
    
    if debug:
        debug_final = image_roi.copy()
        
        # 1. 绘制面积筛选后的非可靠字母轮廓（绿色）
        candidate_contours = [c for c in area_filtered_contours if c[0] not in reliable_contour_indices]
        for idx, contour in candidate_contours:
            cv2.drawContours(debug_final, [contour], -1, (0, 255, 0), 2)
            # 标注轮廓编号
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(debug_final, f"{idx}", (cx-5, cy), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        
        # 2. 绘制推理出来的虚拟字母轮廓（蓝色）
        for i, (x, y, w, h) in enumerate(expected_bboxes):
            cv2.rectangle(debug_final, (int(x-roi_x), int(y-roi_y)), (int(x-roi_x+w), int(y-roi_y+h)), (255, 0, 0), 2)
            cv2.putText(debug_final, f"V{i}", (int(x-roi_x), int(y-roi_y-5)), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        cv2.imwrite("debug_virtual_step4_final_virtual_contours.jpg", debug_final)
    
    # 5. 使用虚拟字母轮廓对面积筛选后的轮廓进行切割
    context_filtered_contours = []
    processed_indices = set()
    candidate_contours = [c for c in area_filtered_contours if c[0] not in reliable_contour_indices] # 面积筛选后的轮廓

    # 为每个候选轮廓创建切割掩码
    overlap_info = []  # 存储重叠信息用于调试
    for idx, contour in candidate_contours:
        if idx in processed_indices: continue
        
        x, y, w, h = cv2.boundingRect(contour) # 面积筛选后的轮廓指标
        abs_x, abs_y = x + roi_x, y + roi_y
        
        # 创建轮廓掩码用于面积计算
        contour_mask_full = np.zeros((roi_h, roi_w), dtype=np.uint8)
        cv2.fillPoly(contour_mask_full, [contour], 255)
        contour_area = cv2.countNonZero(contour_mask_full)
        
        # 检查与哪些虚拟字母轮廓有重叠（基于面积）
        intersecting_virtual_boxes = []
        for exp_x, exp_y, exp_w, exp_h in expected_bboxes: # 虚拟字母轮廓指标
            # 创建虚拟字母轮廓掩码
            virtual_mask = np.zeros((roi_h, roi_w), dtype=np.uint8)
            virt_x1 = max(0, int(exp_x - roi_x))
            virt_y1 = max(0, int(exp_y - roi_y))
            virt_x2 = min(roi_w, int(exp_x + exp_w - roi_x))
            virt_y2 = min(roi_h, int(exp_y + exp_h - roi_y))
            
            if virt_x2 > virt_x1 and virt_y2 > virt_y1:
                cv2.rectangle(virtual_mask, (virt_x1, virt_y1), (virt_x2, virt_y2), 255, -1)
                virtual_area = cv2.countNonZero(virtual_mask)
                
                # 计算重叠区域
                overlap_mask = cv2.bitwise_and(contour_mask_full, virtual_mask)
                overlap_area = cv2.countNonZero(overlap_mask)
                
                # 计算虚拟字母轮廓在候选轮廓内的比例
                if virtual_area > 0:
                    overlap_ratio = overlap_area / virtual_area
                    
                    # 存储重叠信息用于调试
                    overlap_info.append({
                        'contour_idx': idx,
                        'virtual_box': (exp_x, exp_y, exp_w, exp_h),
                        'overlap_ratio': overlap_ratio,
                        'contour_area': contour_area,
                        'virtual_area': virtual_area,
                        'overlap_area': overlap_area
                    })
                    
                    # 如果虚拟字母轮廓达到阈值以上落在候选轮廓内，则认为有效
                    if overlap_ratio >= overlap_threshold:
                        # 转换为相对于轮廓边界框的坐标
                        rel_x1 = virt_x1 - x
                        rel_y1 = virt_y1 - y
                        rel_x2 = virt_x2 - x
                        rel_y2 = virt_y2 - y
                        intersecting_virtual_boxes.append((rel_x1, rel_y1, rel_x2, rel_y2))
        
        if debug:
            # 将overlap_info保存到CSV文件
            import csv
            with open('debug_overlap_info.csv', 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=['contour_idx', 'virtual_box', 'overlap_ratio', 'contour_area', 'virtual_area', 'overlap_area'])
                writer.writeheader()
                writer.writerows(overlap_info)
        
        if intersecting_virtual_boxes:
            # 创建切割掩码
            contour_mask = np.zeros((h, w), dtype=np.uint8)
            cv2.fillPoly(contour_mask, [contour - [x, y]], 255)
            
            # 应用虚拟字母轮廓的切割
            for rel_x1, rel_y1, rel_x2, rel_y2 in intersecting_virtual_boxes:
                # 在切割区域内保留轮廓
                cut_mask = np.zeros((h, w), dtype=np.uint8)
                cv2.rectangle(cut_mask, (int(rel_x1), int(rel_y1)), (int(rel_x2), int(rel_y2)), 255, -1)
                contour_mask = cv2.bitwise_and(contour_mask, cut_mask)
            
            # 从切割后的掩码中提取新轮廓
            cut_contours, _ = cv2.findContours(contour_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 可选：保存切割过程的调试图像
            if debug and len(cut_contours) > 0:
                cut_debug = np.zeros((h, w, 3), dtype=np.uint8)
                cv2.fillPoly(cut_debug, [contour - [x, y]], (0, 255, 0))  # 原轮廓绿色
                for rel_x1, rel_y1, rel_x2, rel_y2 in intersecting_virtual_boxes:
                    cv2.rectangle(cut_debug, (int(rel_x1), int(rel_y1)), (int(rel_x2), int(rel_y2)), (255, 0, 0), 2)  # 切割区域蓝色
                cv2.drawContours(cut_debug, cut_contours, -1, (0, 0, 255), 2)  # 切割后轮廓红色
                cv2.imwrite(f"debug_cut_process_contour_{idx}.jpg", cut_debug)
            
            for cut_contour in cut_contours:
                # 将轮廓坐标调整回ROI坐标系
                adjusted_contour = cut_contour + [x, y]
                context_filtered_contours.append((idx, adjusted_contour))
            
            processed_indices.add(idx)

    if debug:
        debug_context_img = image_roi.copy()
        
        # 绘制重叠信息和比例
        for info in overlap_info:
            contour_idx = info['contour_idx']
            vx, vy, vw, vh = info['virtual_box']
            overlap_ratio = info['overlap_ratio']
            
            # 在重叠区域附近标注重叠比例
            text_x = int(vx - roi_x + vw/2)
            text_y = int(vy - roi_y + vh/2)
            ratio_text = f"{overlap_ratio:.2f}"
            
            # 根据重叠比例选择颜色
            if overlap_ratio >= overlap_threshold:
                color = (0, 255, 0)  # 绿色表示有效重叠
                cv2.putText(debug_context_img, f"✓{ratio_text}", (text_x, text_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 绘制中心轴线（更明显）
        if m_axis is not None and c_axis is not None:
            # 绘制中线，使用黄色且较粗
            y1, y2 = 0, roi_h
            x1 = int(m_axis * (y1 + roi_y) + c_axis - roi_x)
            x2 = int(m_axis * (y2 + roi_y) + c_axis - roi_x)
            cv2.line(debug_context_img, (x1, y1), (x2, y2), (0, 255, 255), 3)  # 黄色粗线
            
            # 在中线上标注
            mid_y = roi_h // 2
            mid_x = int(m_axis * (mid_y + roi_y) + c_axis - roi_x)
            cv2.putText(debug_context_img, "CENTER", (mid_x + 10, mid_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
        
        # 绘制被虚拟字母轮廓切割后的轮廓（绿色，这是最终用于第二遍识别的轮廓）
        for idx, contour in context_filtered_contours:
            cv2.drawContours(debug_context_img, [contour], -1, (0, 255, 0), 2)
        
        # 保存调试图像
        cv2.imwrite("debug_e_detection_5_context_filter.jpg", debug_context_img)
        
        # 打印重叠统计信息
        print("=== 轮廓重叠分析 ===")
        valid_overlaps = [info for info in overlap_info if info['overlap_ratio'] >= overlap_threshold]
        print(f"候选轮廓总数: {len(candidate_contours)}")
        print(f"虚拟字母轮廓总数: {len(expected_bboxes)}")
        print(f"重叠检测总数: {len(overlap_info)} (候选轮廓与虚拟轮廓的所有配对检测)")
        print(f"有效重叠数 (≥{overlap_threshold*100:.0f}%): {len(valid_overlaps)} (满足重叠阈值的配对数)")
        print(f"最终切割轮廓数: {len(context_filtered_contours)} (切割后生成的新轮廓片段数)")
        print("")
        print("说明:")
        print("- 重叠检测总数: 每个候选轮廓与每个虚拟轮廓的配对检测")
        print("- 有效重叠数: 虚拟轮廓在候选轮廓内的重叠率达到阈值的配对")
        print("- 最终切割轮廓数: 经过切割处理后，实际生成的新轮廓片段")
        print("  (一个候选轮廓可能被多个虚拟轮廓切割，生成多个片段)")
        print("  (切割后太小的片段会被过滤掉)")
        print("========================")

    if not context_filtered_contours:
        if debug: 
            print("上下文过滤后无新轮廓，返回第一遍识别的可靠结果。")
        return [s[:5] for s in reliable_shapes]
        
    # --- 第二遍识别 ---
    second_pass_results = _run_recognition_pass(
        context_filtered_contours, image_roi, binary_roi, (roi_x, roi_y), params, "pass2"
    )

    # 合并第一遍的可靠结果和第二遍的所有结果
    final_results = [s[:5] for s in reliable_shapes] + [s[:5] for s in second_pass_results]
    
    return final_results

def detect_e_shapes(
    image: np.ndarray,
    min_area: int = None,
    max_area: int = None,
    aspect_ratio_range: Tuple[float, float] = None,
    solidity_range: Tuple[float, float] = None,
    horizontal_segments_count: int = 3,
    roi: List[int] = None,
    debug: bool = False,
    overlap_threshold: float = None,
    enable_two_pass: bool = None
) -> List[Tuple[int, int, int, int, str]]:
    """
    在图像中统一检测类似字母"E"和反向"E"的形状。采用两遍识别策略提高准确性。
    
    Args:
        image: 输入的BGR彩色图像
        min_area: 轮廓的最小面积阈值（None时从配置文件加载）
        max_area: 轮廓的最大面积阈值（None时从配置文件加载）
        aspect_ratio_range: 宽高比的有效范围（None时从配置文件加载）
        solidity_range: 坚实度的有效范围（None时从配置文件加载）
        horizontal_segments_count: 水平分段数量（未使用）
        roi: ROI区域 [x, y, width, height]
        debug: 是否开启调试模式
        overlap_threshold: 虚拟轮廓与候选轮廓的重叠比例阈值（None时从配置文件加载）
        enable_two_pass: 是否启用两轮匹配识别（None时从配置文件加载）
        
    Returns:
        List of detected shapes: [(x, y, w, h, type), ...]
    """
    # 从配置文件加载默认参数
    try:
        e_config = config_manager.get_config('e_shape_detection')
        min_area = min_area if min_area is not None else e_config.get('min_area', 100)
        max_area = max_area if max_area is not None else e_config.get('max_area', 10000)
        if aspect_ratio_range is None:
            aspect_ratio_range = (e_config.get('aspect_ratio_min', 0.5), e_config.get('aspect_ratio_max', 2.0))
        if solidity_range is None:
            solidity_range = (e_config.get('solidity_min', 0.4), e_config.get('solidity_max', 0.9))
        overlap_threshold = overlap_threshold if overlap_threshold is not None else e_config.get('overlap_threshold', 0.5)
        enable_two_pass = enable_two_pass if enable_two_pass is not None else e_config.get('enable_two_pass', True)
    except Exception as e:
        print(f"无法加载字母E识别配置，使用默认参数: {e}")
        min_area = min_area if min_area is not None else 100
        max_area = max_area if max_area is not None else 10000
        aspect_ratio_range = aspect_ratio_range if aspect_ratio_range is not None else (0.5, 2.0)
        solidity_range = solidity_range if solidity_range is not None else (0.4, 0.9)
        overlap_threshold = overlap_threshold if overlap_threshold is not None else 0.5
        enable_two_pass = enable_two_pass if enable_two_pass is not None else True
    
    print("=== 字母E识别配置 ===")
    print(f"两轮匹配: {'启用' if enable_two_pass else '禁用'}")
    print(f"面积范围: {min_area}-{max_area}")
    print(f"宽高比范围: {aspect_ratio_range}")
    print(f"坚实度范围: {solidity_range}")
    print(f"重叠阈值: {overlap_threshold}")
    print("====================")
    
    binary_mask = _create_color_mask(image, debug, debug_filename_prefix="e_detection")
    
    if roi is None:
        roi = [0, 0, image.shape[1], image.shape[0]]

    roi_x, roi_y, roi_w, roi_h = roi
    binary_roi = binary_mask[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

    if debug:
        cv2.imwrite("debug_e_detection_0_binary_roi.jpg", binary_roi)

    contours, _ = cv2.findContours(binary_roi.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 提取ROI区域的原始图像，用于调试和识别
    image_roi = image[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
    
    if debug:
        _draw_contours_with_indices(image_roi, list(enumerate(contours)), "debug_e_detection_0_all_contours.jpg")

    # 过滤步骤1：基于面积
    area_filtered_contours = []
    for idx, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        if min_area <= area <= max_area:
            area_filtered_contours.append((idx, contour))
    if debug:
        _draw_contours_with_indices(image_roi, area_filtered_contours, "debug_e_detection_1_area_filter.jpg")

    # --- 第一遍识别 ---
    params = {
        'aspect_ratio_range': aspect_ratio_range,
        'solidity_range': solidity_range,
        'debug': debug
    }
    first_pass_results = _run_recognition_pass(
        area_filtered_contours, image_roi, binary_roi, (roi_x, roi_y), params, "pass1"
    )

    if not first_pass_results:
        print("第一遍识别未找到任何形状。")
        # 创建空的中轴线数据
        empty_centerline_data = {
            'm_axis': None,
            'c_axis': None,
            'get_centerline_x': None,
            'median_h': 0,
            'median_w': 0,
            'all_centers': []
        }
        return [], empty_centerline_data
    
    # --- 面积一致性筛选（从第二轮移到第一轮） ---
    # 基于面积一致性筛选可靠形状
    areas = [s[2] * s[3] for s in first_pass_results]
    median_area = np.median(areas)
    area_filtered_shapes = [s for s in first_pass_results if abs((s[2] * s[3]) - median_area) < median_area * 0.75]
    
    # --- X坐标一致性筛选 ---
    if area_filtered_shapes:
        x_coords = [s[0] for s in area_filtered_shapes]
        median_x = np.median(x_coords)
        # 使用中位数绝对偏差(MAD)来确定X坐标的一致性阈值
        mad_x = np.median([abs(x - median_x) for x in x_coords])
        x_threshold = max(mad_x * 2.5, 30)  # 至少30像素的容差
        x_filtered_shapes = [s for s in area_filtered_shapes if abs(s[0] - median_x) <= x_threshold]
        
        print(f"X坐标一致性筛选参数: 中位数X={median_x:.1f}, MAD={mad_x:.1f}, 阈值={x_threshold:.1f}")
    else:
        x_filtered_shapes = area_filtered_shapes
    
    # --- 高度一致性筛选 ---
    if x_filtered_shapes:
        heights = [s[3] for s in x_filtered_shapes]
        median_height = np.median(heights)
        # 使用中位数绝对偏差(MAD)来确定高度的一致性阈值
        mad_height = np.median([abs(h - median_height) for h in heights])
        height_threshold = max(mad_height * 2.5, 10)  # 至少10像素的容差
        reliable_shapes = [s for s in x_filtered_shapes if abs(s[3] - median_height) <= height_threshold]
        
        print(f"高度一致性筛选参数: 中位数高度={median_height:.1f}, MAD={mad_height:.1f}, 阈值={height_threshold:.1f}")
    else:
        reliable_shapes = x_filtered_shapes

    if not reliable_shapes:
        print("面积一致性筛选后没有找到可靠形状，返回第一遍识别结果。")
        # 创建空的中轴线数据
        empty_centerline_data = {
            'm_axis': None,
            'c_axis': None,
            'get_centerline_x': None,
            'median_h': 0,
            'median_w': 0,
            'all_centers': []
        }
        return [s[:5] for s in first_pass_results], empty_centerline_data

    if debug:
        debug_reliable = image_roi.copy()
        for s in reliable_shapes:
            color = (0, 255, 0) if s[4] == 'E' else (0, 0, 255)
            cv2.rectangle(debug_reliable, (s[0], s[1]), (s[0]+s[2], s[1]+s[3]), color, 2)
            cv2.putText(debug_reliable, s[4], (s[0], s[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        cv2.imwrite("debug_e_detection_pass1_reliable_shapes.jpg", debug_reliable)
        print(f"第一轮识别: {len(first_pass_results)} 个形状")
        print(f"面积一致性筛选后: {len(area_filtered_shapes)} 个形状")
        print(f"X坐标一致性筛选后: {len(x_filtered_shapes)} 个形状")
        print(f"高度一致性筛选后: {len(reliable_shapes)} 个可靠形状")
    
    # --- 中轴线计算（从第二轮移到第一轮） ---
    # 分析字母分布并计算中线
    reliable_normal_es = [s for s in reliable_shapes if s[4] == 'E']
    reliable_reversed_es = [s for s in reliable_shapes if s[4] == 'RE']
    
    # 计算所有可靠形状的中心点
    all_centers = []
    for s in reliable_shapes:
        center_x = s[0] + s[2] / 2
        center_y = s[1] + s[3] / 2
        all_centers.append((center_x, center_y, s[4]))  # (x, y, type)
    
    # 拟合中线：使用所有形状的中心点
    if len(all_centers) >= 2:
        y_coords = np.array([c[1] for c in all_centers])
        x_coords = np.array([c[0] for c in all_centers])
        # 拟合中线：y = mx + c -> x = (y - c) / m -> x = my + c (重新参数化)
        centerline_coeffs = np.polyfit(y_coords, x_coords, 1)
        m_axis, c_axis = centerline_coeffs[0], centerline_coeffs[1]
    else:
        # Fallback: 使用第一个形状作为基准
        center_x = reliable_shapes[0][0] + reliable_shapes[0][2] / 2
        m_axis, c_axis = 0, center_x
    
    get_centerline_x = np.poly1d([m_axis, c_axis])
    
    # 计算字母尺寸的中位数
    heights = [s[3] for s in reliable_shapes]
    widths = [s[2] for s in reliable_shapes]
    median_h = np.median(heights)
    median_w = np.median(widths)
    
    if debug:
        debug_centerline = image_roi.copy()
        # 绘制中心点
        for center_x, center_y, shape_type in all_centers:
            color = (0, 255, 0) if shape_type == 'E' else (0, 0, 255)
            cv2.circle(debug_centerline, (int(center_x), int(center_y)), 5, color, -1)
        # 绘制中线
        y1, y2 = 0, roi_h
        x1 = int(get_centerline_x(y1 + roi_y))
        x2 = int(get_centerline_x(y2 + roi_y))
        cv2.line(debug_centerline, (x1, y1), (x2, y2), (0, 255, 255), 3)
        cv2.putText(debug_centerline, f"Centerline: x={m_axis:.3f}*y+{c_axis:.1f}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        cv2.imwrite("debug_e_detection_pass1_centerline.jpg", debug_centerline)
        print(f"中轴线参数: m={m_axis:.3f}, c={c_axis:.1f}, 字母尺寸: {median_w:.1f}x{median_h:.1f}")
    
    # 准备中轴线数据
    centerline_data = {
        'm_axis': m_axis,
        'c_axis': c_axis,
        'get_centerline_x': get_centerline_x,
        'median_h': median_h,
        'median_w': median_w,
        'all_centers': all_centers
    }
    
    # 如果禁用两轮匹配，返回面积一致性筛选后的结果
    if not enable_two_pass:
        if debug: print("两轮匹配已禁用，返回面积一致性筛选后的结果。")
        # 返回结果和中轴线数据
        return [s[:5] for s in reliable_shapes], centerline_data

    # --- 第二遍识别 ---
    second_pass_results = _perform_second_pass_recognition(
        reliable_shapes, area_filtered_contours, image_roi, binary_roi, (roi_x, roi_y), (roi_h, roi_w), params, overlap_threshold, centerline_data, debug
    )

    return second_pass_results, centerline_data

def detect_digit_9(
    mask_image: np.ndarray,
    roi: List[int] = None,
    debug: bool = False
) -> List[Tuple[int, int, int, int]]:
    """
    在二值化图像的ROI区域内直接使用OCR检测数字"9"
    
    Args:
        mask_image: 输入图像
        roi: ROI区域 [x, y, width, height]，若为None则尝试从配置文件加载
        debug: 是否开启调试模式
        
    Returns:
        List[Tuple[int, int, int, int]]: 检测到的数字"9"的边界框列表 [(x, y, w, h), ...]
    """
    # 加载默认ROI
    if roi is None:
        try:
            # 使用main配置中的default_roi
            config = config_manager.get_config('main')
            roi = config.get('default_roi', [0, 0, mask_image.shape[1], mask_image.shape[0]])
        except Exception as e:
            print(f"无法加载配置文件，使用整个图像: {e}")
            roi = [0, 0, mask_image.shape[1], mask_image.shape[0]]
    
    # 检查输入图像类型
    if len(mask_image.shape) > 2:
        # 如果是彩色图像，转换为灰度图
        gray = cv2.cvtColor(mask_image, cv2.COLOR_BGR2GRAY)
    else:
        gray = mask_image.copy()
    
    # 提取ROI区域
    x, y, w, h = roi
    roi_img = gray[y:y+h, x:x+w]
    
    # 确保图像是二值化的
    _, binary = cv2.threshold(roi_img, 127, 255, cv2.THRESH_BINARY)
    
    if debug:
        cv2.imwrite("debug_9_detection_roi.jpg", roi_img)
        cv2.imwrite("debug_9_detection_binary.jpg", binary)
    
    # 增强图像以提高OCR准确性
    roi_resized = cv2.resize(binary, (0, 0), fx=2, fy=2, interpolation=cv2.INTER_CUBIC)
    roi_processed = cv2.GaussianBlur(roi_resized, (5, 5), 0)
    _, roi_processed = cv2.threshold(roi_processed, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    if debug:
        cv2.imwrite("debug_9_detection_processed.jpg", roi_processed)
    
    # 使用Tesseract OCR进行文本识别
    # 配置Tesseract只识别数字9
    custom_config = r'--oem 3 --psm 11 -c tessedit_char_whitelist=9'
    
    # 使用page segmentation模式11 (稀疏文本)
    result = pytesseract.image_to_data(roi_processed, config=custom_config, output_type=pytesseract.Output.DICT)
    
    detected_9_digits = []
    
    # 调试用图像
    if debug:
        debug_image = cv2.cvtColor(roi_img, cv2.COLOR_GRAY2BGR)
    
    # 处理OCR结果
    for i in range(len(result['text'])):
        if result['text'][i].strip() == '9':
            # 获取OCR检测到的坐标 (相对于ROI)
            x_ocr = result['left'][i]
            y_ocr = result['top'][i]
            w_ocr = result['width'][i]
            h_ocr = result['height'][i]
            
            # 缩放回原始尺寸
            x_ocr = x_ocr // 2
            y_ocr = y_ocr // 2
            w_ocr = max(w_ocr // 2, 1)  # 确保宽度至少为1
            h_ocr = max(h_ocr // 2, 1)  # 确保高度至少为1
            
            # 添加ROI偏移，得到在原图中的坐标
            x_abs = x + x_ocr
            y_abs = y + y_ocr
            
            detected_9_digits.append((x_abs, y_abs, w_ocr, h_ocr))
            
            if debug:
                cv2.rectangle(debug_image, (x_ocr, y_ocr), (x_ocr+w_ocr, y_ocr+h_ocr), (255, 0, 0), 2)
                cv2.putText(debug_image, "9", (x_ocr, y_ocr-10), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
    
    if debug and len(detected_9_digits) > 0:
        cv2.imwrite("debug_detected_9_digits.jpg", debug_image)
    
    return detected_9_digits

def _generate_ruler_top_search_roi(
    image: np.ndarray,
    centerline_data: dict,
    search_width: int = 200
) -> tuple:
    """
    基于中轴线生成水尺顶部搜索ROI
    
    Args:
        image: 输入图像
        centerline_data: 中轴线数据
        search_width: 搜索区域宽度
        
    Returns:
        tuple: (x, y, w, h) 搜索ROI，如果无法生成则返回None
    """
    if not centerline_data or centerline_data.get('get_centerline_x') is None:
        return None
    
    get_centerline_x = centerline_data['get_centerline_x']
    image_h, image_w = image.shape[:2]
    
    # 在图像上半部分搜索水尺顶部
    search_y_start = 0
    search_y_end = image_h # // 2  # 只搜索上半部分
    
    # 计算搜索区域的中心x坐标（基于图像中部的中轴线位置）
    center_y = search_y_end // 2
    center_x = int(get_centerline_x(center_y))
    
    # 生成搜索ROI
    roi_x = max(0, center_x - search_width // 2)
    roi_y = search_y_start
    roi_w = min(search_width, image_w - roi_x)
    roi_h = search_y_end - search_y_start
    
    return (roi_x, roi_y, roi_w, roi_h)

def detect_ruler_top_by_edge_detection(
    image: np.ndarray,
    roi: tuple = None,
    centerline_data: dict = None,
    canny_low: int = 50,
    canny_high: int = 150,
    debug: bool = False
) -> tuple:
    """
    使用边缘检测方法识别水尺顶部位置
    基于水尺顶部通常有明显的边缘特征这一假设
    检测垂直于中轴线的边缘
    
    Args:
        image: 输入的BGR格式图像
        roi: ROI区域 (x, y, w, h)，如果为None则基于中轴线生成搜索区域
        centerline_data: 中轴线数据，包含斜率等信息
        canny_low: Canny边缘检测低阈值
        canny_high: Canny边缘检测高阈值
        debug: 是否开启调试模式
        
    Returns:
        tuple: (top_x, top_y) 水尺顶部坐标，如果未找到则返回None
    """
    # 从配置获取搜索宽度
    from src.config.config_utils import config_manager
    water_rule_config = config_manager.get_config('water_rule') or {}
    search_width = water_rule_config.get('top_detection_search_width', 200)
    
    # 基于中轴线生成搜索ROI
    if roi is None or centerline_data:
        search_roi = _generate_ruler_top_search_roi(image, centerline_data, search_width)
        if search_roi:
            roi = search_roi
        elif roi is None:
            roi = (0, 0, image.shape[1], image.shape[0])
    
    roi_x, roi_y, roi_w, roi_h = roi
    roi_image = image[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
    
    # 转换为灰度图
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Canny边缘检测
    edges = cv2.Canny(blurred, canny_low, canny_high)
    
    # 获取中轴线信息
    if centerline_data and centerline_data.get('m_axis') is not None:
        m_axis = centerline_data['m_axis']
        # 计算垂直于中轴线的角度
        centerline_angle = np.arctan(m_axis)  # 中轴线角度
        perpendicular_angle = centerline_angle + np.pi/2  # 垂直角度
        
        # 创建垂直于中轴线的形态学核
        kernel_length = 15
        cos_angle = np.cos(perpendicular_angle)
        sin_angle = np.sin(perpendicular_angle)
        
        # 创建旋转的线性核
        kernel_size = max(15, int(kernel_length * abs(cos_angle) + kernel_length * abs(sin_angle)))
        kernel = np.zeros((kernel_size, kernel_size), dtype=np.uint8)
        
        # 在核中心画一条垂直于中轴线的线
        center = kernel_size // 2
        for i in range(-kernel_length//2, kernel_length//2):
            x = int(center + i * cos_angle)
            y = int(center + i * sin_angle)
            if 0 <= x < kernel_size and 0 <= y < kernel_size:
                kernel[y, x] = 1
    else:
        # 如果没有中轴线信息，使用水平核
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 1))
    
    # 形态学操作，连接垂直于中轴线的边缘
    processed_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
    
    # 寻找边缘线条
    contours, _ = cv2.findContours(processed_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        print("边缘检测方法：未找到明显的边缘")
        return None
    
    # 分析所有轮廓并保存详细信息
    contour_info = []
    for i, contour in enumerate(contours):
        x, y, w, h = cv2.boundingRect(contour)
        area = cv2.contourArea(contour)
        contour_info.append({
            'id': i,
            'x': x,
            'y': y,
            'w': w,
            'h': h,
            'area': area,
            'center_x': x + w // 2,
            'center_y': y + h // 2
        })
    
    if debug:
        # 保存所有轮廓信息到CSV文件
        import csv
        with open("debug_ruler_top_contours_analysis.csv", "w", newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['id', 'x', 'y', 'w', 'h', 'area', 'center_x', 'center_y'])
            writer.writeheader()
            writer.writerows(contour_info)
        print(f"轮廓分析：共找到 {len(contours)} 个轮廓，详细信息已保存到 debug_ruler_top_contours_analysis.csv")
    
    # 过滤轮廓：过滤太小的边缘和太靠近边界的边缘
    valid_contours = []
    for info in contour_info:
        # 边缘应该有一定长度，且不在图像顶部边缘，面积也要合适
        if (info['w'] > roi_w * 0.2 and 
            info['y'] > 10 and 
            info['area'] > 50):  # 添加最小面积要求
            valid_contours.append(info)
    
    if debug:
        print(f"轮廓过滤：{len(contours)} -> {len(valid_contours)} 个有效轮廓")
        
        # 绘制所有轮廓（灰色）和有效轮廓（绿色）
        debug_image = roi_image.copy()
        
        # 先绘制所有轮廓（灰色）
        for i, contour in enumerate(contours):
            cv2.drawContours(debug_image, [contour], -1, (128, 128, 128), 1)
            # 标注轮廓ID
            info = contour_info[i]
            cv2.putText(debug_image, str(info['id']), 
                       (info['center_x'], info['center_y']), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)
        
        # 再绘制有效轮廓（绿色）并标注ID
        for info in valid_contours:
            contour = contours[info['id']]
            cv2.drawContours(debug_image, [contour], -1, (0, 255, 0), 2)
            cv2.putText(debug_image, f"V{info['id']}", 
                       (info['center_x'], info['center_y']), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            # 绘制边界框
            cv2.rectangle(debug_image, (info['x'], info['y']), 
                         (info['x'] + info['w'], info['y'] + info['h']), (0, 255, 0), 1)
        
        cv2.imwrite("debug_ruler_top_contours_filtered.jpg", debug_image)
    
    if not valid_contours:
        print("边缘检测方法：未找到符合条件的水平边缘")
        return None
    
    # 找到面积最大的有效边缘作为水尺顶部
    # 按面积排序，选择面积最大的
    valid_contours.sort(key=lambda x: x['area'], reverse=True)
    top_contour = valid_contours[0]
    
    top_y_local = top_contour['y']
    top_center_x = top_contour['center_x']
    
    # 转换为全局坐标
    top_x_global = roi_x + top_center_x
    top_y_global = roi_y + top_y_local
    
    if debug:
        # 创建最终结果可视化
        debug_final = roi_image.copy()
        
        # 绘制所有有效轮廓（绿色）
        for info in valid_contours:
            contour = contours[info['id']]
            cv2.drawContours(debug_final, [contour], -1, (0, 255, 0), 2)
            cv2.putText(debug_final, f"V{info['id']}", 
                       (info['center_x'], info['center_y']), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # 突出显示选中的顶部轮廓（红色）
        top_contour_shape = contours[top_contour['id']]
        cv2.drawContours(debug_final, [top_contour_shape], -1, (0, 0, 255), 3)
        cv2.circle(debug_final, (top_center_x, top_y_local), 8, (255, 0, 0), -1)
        cv2.putText(debug_final, f"TOP-{top_contour['id']}", 
                   (top_center_x + 10, top_y_local - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
        
        cv2.imwrite("debug_ruler_top_edge_detection.jpg", debug_final)
        cv2.imwrite("debug_ruler_top_edges.jpg", edges)
        cv2.imwrite("debug_ruler_top_processed_edges.jpg", processed_edges)
        
        print(f"边缘检测方法：选择面积最大的轮廓ID {top_contour['id']} 作为水尺顶部")
        print(f"轮廓信息：位置({top_contour['x']}, {top_contour['y']}), 尺寸({top_contour['w']}x{top_contour['h']}), 面积{top_contour['area']}")
        print(f"检测到水尺顶部位置 ({top_x_global}, {top_y_global})")
    
    return (top_x_global, top_y_global)

def detect_ruler_top_by_texture_similarity(
    image: np.ndarray,
    centerline_x: int,
    roi: tuple = None,
    centerline_data: dict = None,
    window_size: int = 50,
    step_size: int = 10,
    similarity_threshold: float = 0.8,
    debug: bool = False
) -> tuple:
    """
    使用滑动窗口纹理相似性分析方法识别水尺顶部位置
    基于水尺区域具有相似纹理特征这一假设
    
    Args:
        image: 输入的BGR格式图像
        centerline_x: 中轴线的x坐标（保留兼容性）
        roi: ROI区域 (x, y, w, h)，如果为None则基于中轴线生成搜索区域
        centerline_data: 中轴线数据，包含斜率等信息
        window_size: 滑动窗口大小
        step_size: 滑动步长
        similarity_threshold: 相似性阈值
        debug: 是否开启调试模式
        
    Returns:
        tuple: (top_x, top_y) 水尺顶部坐标，如果未找到则返回None
    """
    # 从配置获取搜索宽度
    from src.config.config_utils import config_manager
    water_rule_config = config_manager.get_config('water_rule') or {}
    search_width = water_rule_config.get('top_detection_search_width', 200)
    
    # 基于中轴线生成搜索ROI
    if roi is None or centerline_data:
        search_roi = _generate_ruler_top_search_roi(image, centerline_data, search_width)
        if search_roi:
            roi = search_roi
        elif roi is None:
            # 回退到基于centerline_x生成ROI
            roi_x = max(0, centerline_x - search_width // 2)
            roi_w = min(search_width, image.shape[1] - roi_x)
            roi = (roi_x, 0, roi_w, image.shape[0])
    
    roi_x, roi_y, roi_w, roi_h = roi
    roi_image = image[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
    
    # 转换为灰度图
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    
    # 计算每个窗口位置的纹理特征
    similarities = []
    window_positions = []
    
    # 选择参考窗口（假设在ROI中部有典型的水尺纹理）
    ref_y = roi_h // 3  # 在上半部分选择参考区域
    ref_window = gray[ref_y:ref_y+window_size, :]
    
    # 计算参考窗口的纹理特征（使用梯度幅值）
    ref_grad_x = cv2.Sobel(ref_window, cv2.CV_64F, 1, 0, ksize=3)
    ref_grad_y = cv2.Sobel(ref_window, cv2.CV_64F, 0, 1, ksize=3)
    ref_magnitude = np.sqrt(ref_grad_x**2 + ref_grad_y**2)
    ref_texture = np.mean(ref_magnitude)
    
    # 滑动窗口分析
    for y in range(0, roi_h - window_size, step_size):
        window = gray[y:y+window_size, :]
        
        # 计算当前窗口的纹理特征
        grad_x = cv2.Sobel(window, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(window, cv2.CV_64F, 0, 1, ksize=3)
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        texture = np.mean(magnitude)
        
        # 计算相似度（归一化相关系数）
        if ref_texture > 0 and texture > 0:
            similarity = min(texture, ref_texture) / max(texture, ref_texture)
        else:
            similarity = 0
        
        similarities.append(similarity)
        window_positions.append(y)
    
    # 找到相似度突然下降的位置（可能是水尺顶部）
    similarities = np.array(similarities)
    if len(similarities) < 5:
        if debug:
            print("纹理相似性方法：可分析的窗口数量不足")
        return None
    
    # 计算相似度梯度
    similarity_grad = np.gradient(similarities)
    
    # 寻找最大负梯度位置（相似度下降最快的地方）
    min_grad_idx = np.argmin(similarity_grad)
    
    # 验证该位置的相似度是否低于阈值
    if similarities[min_grad_idx] > similarity_threshold:
        if debug:
            print(f"纹理相似性方法：未找到相似度低于阈值的区域，最低相似度：{similarities[min_grad_idx]:.3f}")
        return None
    
    # 计算顶部位置
    top_y_local = window_positions[min_grad_idx]
    top_x_local = roi_w // 2  # 使用ROI中心作为x坐标
    
    # 转换为全局坐标
    top_x_global = roi_x + top_x_local
    top_y_global = roi_y + top_y_local
    
    if debug:
        debug_image = roi_image.copy()
        
        # 标记参考窗口
        cv2.rectangle(debug_image, (0, ref_y), (roi_w, ref_y+window_size), (0, 255, 0), 2)
        cv2.putText(debug_image, "REF", (5, ref_y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # 标记检测到的顶部
        cv2.rectangle(debug_image, (0, top_y_local), (roi_w, top_y_local+window_size), (0, 0, 255), 2)
        cv2.circle(debug_image, (top_x_local, top_y_local), 5, (255, 0, 0), -1)
        cv2.putText(debug_image, "TOP", (top_x_local+10, top_y_local), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        cv2.imwrite("debug_ruler_top_texture_similarity.jpg", debug_image)
        
        # 保存相似度曲线数据
        with open("debug_ruler_top_similarity_data.csv", "w") as f:
            f.write("Y,Similarity,Gradient\n")
            for i, (pos, sim, grad) in enumerate(zip(window_positions, similarities, similarity_grad)):
                f.write(f"{pos},{sim:.4f},{grad:.4f}\n")
        
        print(f"纹理相似性方法：检测到水尺顶部位置 ({top_x_global}, {top_y_global})")
        print(f"该位置相似度：{similarities[min_grad_idx]:.3f}，梯度：{similarity_grad[min_grad_idx]:.3f}")
    
    return (top_x_global, top_y_global)

def detect_ruler_top_by_line_detection(
    image: np.ndarray,
    roi: tuple = None,
    centerline_data: dict = None,
    debug: bool = False
) -> tuple:
    """
    使用霍夫直线检测方法识别水尺顶部位置
    检测水尺顶部的直线特征
    
    Args:
        image: 输入的BGR格式图像
        roi: ROI区域 (x, y, w, h)，如果为None则基于中轴线生成搜索区域
        centerline_data: 中轴线数据，包含斜率等信息
        debug: 是否开启调试模式
        
    Returns:
        tuple: (top_x, top_y) 水尺顶部坐标，如果未找到则返回None
    """
    # 从配置获取搜索宽度
    from src.config.config_utils import config_manager
    water_rule_config = config_manager.get_config('water_rule') or {}
    search_width = water_rule_config.get('top_detection_search_width', 200)
    
    # 基于中轴线生成搜索ROI
    if roi is None or centerline_data:
        search_roi = _generate_ruler_top_search_roi(image, centerline_data, search_width)
        if search_roi:
            roi = search_roi
        elif roi is None:
            roi = (0, 0, image.shape[1], image.shape[0])
    
    roi_x, roi_y, roi_w, roi_h = roi
    roi_image = image[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
    
    # 转换为灰度图
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 边缘检测
    edges = cv2.Canny(blurred, 50, 150)
    
    # 霍夫直线检测
    lines = cv2.HoughLinesP(
        edges, 
        rho=1, 
        theta=np.pi/180, 
        threshold=50,
        minLineLength=int(roi_w * 0.3),  # 最小线长为ROI宽度的30%
        maxLineGap=10
    )
    
    if lines is None:
        if debug:
            print("霍夫直线检测方法：未检测到直线")
        return None
    
    # 过滤出水平线（角度接近0度）
    horizontal_lines = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)
        if angle < 15 or angle > 165:  # 接近水平的线
            horizontal_lines.append((x1, y1, x2, y2, y1))  # 添加y坐标用于排序
    
    if not horizontal_lines:
        if debug:
            print("霍夫直线检测方法：未检测到水平线")
        return None
    
    # 按y坐标排序，找到最高的水平线
    horizontal_lines.sort(key=lambda x: x[4])
    top_line = horizontal_lines[0]
    x1, y1, x2, y2, _ = top_line
    
    # 计算线的中心点作为水尺顶部
    top_x_local = (x1 + x2) // 2
    top_y_local = (y1 + y2) // 2
    
    # 转换为全局坐标
    top_x_global = roi_x + top_x_local
    top_y_global = roi_y + top_y_local
    
    if debug:
        debug_image = roi_image.copy()
        
        # 绘制所有检测到的水平线
        for x1, y1, x2, y2, _ in horizontal_lines:
            cv2.line(debug_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 突出显示顶部线条
        x1, y1, x2, y2, _ = top_line
        cv2.line(debug_image, (x1, y1), (x2, y2), (0, 0, 255), 3)
        cv2.circle(debug_image, (top_x_local, top_y_local), 5, (255, 0, 0), -1)
        
        cv2.imwrite("debug_ruler_top_line_detection.jpg", debug_image)
        cv2.imwrite("debug_ruler_top_edges_for_lines.jpg", edges)
        
        print(f"霍夫直线检测方法：检测到{len(horizontal_lines)}条水平线")
        print(f"水尺顶部位置 ({top_x_global}, {top_y_global})")
    
    return (top_x_global, top_y_global)

def detect_ruler_top_by_pattern_matching(
    image: np.ndarray,
    centerline_x: int,
    roi: tuple = None,
    centerline_data: dict = None,
    pattern_height: int = 30,
    debug: bool = False
) -> tuple:
    """
    使用模式匹配方法识别水尺顶部位置
    基于水尺顶部通常有特定的图案特征
    
    Args:
        image: 输入的BGR格式图像
        centerline_x: 中轴线的x坐标（保留兼容性）
        roi: ROI区域 (x, y, w, h)，如果为None则基于中轴线生成搜索区域
        centerline_data: 中轴线数据，包含斜率等信息
        pattern_height: 模式高度
        debug: 是否开启调试模式
        
    Returns:
        tuple: (top_x, top_y) 水尺顶部坐标，如果未找到则返回None
    """
    # 从配置获取搜索宽度
    from src.config.config_utils import config_manager
    water_rule_config = config_manager.get_config('water_rule') or {}
    search_width = water_rule_config.get('top_detection_search_width', 200)
    
    # 基于中轴线生成搜索ROI
    if roi is None or centerline_data:
        search_roi = _generate_ruler_top_search_roi(image, centerline_data, search_width)
        if search_roi:
            roi = search_roi
        elif roi is None:
            # 回退到基于centerline_x生成ROI
            roi_x = max(0, centerline_x - search_width // 2)
            roi_w = min(search_width, image.shape[1] - roi_x)
            roi = (roi_x, 0, roi_w, image.shape[0] // 2)  # 只搜索上半部分
    
    roi_x, roi_y, roi_w, roi_h = roi
    roi_image = image[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
    
    # 转换为灰度图
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    
    # 计算垂直梯度（水尺顶部通常有强烈的垂直边缘）
    grad_y = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
    grad_y_abs = np.abs(grad_y)
    
    # 寻找垂直梯度最强的水平条带
    vertical_response = np.mean(grad_y_abs, axis=1)
    
    if len(vertical_response) < pattern_height:
        if debug:
            print("模式匹配方法：ROI高度不足")
        return None
    
    # 使用滑动窗口找到梯度响应最强的区域
    max_response = 0
    best_y = 0
    
    for y in range(len(vertical_response) - pattern_height):
        window_response = np.mean(vertical_response[y:y+pattern_height])
        if window_response > max_response:
            max_response = window_response
            best_y = y
    
    # 验证响应强度
    mean_response = np.mean(vertical_response)
    if max_response < mean_response * 1.5:  # 响应必须显著高于平均值
        if debug:
            print(f"模式匹配方法：未找到显著的梯度响应，最大响应：{max_response:.2f}，平均响应：{mean_response:.2f}")
        return None
    
    # 在最佳区域内寻找精确的顶部位置
    top_region = vertical_response[best_y:best_y+pattern_height]
    relative_top = np.argmax(top_region)
    top_y_local = best_y + relative_top
    top_x_local = roi_w // 2
    
    # 转换为全局坐标
    top_x_global = roi_x + top_x_local
    top_y_global = roi_y + top_y_local
    
    if debug:
        debug_image = roi_image.copy()
        
        # 标记检测区域
        cv2.rectangle(debug_image, (0, best_y), (roi_w, best_y+pattern_height), (0, 255, 0), 2)
        cv2.circle(debug_image, (top_x_local, top_y_local), 5, (255, 0, 0), -1)
        cv2.putText(debug_image, "TOP", (top_x_local+10, top_y_local), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        cv2.imwrite("debug_ruler_top_pattern_matching.jpg", debug_image)
        cv2.imwrite("debug_ruler_top_gradient_y.jpg", (grad_y_abs * 255 / np.max(grad_y_abs)).astype(np.uint8))
        
        # 保存响应曲线数据
        with open("debug_ruler_top_response_data.csv", "w") as f:
            f.write("Y,Response\n")
            for i, response in enumerate(vertical_response):
                f.write(f"{i},{response:.4f}\n")
        
        print(f"模式匹配方法：检测到水尺顶部位置 ({top_x_global}, {top_y_global})")
        print(f"最大响应值：{max_response:.2f}，平均响应值：{mean_response:.2f}")
    
    return (top_x_global, top_y_global)

def detect_ruler_top_multi_method(
    image: np.ndarray,
    centerline_x: int = None,
    roi: tuple = None,
    centerline_data: dict = None,
    debug: bool = False
) -> dict:
    """
    综合使用多种方法检测水尺顶部位置，提供更鲁棒的检测结果
    
    Args:
        image: 输入的BGR格式图像
        centerline_x: 中轴线的x坐标（从E字母检测结果获得）
        roi: ROI区域 (x, y, w, h)
        debug: 是否开启调试模式
        
    Returns:
        dict: 包含各方法检测结果和综合结果的字典
    """
    results = {
        "edge_detection": None,
        "texture_similarity": None,
        "line_detection": None,
        "pattern_matching": None,
        "final_result": None,
        "confidence": 0.0,
        "method_count": 0
    }
    
    valid_results = []
    
    # 方法1：边缘检测
    try:
        edge_result = detect_ruler_top_by_edge_detection(image, roi, centerline_data, debug=debug)
        results["edge_detection"] = edge_result
        if edge_result:
            valid_results.append(("edge_detection", edge_result))
    except Exception as e:
        if debug:
            print(f"边缘检测方法出错：{e}")
    
    # 方法2：纹理相似性（需要centerline_x）
    if centerline_x is not None:
        try:
            texture_result = detect_ruler_top_by_texture_similarity(
                image, centerline_x, roi, centerline_data, debug=debug
            )
            results["texture_similarity"] = texture_result
            if texture_result:
                valid_results.append(("texture_similarity", texture_result))
        except Exception as e:
            if debug:
                print(f"纹理相似性方法出错：{e}")
    
    # 方法3：霍夫直线检测
    try:
        line_result = detect_ruler_top_by_line_detection(image, roi, centerline_data, debug=debug)
        results["line_detection"] = line_result
        if line_result:
            valid_results.append(("line_detection", line_result))
    except Exception as e:
        if debug:
            print(f"霍夫直线检测方法出错：{e}")
    
    # 方法4：模式匹配（需要centerline_x）
    if centerline_x is not None:
        try:
            pattern_result = detect_ruler_top_by_pattern_matching(
                image, centerline_x, roi, centerline_data, debug=debug
            )
            results["pattern_matching"] = pattern_result
            if pattern_result:
                valid_results.append(("pattern_matching", pattern_result))
        except Exception as e:
            if debug:
                print(f"模式匹配方法出错：{e}")
    
    # 综合多个方法的结果
    if valid_results:
        results["method_count"] = len(valid_results)
        
        # 如果只有一个有效结果，直接使用
        if len(valid_results) == 1:
            results["final_result"] = valid_results[0][1]
            results["confidence"] = 0.5
        else:
            # 多个结果时，计算平均位置（加权或简单平均）
            x_coords = [result[1][0] for result in valid_results]
            y_coords = [result[1][1] for result in valid_results]
            
            # 计算坐标的标准差来评估一致性
            x_std = np.std(x_coords)
            y_std = np.std(y_coords)
            
            # 如果结果比较一致（标准差小），则信心度高
            max_std = max(x_std, y_std)
            if max_std < 20:  # 像素级别的一致性
                results["confidence"] = min(1.0, len(valid_results) * 0.3)
                results["final_result"] = (int(np.mean(x_coords)), int(np.mean(y_coords)))
            elif max_std < 50:
                results["confidence"] = min(0.8, len(valid_results) * 0.2)
                results["final_result"] = (int(np.mean(x_coords)), int(np.mean(y_coords)))
            else:
                # 结果不一致时，选择y坐标最小的（最高的点）
                min_y_idx = np.argmin(y_coords)
                results["final_result"] = valid_results[min_y_idx][1]
                results["confidence"] = 0.3
    
    if debug:
        print(f"多方法检测结果：")
        for method, result in [("边缘检测", results["edge_detection"]),
                              ("纹理相似性", results["texture_similarity"]),
                              ("直线检测", results["line_detection"]),
                              ("模式匹配", results["pattern_matching"])]:
            print(f"  {method}: {result}")
        print(f"最终结果: {results['final_result']}")
        print(f"有效方法数: {results['method_count']}")
        print(f"信心度: {results['confidence']:.2f}")
        
        if results["final_result"]:
            # 可视化所有结果
            vis_image = image.copy()
            colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # 不同颜色标记不同方法
            method_names = ["edge", "texture", "line", "pattern"]
            
            for i, (method_name, result) in enumerate(valid_results):
                if result:
                    cv2.circle(vis_image, result, 8, colors[i], 2)
                    cv2.putText(vis_image, method_names[i], 
                               (result[0]+10, result[1]-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, colors[i], 2)
            
            # 标记最终结果
            final_pos = results["final_result"]
            cv2.circle(vis_image, final_pos, 12, (255, 255, 255), 3)
            cv2.putText(vis_image, "FINAL", (final_pos[0]+15, final_pos[1]), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imwrite("debug_ruler_top_multi_method.jpg", vis_image)
    
    return results