import cv2
import numpy as np
import pandas as pd
from src.config.config_utils import config_manager
import csv

# 从配置获取默认参数
def get_default_transparency_params():
    """从配置文件获取透明度检测参数"""
    config = config_manager.get_config('transparency')
    return {
        'alpha_thresh': config.get('alpha_thresh', 50),
        'canny_low': config.get('canny_low', 30),
        'canny_high': config.get('canny_high', 100),
        'blur_ksize': config.get('blur_ksize', 5),
        'morph_kernel_size': config.get('morph_kernel_size', 5),
        'min_area_factor': config.get('min_area_factor', 0.01),
        'brightness_thresh': config.get('brightness_thresh', 80),
        'black_pixel_ratio_thresh': config.get('black_pixel_ratio_thresh', 0.5),
        'avg_change_rate_thresh': config.get('avg_change_rate_thresh', 0.4)
    }

def get_contrast_change_params():
    """从配置文件获取对比度变化检测参数"""
    config = config_manager.get_config('transparency')
    contrast_config = config.get('contrast_change', {})
    return {
        'threshold_factor': contrast_config.get('threshold_factor', 1.5),
        'change_rate_threshold': contrast_config.get('change_rate_threshold', 0.3),
        'absolute_diff_threshold': contrast_config.get('absolute_diff_threshold', 15.0),
        'min_consecutive_rows': contrast_config.get('min_consecutive_rows', 3),
        'search_window': contrast_config.get('search_window', 50),
        'water_region_height_factor': contrast_config.get('water_region_height_factor', 0.5),
        'water_region_min_height': contrast_config.get('water_region_min_height', 200)
    }

# 默认参数值
params = get_default_transparency_params()
DEFAULT_ALPHA_THRESH = params['alpha_thresh']            # 透明度检测阈值
DEFAULT_CANNY_LOW = params['canny_low']               # Canny边缘检测低阈值
DEFAULT_CANNY_HIGH = params['canny_high']             # Canny边缘检测高阈值
DEFAULT_BLUR_KSIZE = params['blur_ksize']               # 高斯模糊核大小
DEFAULT_MORPH_KERNEL_SIZE = params['morph_kernel_size']        # 形态学操作的核大小
DEFAULT_MIN_AREA_FACTOR = params['min_area_factor']       # 最小区域因子（相对于ROI面积）
DEFAULT_BRIGHTNESS_THRESH = params['brightness_thresh']      # 亮度阈值，用于区分透明水面和非水面
DEFAULT_BLACK_PIXEL_RATIO_THRESH = params['black_pixel_ratio_thresh']  # 黑色像素占比阈值
DEFAULT_AVG_CHANGE_RATE_THRESH = params['avg_change_rate_thresh']      # 平均变化率阈值

def visualize_transparency_heatmap(
    roi_image: np.ndarray,
    blur_ksize: int = 5,
    contrast_params: dict = None,
    debug: bool = False
) -> tuple:
    """
    生成透明度热力图可视化，帮助理解图像中不同区域的透明度分布
    
    Args:
        roi_image: ROI图像
        blur_ksize: 高斯模糊核大小
        debug: 是否保存调试信息
        
    Returns:
        tuple: (热力图叠加结果图像, 透明度分数矩阵, 统计信息字典)
    """
    if not debug:
        return None, None, None
    
    # 转为灰度图并应用高斯模糊
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (blur_ksize, blur_ksize), 0)
    
    roi_height, roi_width = blurred.shape
    
    # 定义水相关区域 - 图片底部区域，使用配置参数
    water_region_height = min(
        contrast_params['water_region_min_height'], 
        int(roi_height * contrast_params['water_region_height_factor'])
    )
    water_region_start_y = roi_height - water_region_height
    water_region = blurred[water_region_start_y:roi_height, :]
    
    # 计算水区域统计特性
    water_mean = np.mean(water_region)
    water_std = np.std(water_region)
    
    print(f"[透明度热力图] 水区域统计: 均值={water_mean:.2f}, 标准差={water_std:.2f}")
    
    # 计算综合透明度分数 (0-100, 100表示最透明)
    transparency_score = np.zeros((roi_height, roi_width), dtype=np.float32)
    
    # 1. 基于亮度的透明度分数 (权重: 40%)
    # 水面通常较亮，但要考虑水区域的统计特性
    brightness_score = blurred.astype(np.float32)
    # 归一化到0-100
    brightness_score = (brightness_score / 255.0) * 100
    
    # 2. 基于局部对比度的透明度分数 (权重: 30%)
    # 透明水面通常对比度较低
    kernel = np.ones((9, 9), np.float32) / 81
    local_mean = cv2.filter2D(blurred.astype(np.float32), -1, kernel)
    local_variance = cv2.filter2D((blurred.astype(np.float32) - local_mean) ** 2, -1, kernel)
    local_std = np.sqrt(local_variance)
    
    # 对比度分数：标准差越小，透明度越高
    contrast_score = 100 - np.clip((local_std / np.max(local_std)) * 100, 0, 100)
    
    # 3. 基于梯度的透明度分数 (权重: 30%)
    # 透明区域梯度变化通常较小
    sobelx = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = cv2.magnitude(sobelx, sobely)
    
    # 梯度分数：梯度越小，透明度越高
    gradient_score = 100 - np.clip((gradient_magnitude / np.max(gradient_magnitude)) * 100, 0, 100)
    
    # 4. 基于水区域相对位置的透明度分数 (权重: 可选)
    # 靠近水区域的像素更可能是透明的
    position_weight = np.ones((roi_height, roi_width), dtype=np.float32)
    for y in range(roi_height):
        if y >= water_region_start_y:
            # 在水区域内，权重递增
            relative_pos = (y - water_region_start_y) / water_region_height
            position_weight[y, :] = 1.0 + relative_pos * 0.3  # 最多增加30%权重
        else:
            # 在水区域外，根据距离递减权重
            distance_factor = (water_region_start_y - y) / water_region_start_y
            position_weight[y, :] = 1.0 - distance_factor * 0.5  # 最多减少50%权重
    
    # 综合透明度分数计算
    transparency_score = (
        brightness_score * 0.4 +        # 亮度权重40%
        contrast_score * 0.3 +          # 对比度权重30%
        gradient_score * 0.3            # 梯度权重30%
    ) * position_weight
    
    # 确保分数在0-100范围内
    transparency_score = np.clip(transparency_score, 0, 100)
    
    # 生成热力图
    # 将透明度分数转换为0-255范围用于colormap
    heatmap_data = (transparency_score / 100.0 * 255).astype(np.uint8)
    
    # 使用JET colormap生成热力图 (蓝色=低透明度, 红色=高透明度)
    heatmap_colored = cv2.applyColorMap(heatmap_data, cv2.COLORMAP_JET)
    
    # 创建叠加图像
    overlay_result = roi_image.copy()
    
    # 将热力图半透明叠加到原图上
    alpha = 0.6  # 热力图透明度
    beta = 1.0 - alpha  # 原图透明度
    overlay_result = cv2.addWeighted(overlay_result, beta, heatmap_colored, alpha, 0)
    
    # 添加透明度刻度条
    scale_height = 30
    scale_width = roi_width - 40
    scale_x = 20
    scale_y = roi_height - scale_height - 10
    
    # 创建刻度条
    scale_data = np.linspace(0, 255, scale_width).astype(np.uint8)
    scale_data = np.tile(scale_data, (scale_height, 1))
    scale_colored = cv2.applyColorMap(scale_data, cv2.COLORMAP_JET)
    
    # 将刻度条添加到叠加图像
    overlay_result[scale_y:scale_y+scale_height, scale_x:scale_x+scale_width] = scale_colored
    
    # 添加刻度标记
    cv2.putText(overlay_result, "0", 
                (scale_x - 10, scale_y + scale_height + 15), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(overlay_result, "50", 
                (scale_x + scale_width//2 - 10, scale_y + scale_height + 15), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(overlay_result, "100", 
                (scale_x + scale_width - 20, scale_y + scale_height + 15), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(overlay_result, "Transparency Score", 
                (scale_x, scale_y - 10), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加水区域边界标记
    cv2.line(overlay_result, (0, water_region_start_y), (roi_width, water_region_start_y), 
             (255, 255, 255), 2)
    cv2.putText(overlay_result, "Water Region Boundary", 
                (10, water_region_start_y - 10), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 创建带网格的版本
    overlay_with_grid = overlay_result.copy()
    heatmap_with_grid = heatmap_colored.copy()
    
    # 网格参数
    grid_spacing_x = max(50, roi_width // 10)  # 水平网格间距，最少50像素
    grid_spacing_y = max(50, roi_height // 10)  # 垂直网格间距，最少50像素
    grid_color = (255, 255, 255)  # 白色网格线
    grid_thickness = 1
    
    # 绘制垂直网格线
    for x in range(0, roi_width, grid_spacing_x):
        cv2.line(overlay_with_grid, (x, 0), (x, roi_height), grid_color, grid_thickness)
        cv2.line(heatmap_with_grid, (x, 0), (x, roi_height), grid_color, grid_thickness)
        
        # 添加x坐标标注（每隔2条线标注一次，避免过密）
        if x % (grid_spacing_x * 2) == 0:
            cv2.putText(overlay_with_grid, f"{x}", (x + 2, 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, grid_color, 1)
            cv2.putText(heatmap_with_grid, f"{x}", (x + 2, 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, grid_color, 1)
    
    # 绘制水平网格线
    for y in range(0, roi_height, grid_spacing_y):
        cv2.line(overlay_with_grid, (0, y), (roi_width, y), grid_color, grid_thickness)
        cv2.line(heatmap_with_grid, (0, y), (roi_width, y), grid_color, grid_thickness)
        
        # 添加y坐标标注（每隔2条线标注一次，避免过密）
        if y % (grid_spacing_y * 2) == 0 and y > 10:  # 避免与x坐标重叠
            cv2.putText(overlay_with_grid, f"{y}", (2, y - 2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, grid_color, 1)
            cv2.putText(heatmap_with_grid, f"{y}", (2, y - 2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, grid_color, 1)
    
    # 特别标记水区域边界线（更粗的线）
    cv2.line(overlay_with_grid, (0, water_region_start_y), (roi_width, water_region_start_y), 
             (0, 255, 255), 3)  # 黄色粗线
    cv2.line(heatmap_with_grid, (0, water_region_start_y), (roi_width, water_region_start_y), 
             (0, 255, 255), 3)  # 黄色粗线
    
    # 在网格版本上添加网格信息标注
    grid_info_text = f"Grid: {grid_spacing_x}x{grid_spacing_y}px"
    cv2.putText(overlay_with_grid, grid_info_text, (10, roi_height - 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(heatmap_with_grid, grid_info_text, (10, roi_height - 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 生成网格区域统计
    grid_stats_csv_filename = "transparency_grid_stats.csv"
    with open(grid_stats_csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['grid_x', 'grid_y', 'start_x', 'end_x', 'start_y', 'end_y', 
                     'mean_transparency', 'std_transparency', 'min_transparency', 'max_transparency',
                     'pixel_count', 'is_water_region']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        grid_x_idx = 0
        for x in range(0, roi_width, grid_spacing_x):
            grid_y_idx = 0
            for y in range(0, roi_height, grid_spacing_y):
                # 定义网格区域边界
                end_x = min(x + grid_spacing_x, roi_width)
                end_y = min(y + grid_spacing_y, roi_height)
                
                # 提取该网格区域的透明度分数
                grid_region = transparency_score[y:end_y, x:end_x]
                
                if grid_region.size > 0:
                    # 计算统计信息
                    mean_trans = np.mean(grid_region)
                    std_trans = np.std(grid_region)
                    min_trans = np.min(grid_region)
                    max_trans = np.max(grid_region)
                    pixel_count = grid_region.size
                    is_water = y >= water_region_start_y
                    
                    writer.writerow({
                        'grid_x': grid_x_idx,
                        'grid_y': grid_y_idx,
                        'start_x': x,
                        'end_x': end_x,
                        'start_y': y,
                        'end_y': end_y,
                        'mean_transparency': f"{mean_trans:.2f}",
                        'std_transparency': f"{std_trans:.2f}",
                        'min_transparency': f"{min_trans:.2f}",
                        'max_transparency': f"{max_trans:.2f}",
                        'pixel_count': pixel_count,
                        'is_water_region': is_water
                    })
                
                grid_y_idx += 1
            grid_x_idx += 1
    
    # 保存各种可视化结果
    cv2.imwrite("debug_transparency_heatmap_overlay.jpg", overlay_result)
    cv2.imwrite("debug_transparency_heatmap_overlay_with_grid.jpg", overlay_with_grid)
    cv2.imwrite("debug_transparency_heatmap_pure.jpg", heatmap_colored)
    cv2.imwrite("debug_transparency_heatmap_pure_with_grid.jpg", heatmap_with_grid)
    cv2.imwrite("debug_transparency_brightness_component.jpg", 
                cv2.applyColorMap((brightness_score / 100.0 * 255).astype(np.uint8), cv2.COLORMAP_JET))
    cv2.imwrite("debug_transparency_contrast_component.jpg", 
                cv2.applyColorMap((contrast_score / 100.0 * 255).astype(np.uint8), cv2.COLORMAP_JET))
    cv2.imwrite("debug_transparency_gradient_component.jpg", 
                cv2.applyColorMap((gradient_score / 100.0 * 255).astype(np.uint8), cv2.COLORMAP_JET))
    
    # 计算统计信息
    stats = {
        'overall_mean': np.mean(transparency_score),
        'overall_std': np.std(transparency_score),
        'overall_min': np.min(transparency_score),
        'overall_max': np.max(transparency_score),
        'water_region_mean': np.mean(transparency_score[water_region_start_y:, :]),
        'water_region_std': np.std(transparency_score[water_region_start_y:, :]),
        'non_water_region_mean': np.mean(transparency_score[:water_region_start_y, :]),
        'non_water_region_std': np.std(transparency_score[:water_region_start_y, :]),
        'high_transparency_pixels': np.sum(transparency_score > 70),
        'medium_transparency_pixels': np.sum((transparency_score > 30) & (transparency_score <= 70)),
        'low_transparency_pixels': np.sum(transparency_score <= 30),
        'water_region_start_y': water_region_start_y,
        'water_region_height': water_region_height
    }
    
    # 保存详细统计信息
    stats_filename = "transparency_heatmap_stats.txt"
    with open(stats_filename, 'w', encoding='utf-8') as f:
        f.write("透明度热力图统计信息\n")
        f.write("=" * 50 + "\n")
        f.write(f"整体透明度分数:\n")
        f.write(f"  均值: {stats['overall_mean']:.2f}\n")
        f.write(f"  标准差: {stats['overall_std']:.2f}\n")
        f.write(f"  最小值: {stats['overall_min']:.2f}\n")
        f.write(f"  最大值: {stats['overall_max']:.2f}\n\n")
        
        f.write(f"水区域透明度分数 (y>={water_region_start_y}):\n")
        f.write(f"  均值: {stats['water_region_mean']:.2f}\n")
        f.write(f"  标准差: {stats['water_region_std']:.2f}\n\n")
        
        f.write(f"非水区域透明度分数 (y<{water_region_start_y}):\n")
        f.write(f"  均值: {stats['non_water_region_mean']:.2f}\n")
        f.write(f"  标准差: {stats['non_water_region_std']:.2f}\n\n")
        
        f.write(f"透明度分布:\n")
        f.write(f"  高透明度像素 (>70): {stats['high_transparency_pixels']}\n")
        f.write(f"  中等透明度像素 (30-70): {stats['medium_transparency_pixels']}\n")
        f.write(f"  低透明度像素 (≤30): {stats['low_transparency_pixels']}\n\n")
        
        f.write(f"建议的透明度阈值范围:\n")
        mean_water = stats['water_region_mean']
        std_water = stats['water_region_std']
        f.write(f"  基于水区域统计的建议范围: {mean_water - std_water:.1f} - {mean_water + std_water:.1f}\n")
        f.write(f"  保守阈值 (均值-0.5*标准差): {mean_water - 0.5*std_water:.1f}\n")
        f.write(f"  激进阈值 (均值+0.5*标准差): {mean_water + 0.5*std_water:.1f}\n")
    
    print(f"[透明度热力图] 已保存可视化结果:")
    print(f"  - 热力图叠加: debug_transparency_heatmap_overlay.jpg")
    print(f"  - 热力图叠加(带网格): debug_transparency_heatmap_overlay_with_grid.jpg")
    print(f"  - 纯热力图: debug_transparency_heatmap_pure.jpg")
    print(f"  - 纯热力图(带网格): debug_transparency_heatmap_pure_with_grid.jpg")
    print(f"  - 各组件热力图: debug_transparency_*_component.jpg")
    print(f"  - 统计信息: {stats_filename}")
    print(f"  - 网格统计: {grid_stats_csv_filename}")
    print(f"[透明度热力图] 网格大小: {grid_spacing_x}x{grid_spacing_y}px")
    print(f"[透明度热力图] 整体均值: {stats['overall_mean']:.2f}, 水区域均值: {stats['water_region_mean']:.2f}")
    
    return overlay_result, transparency_score, stats

def find_water_surface_transition(
    roi_image: np.ndarray,
    transparency_mask: np.ndarray,
    gradient_magnitude: np.ndarray,
    max_contour: np.ndarray,
    x: int, y: int, w: int, h: int,
    debug: bool = False
) -> int:
    """
    通过分析轮廓掩码中黑色像素的水平分布来检测水面线
    计算每行的变化率和平均变化率，找到符合条件的行作为水面线
    
    Args:
        roi_image: ROI图像
        transparency_mask: 透明度掩码
        gradient_magnitude: 梯度幅值图
        max_contour: 最大轮廓
        x, y, w, h: 轮廓边界框
        debug: 是否保存调试图像和数据
        
    Returns:
        水面线y坐标，如果检测失败返回None
    """
    try:
        # 1. 创建轮廓掩码
        contour_mask = np.zeros(roi_image.shape[:2], dtype=np.uint8)
        cv2.drawContours(contour_mask, [max_contour], -1, 255, -1)  # 填充轮廓
        if debug:
            cv2.imwrite("debug_trans_contour_mask.jpg", contour_mask)
        
        # 2. 计算每一行的黑色像素数量 - 使用numpy向量化计算
        roi_height = roi_image.shape[0]
        roi_width = roi_image.shape[1]
        
        # 向量化计算黑色像素数量
        black_pixels_data = np.sum(contour_mask == 0, axis=1)  # 每行黑色像素数量
        
        # 3. 使用numpy向量化计算变化率和平均变化率
        if len(black_pixels_data) < 10:
            return None
        
        # 创建数组存储计算结果
        n_rows = len(black_pixels_data)
        avg_before = np.full(n_rows, np.nan)
        avg_after = np.full(n_rows, np.nan)
        change_rate = np.full(n_rows, np.nan)
        avg_change_rate = np.full(n_rows, np.nan)
        
        # 向量化计算前5行和后5行的平均值
        for i in range(4, n_rows - 4):
            avg_before[i] = np.mean(black_pixels_data[i-4:i+1])
            avg_after[i] = np.mean(black_pixels_data[i:i+5])
            
            if avg_before[i] > 0:
                change_rate[i] = (avg_before[i] - avg_after[i]) / avg_before[i]
        
        # 向量化计算平均变化率（当前行和后2行的change_rate平均值）
        for i in range(4, n_rows - 6):  # 确保后面还有2行
            valid_rates = change_rate[i:i+3]
            valid_rates = valid_rates[~np.isnan(valid_rates)]
            if len(valid_rates) > 0:
                avg_change_rate[i] = np.mean(valid_rates)
        
        # 4. 过滤条件
        # 4.1 计算黑色像素占比
        black_pixel_ratio = black_pixels_data / roi_width
        
        # 4.2 找到符合条件的行：黑色像素占比 <= 阈值 且 平均变化率 >= 阈值
        valid_mask = (
            (black_pixel_ratio <= DEFAULT_BLACK_PIXEL_RATIO_THRESH) & 
            (avg_change_rate >= DEFAULT_AVG_CHANGE_RATE_THRESH) &
            (~np.isnan(avg_change_rate))
        )
        
        valid_indices = np.where(valid_mask)[0]
        
        # 5. 保存分析数据到CSV文件（仅debug模式）
        if debug:
            csv_filename = "contour_black_pixels_analysis.csv"
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['row_y', 'black_pixels', 'black_pixel_ratio', 'avg_before', 'avg_after', 'change_rate', 'avg_change_rate', 'is_valid']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for i in range(n_rows):
                    writer.writerow({
                        'row_y': i,
                        'black_pixels': black_pixels_data[i],
                        'black_pixel_ratio': black_pixel_ratio[i],
                        'avg_before': avg_before[i] if not np.isnan(avg_before[i]) else None,
                        'avg_after': avg_after[i] if not np.isnan(avg_after[i]) else None,
                        'change_rate': change_rate[i] if not np.isnan(change_rate[i]) else None,
                        'avg_change_rate': avg_change_rate[i] if not np.isnan(avg_change_rate[i]) else None,
                        'is_valid': i in valid_indices
                    })
            print(f"黑色像素分析数据已保存到: {csv_filename}")
        
        # 6. 创建可视化图像
        if debug:
            debug_img = roi_image.copy()
            
            # 绘制黑色像素数量变化
            max_black_pixels = np.max(black_pixels_data) if len(black_pixels_data) > 0 else 1
            
            for i in range(0, len(black_pixels_data), 20):  # 每20行绘制一次
                black_count = black_pixels_data[i]
                line_length = int(80 * (black_count / max_black_pixels)) if max_black_pixels > 0 else 0
                color = (0, 255, 0) if black_count > 10 else (0, 0, 255)
                
                cv2.line(debug_img, (roi_width - 100, i), 
                        (roi_width - 100 + line_length, i), color, 2)
                
                if i % 100 == 0:
                    cv2.putText(debug_img, f"{black_count}", 
                              (roi_width - 90, i), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            
            # 标注符合条件的行
            for idx in valid_indices:
                cv2.circle(debug_img, (roi_width - 50, idx), 5, (255, 0, 255), -1)
                cv2.putText(debug_img, f"AR:{avg_change_rate[idx]:.2f}", 
                          (roi_width - 45, idx), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
            
            cv2.imwrite("debug_trans_black_pixels_analysis.jpg", debug_img)
        
        # 7. 计算水面线位置
        if len(valid_indices) > 0:
            # 计算符合条件的行的y坐标平均值
            water_line_y = int(np.mean(valid_indices))
            
            if debug:
                print(f"找到水面转换点:")
                print(f"  符合条件的行数: {len(valid_indices)}")
                print(f"  符合条件的行: {valid_indices}")
                print(f"  水面线位置: y={water_line_y}")
                print(f"  黑色像素占比阈值: {DEFAULT_BLACK_PIXEL_RATIO_THRESH}")
                print(f"  平均变化率阈值: {DEFAULT_AVG_CHANGE_RATE_THRESH}")
            
            return water_line_y
        
        return None
    
    except Exception as e:
        if debug:
            print(f"黑色像素分析出错: {e}")
        return None

def visualize_contours_with_fill(
    roi_image: np.ndarray,
    valid_contours: list,
    method_name: str = "Unknown Method",
    save_path: str = None
) -> np.ndarray:
    """
    独立的轮廓可视化方法：画出前三个最大轮廓的边界，并对最大轮廓进行透明填充
    
    Args:
        roi_image: ROI图像
        valid_contours: 有效轮廓列表
        method_name: 方法名称，用于图像标注
        save_path: 保存路径，如果为None则使用默认路径
        
    Returns:
        可视化结果图像
    """
    if not valid_contours:
        return roi_image.copy()
    
    # 按面积从大到小排序轮廓，取前三个
    sorted_contours = sorted(valid_contours, key=cv2.contourArea, reverse=True)
    top_three_contours = sorted_contours[:3]
    
    # 定义颜色
    colors = [
        (0, 0, 255),    # 红色 - 最大轮廓
        (0, 255, 0),    # 绿色 - 第二大轮廓  
        (255, 0, 0),    # 蓝色 - 第三大轮廓
    ]
    
    # 创建结果图像
    result_img = roi_image.copy()
    
    # 创建透明叠加层用于最大轮廓填充
    transparent_overlay = np.zeros_like(roi_image, dtype=np.uint8)
    
    # 处理前三个轮廓
    for i, contour in enumerate(top_three_contours):
        color = colors[i % len(colors)]
        area = cv2.contourArea(contour)
        x, y, w, h = cv2.boundingRect(contour)
        
        if i == 0:  # 最大轮廓：透明填充 + 边界
            # 透明填充（使用与边界不同的颜色 - 绿色）
            cv2.drawContours(transparent_overlay, [contour], 0, (0, 255, 0), -1)
            # 边界线（红色，4像素宽）
            cv2.drawContours(result_img, [contour], 0, color, 4)
        else:  # 其他轮廓：只画边界
            cv2.drawContours(result_img, [contour], 0, color, 4)
        
        # 标注
        cv2.putText(result_img, f"#{i+1}", (x, y-25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        cv2.putText(result_img, f"Area: {int(area)}", (x, y-5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    # 混合透明叠加层
    if len(top_three_contours) > 0:
        result_img = cv2.addWeighted(result_img, 1.0, transparent_overlay, 0.3, 0)
    
    # 添加方法名称标注
    cv2.putText(result_img, method_name, (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.putText(result_img, f"Valid Contours: {len(valid_contours)}", (10, 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 保存图像
    if save_path is None:
        save_path = f"debug_contours_{method_name.replace(' ', '_').lower()}.jpg"
    cv2.imwrite(save_path, result_img)
    
    return result_img

def generate_water_region_transparency_csv(
    blurred_image: np.ndarray,
    transparency_mask: np.ndarray,
    water_region_start_y: int,
    roi_width: int,
    roi_height: int,
    scheme_name: str
) -> None:
    """
    生成水区域内每个像素的透明度数据CSV文件
    
    Args:
        blurred_image: 模糊后的灰度图像
        transparency_mask: 透明度掩码
        water_region_start_y: 水区域开始的y坐标
        roi_width: ROI宽度
        roi_height: ROI高度
        scheme_name: 方案名称
    """
    csv_filename = f"water_region_transparency_{scheme_name}.csv"
    
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['x', 'y', 'gray_value', 'transparency_mask_value', 'is_in_water_region', 'relative_y_in_water_region']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # 遍历整个ROI区域
        for y in range(roi_height):
            for x in range(roi_width):
                # 获取灰度值
                gray_value = int(blurred_image[y, x])
                
                # 获取透明度掩码值
                mask_value = int(transparency_mask[y, x])
                
                # 判断是否在水区域内
                is_in_water_region = y >= water_region_start_y
                
                # 计算在水区域内的相对y坐标
                relative_y_in_water_region = y - water_region_start_y if is_in_water_region else -1
                
                writer.writerow({
                    'x': x,
                    'y': y,
                    'gray_value': gray_value,
                    'transparency_mask_value': mask_value,
                    'is_in_water_region': is_in_water_region,
                    'relative_y_in_water_region': relative_y_in_water_region
                })
    
    print(f"水区域透明度数据已保存到: {csv_filename}")
    
    # 额外生成仅包含水区域内像素的CSV文件
    water_only_csv_filename = f"water_region_only_transparency_{scheme_name}.csv"
    
    with open(water_only_csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['x', 'y', 'gray_value', 'transparency_mask_value', 'relative_y_in_water_region']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # 仅遍历水区域
        for y in range(water_region_start_y, roi_height):
            for x in range(roi_width):
                # 获取灰度值
                gray_value = int(blurred_image[y, x])
                
                # 获取透明度掩码值
                mask_value = int(transparency_mask[y, x])
                
                # 计算在水区域内的相对y坐标
                relative_y_in_water_region = y - water_region_start_y
                
                writer.writerow({
                    'x': x,
                    'y': y,
                    'gray_value': gray_value,
                    'transparency_mask_value': mask_value,
                    'relative_y_in_water_region': relative_y_in_water_region
                })
    
    print(f"仅水区域透明度数据已保存到: {water_only_csv_filename}")
    
    # 生成水区域透明度统计信息
    water_region = blurred_image[water_region_start_y:roi_height, :]
    water_mask_region = transparency_mask[water_region_start_y:roi_height, :]
    
    # 计算统计信息
    total_water_pixels = water_region.size
    transparent_pixels = np.sum(water_mask_region == 255)
    transparent_ratio = transparent_pixels / total_water_pixels if total_water_pixels > 0 else 0
    
    avg_gray_in_water = np.mean(water_region)
    std_gray_in_water = np.std(water_region)
    
    # 保存统计信息
    stats_filename = f"water_region_stats_{scheme_name}.txt"
    with open(stats_filename, 'w', encoding='utf-8') as f:
        f.write(f"水区域透明度统计信息 ({scheme_name} 方案)\n")
        f.write("=" * 50 + "\n")
        f.write(f"水区域范围: y={water_region_start_y} 到 y={roi_height-1}\n")
        f.write(f"水区域高度: {roi_height - water_region_start_y} 像素\n")
        f.write(f"水区域宽度: {roi_width} 像素\n")
        f.write(f"总像素数: {total_water_pixels}\n")
        f.write(f"透明像素数: {transparent_pixels}\n")
        f.write(f"透明度比例: {transparent_ratio:.3f} ({transparent_ratio*100:.1f}%)\n")
        f.write(f"水区域平均灰度值: {avg_gray_in_water:.2f}\n")
        f.write(f"水区域灰度标准差: {std_gray_in_water:.2f}\n")
    
    print(f"水区域统计信息已保存到: {stats_filename}")

def waterline_transparency_local(
    roi_image: np.ndarray,
    alpha_thresh: int = None,
    canny_low: int = None,
    canny_high: int = None,
    blur_ksize: int = None,
    morph_kernel_size: int = None,
    min_area_factor: float = None,
    brightness_thresh: int = None,
    transparency_scheme: str = "adaptive_water",
    contrast_params: dict = None,
    debug: bool = False
    ) -> int:
        """
        使用透明度特性检测水面线，主要利用水面较浅较透明的特性
        
        Args:
            roi_image: ROI图像
            alpha_thresh: 透明度阈值
            canny_low: Canny边缘检测低阈值
            canny_high: Canny边缘检测高阈值
            blur_ksize: 高斯模糊核大小
            morph_kernel_size: 形态学操作的核大小
            min_area_factor: 最小区域因子（相对于ROI面积）
            brightness_thresh: 亮度阈值，用于区分透明水面和非水面
            transparency_scheme: 透明度检测方案，可选：
                - "adaptive_water": 基于水区域自适应阈值（默认）
                - "fixed_dual": 固定双阈值
                - "pure_adaptive": 纯自适应阈值
                - "otsu_water": 基于水区域的Otsu阈值
                - "multiscale_adaptive": 多尺度自适应
                - "edge_enhanced": 边缘增强
                - "contrast_change": 基于对比度变化检测（新方案）
            debug: 是否保存调试图像
            
        Returns:
            水面线在ROI中的y坐标（局部坐标），如果未检测到则返回None
        """
        # 使用配置文件的默认值
        if alpha_thresh is None:
            alpha_thresh = DEFAULT_ALPHA_THRESH
        if canny_low is None:
            canny_low = DEFAULT_CANNY_LOW
        if canny_high is None:
            canny_high = DEFAULT_CANNY_HIGH
        if blur_ksize is None:
            blur_ksize = DEFAULT_BLUR_KSIZE
        if morph_kernel_size is None:
            morph_kernel_size = DEFAULT_MORPH_KERNEL_SIZE
        if min_area_factor is None:
            min_area_factor = DEFAULT_MIN_AREA_FACTOR
        if brightness_thresh is None:
            brightness_thresh = DEFAULT_BRIGHTNESS_THRESH
        
        # 如果使用对比度变化方案，直接调用新函数
        if transparency_scheme == "contrast_change":
            waterline_y, top_5_changes, stats = detect_waterline_by_contrast_change(
                roi_image, blur_ksize=blur_ksize, debug=debug
            )
            return waterline_y
        
        # 在透明度处理之前生成热力图可视化 (仅debug模式)
        if debug:
            print(f"[Debug] 开始生成透明度热力图可视化...")
            heatmap_overlay, transparency_scores, heatmap_stats = visualize_transparency_heatmap(
                roi_image, blur_ksize, contrast_params, debug
            )
        
        # 转为灰度图
        gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
        
        # 应用高斯模糊减少噪点
        blurred = cv2.GaussianBlur(gray, (blur_ksize, blur_ksize), 0)
        
        if debug:
            cv2.imwrite("debug_trans_gray_blurred.jpg", blurred)
        
        # 定义水相关区域 - 图片底部高度约200的区域
        roi_height, roi_width = blurred.shape
        water_region_height = min(200, roi_height // 2)  # 最多不超过图片高度的一半
        water_region_start_y = roi_height - water_region_height
        
        # 提取底部水相关区域
        water_region = blurred[water_region_start_y:roi_height, :]
        
        # 计算水区域的统计特性
        water_mean = np.mean(water_region)
        water_std = np.std(water_region)
        water_median = np.median(water_region)
        
        if debug:
            water_region_bgr = cv2.cvtColor(water_region, cv2.COLOR_GRAY2BGR)
            cv2.imwrite("debug_trans_water_region.jpg", water_region_bgr)
            print(f"水相关区域: y={water_region_start_y}到{roi_height}, 高度={water_region_height}")
            print(f"水区域统计: 均值={water_mean:.1f}, 标准差={water_std:.1f}, 中位数={water_median:.1f}")
            print(f"使用透明度方案: {transparency_scheme}")
        
        # 根据选择的方案生成透明度掩码
        if transparency_scheme == "adaptive_water":
            # 方案1：基于水区域自适应阈值（原方案）
            if water_mean > brightness_thresh:
                adaptive_brightness_thresh = int(water_mean + water_std * 0.5)
                lower_brightness_thresh = int(water_mean - water_std * 0.3)
            else:
                adaptive_brightness_thresh = int(water_mean + water_std * 0.3)
                lower_brightness_thresh = int(max(water_mean - water_std * 0.5, 30))
            
            adaptive_brightness_thresh = max(min(adaptive_brightness_thresh, 255), 50)
            lower_brightness_thresh = max(min(lower_brightness_thresh, adaptive_brightness_thresh - 10), 20)
            
            _, bright_mask_high = cv2.threshold(blurred, adaptive_brightness_thresh, 255, cv2.THRESH_BINARY)
            _, bright_mask_low = cv2.threshold(blurred, lower_brightness_thresh, 255, cv2.THRESH_BINARY)
            
            adaptive_mask = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, -3
            )
            
            combined_basic = cv2.bitwise_or(bright_mask_high, bright_mask_low)
            water_adaptive_mask = adaptive_mask.copy()
            water_adaptive_mask[:water_region_start_y, :] = 0
            
            bright_mask = cv2.bitwise_or(combined_basic, water_adaptive_mask)
            
        elif transparency_scheme == "fixed_dual":
            # 方案2：固定双阈值
            _, bright_mask_high = cv2.threshold(blurred, 120, 255, cv2.THRESH_BINARY)
            _, bright_mask_low = cv2.threshold(blurred, 60, 255, cv2.THRESH_BINARY)
            bright_mask = cv2.bitwise_or(bright_mask_high, bright_mask_low)
            
        elif transparency_scheme == "pure_adaptive":
            # 方案3：纯自适应阈值
            bright_mask = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, -2
            )
            
        elif transparency_scheme == "otsu_water":
            # 方案4：基于水区域的Otsu阈值
            otsu_thresh, _ = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            water_otsu_thresh, _ = cv2.threshold(water_region, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            combined_thresh = int((otsu_thresh + water_otsu_thresh) / 2)
            _, bright_mask = cv2.threshold(blurred, combined_thresh, 255, cv2.THRESH_BINARY)
            
        elif transparency_scheme == "multiscale_adaptive":
            # 方案5：多尺度自适应
            mask1 = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, -2)
            mask2 = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 21, -3)
            mask3 = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 31, -4)
            
            bright_mask = cv2.bitwise_or(mask1, mask2)
            bright_mask = cv2.bitwise_or(bright_mask, mask3)
            
        elif transparency_scheme == "edge_enhanced":
            # 方案6：边缘增强的透明度检测
            sobelx = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
            sobely = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
            gradient_mag = cv2.magnitude(sobelx, sobely)
            gradient_mag = cv2.normalize(gradient_mag, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
            
            _, edge_mask = cv2.threshold(gradient_mag, 30, 255, cv2.THRESH_BINARY)
            _, bright_mask_basic = cv2.threshold(blurred, int(water_mean + water_std * 0.2), 255, cv2.THRESH_BINARY)
            
            bright_mask = cv2.bitwise_or(edge_mask, bright_mask_basic)
            
        else:
            # 默认回退到adaptive_water方案
            if debug:
                print(f"未知方案 '{transparency_scheme}'，回退到 'adaptive_water'")
            
            if water_mean > brightness_thresh:
                adaptive_brightness_thresh = int(water_mean + water_std * 0.5)
                lower_brightness_thresh = int(water_mean - water_std * 0.3)
            else:
                adaptive_brightness_thresh = int(water_mean + water_std * 0.3)
                lower_brightness_thresh = int(max(water_mean - water_std * 0.5, 30))
            
            adaptive_brightness_thresh = max(min(adaptive_brightness_thresh, 255), 50)
            lower_brightness_thresh = max(min(lower_brightness_thresh, adaptive_brightness_thresh - 10), 20)
            
            _, bright_mask_high = cv2.threshold(blurred, adaptive_brightness_thresh, 255, cv2.THRESH_BINARY)
            _, bright_mask_low = cv2.threshold(blurred, lower_brightness_thresh, 255, cv2.THRESH_BINARY)
            
            adaptive_mask = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, -3
            )
            
            combined_basic = cv2.bitwise_or(bright_mask_high, bright_mask_low)
            water_adaptive_mask = adaptive_mask.copy()
            water_adaptive_mask[:water_region_start_y, :] = 0
            
            bright_mask = cv2.bitwise_or(combined_basic, water_adaptive_mask)
        
        if debug:
            cv2.imwrite(f"debug_trans_bright_mask_{transparency_scheme}.jpg", bright_mask)
        
        # 计算透明度差异图 - 利用水与非水区域明亮度和纹理差异
        # 创建自适应阈值，寻找局部对比度高的区域
        adapt_thresh = cv2.adaptiveThreshold(
            blurred,
            255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY,
            11,
            2
        )
        
        if debug:
            cv2.imwrite("debug_trans_adapt_thresh.jpg", adapt_thresh)
        
        # 改进的透明度掩码组合策略
        # 1. 使用亮度掩码作为基础
        transparency_mask = bright_mask.copy()
        
        # 2. 结合自适应阈值结果 - 这是关键改进点
        # 自适应阈值能很好识别水面，应该被充分利用
        combined_mask = cv2.bitwise_or(transparency_mask, adapt_thresh)
        
        if debug:
            cv2.imwrite("debug_trans_combined_before_morph.jpg", combined_mask)
        
        # 3. 优化的形态学操作 - 使用更小的核和更温和的操作
        # 减小核大小以保留更多细节
        small_kernel = np.ones((max(morph_kernel_size - 2, 3), max(morph_kernel_size - 2, 3)), np.uint8)
        
        # 先进行轻微的开运算去除小噪点，但保留主要结构
        transparency_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, small_kernel, iterations=1)
        
        # 再进行闭运算连接断开的区域
        transparency_mask = cv2.morphologyEx(transparency_mask, cv2.MORPH_CLOSE, small_kernel, iterations=1)
        
        # 可选：如果结果太碎片化，可以用稍大的核进行一次闭运算
        if np.sum(transparency_mask > 0) < roi_image.shape[0] * roi_image.shape[1] * 0.05:  # 如果白色区域太少
            larger_kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
            transparency_mask = cv2.morphologyEx(transparency_mask, cv2.MORPH_CLOSE, larger_kernel, iterations=1)
        
        if debug:
            cv2.imwrite("debug_trans_mask_morph.jpg", transparency_mask)
        
        # 应用Canny边缘检测
        edges = cv2.Canny(blurred, canny_low, canny_high)
        
        if debug:
            cv2.imwrite("debug_trans_edges.jpg", edges)
        
        # 计算垂直方向的梯度变化（Sobel算子）
        # 水面通常有水平梯度变化（水平线）
        sobelx = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
        
        # 计算梯度幅值，这个图像中值较大的区域表示透明度变化明显的区域
        gradient_magnitude = cv2.magnitude(sobelx, sobely)
        gradient_magnitude = cv2.normalize(gradient_magnitude, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
        
        if debug:
            cv2.imwrite("debug_trans_gradient_magnitude.jpg", gradient_magnitude)
        
        # 找到所有轮廓
        contours, _ = cv2.findContours(transparency_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 如果没有轮廓，返回None
        if not contours:
            return None
        
        # 过滤掉太小的轮廓
        min_area = roi_image.shape[0] * roi_image.shape[1] * min_area_factor
        valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
        
        if not valid_contours:
            return None
        
        # 使用独立的可视化方法生成调试图像
        if debug and valid_contours:
            # 创建包含水区域框选的可视化图像
            debug_img_with_water_region = roi_image.copy()
            
            # 框出水区域
            cv2.rectangle(debug_img_with_water_region, 
                         (0, water_region_start_y), 
                         (roi_width, roi_height), 
                         (255, 255, 0), 3)  # 青色框
            
            # 添加水区域标注
            cv2.putText(debug_img_with_water_region, 
                       f"Water Region (Height: {water_region_height}px)", 
                       (10, water_region_start_y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
            
            # 添加统计信息
            cv2.putText(debug_img_with_water_region, 
                       f"Mean: {water_mean:.1f}, Std: {water_std:.1f}", 
                       (10, water_region_start_y + 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # 保存带水区域标注的图像
            cv2.imwrite("debug_trans_water_region_marked.jpg", debug_img_with_water_region)
            
            # 生成水区域透明度CSV数据
            generate_water_region_transparency_csv(
                blurred, bright_mask, water_region_start_y, roi_width, roi_height, transparency_scheme
            )
            
            visualize_contours_with_fill(
                roi_image, valid_contours, f"{transparency_scheme}"
            )
        
        # 找到最大面积的轮廓
        max_contour = max(valid_contours, key=cv2.contourArea)
        
        # 获取轮廓的边界框
        x, y, w, h = cv2.boundingRect(max_contour)
        
        # 改进的水面线检测：找到实体水尺和倒影的交接点
        water_line_y = find_water_surface_transition(
            roi_image, transparency_mask, gradient_magnitude, max_contour, x, y, w, h, debug
        )
        
        # 如果智能检测失败，回退到原方法
        if water_line_y is None:
            # 找到所有轮廓中y坐标最大的点（最下沿）作为水面线
            bottom_points = []
            for contour in valid_contours:
                # 找到每个轮廓中y坐标最大的点
                contour_bottom = max(contour[:, 0, 1])
                bottom_points.append(contour_bottom)
            
            # 使用所有轮廓最下沿中的最大值作为水面线
            water_line_y = max(bottom_points) if bottom_points else y + h
        
        # 可视化轮廓和水面线
        result_img = roi_image.copy()
        if debug:
            # 绘制所有有效轮廓
            cv2.drawContours(result_img, valid_contours, -1, (0, 255, 0), 2)
            # 画出最大轮廓的边界框
            cv2.rectangle(result_img, (x, y), (x+w, y+h), (255, 0, 0), 2)
            # 画更粗的水面分界线
            cv2.line(result_img, (0, water_line_y), (result_img.shape[1], water_line_y), (0, 0, 255), 4)
            
            # 标注检测方法
            method_text = "Smart Transition Detection" if water_line_y != (y + h) else "Fallback: Bottom Detection"
            cv2.putText(result_img, method_text, (10, water_line_y - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            cv2.putText(result_img, f"Water line at y={water_line_y}", (10, water_line_y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            cv2.imwrite("debug_trans_water_line.jpg", result_img)
        
        return water_line_y

def visualize_transparency_detection(
    roi_image: np.ndarray,
    alpha_thresh: int = None,
    canny_low: int = None,
    canny_high: int = None,
    blur_ksize: int = None,
    morph_kernel_size: int = None,
    min_area_factor: float = None,
    brightness_thresh: int = None
) -> np.ndarray:
    """
    可视化透明度检测过程的每个步骤
    
    Args:
        roi_image: ROI图像
        alpha_thresh: 透明度阈值
        canny_low: Canny边缘检测低阈值
        canny_high: Canny边缘检测高阈值
        blur_ksize: 高斯模糊核大小
        morph_kernel_size: 形态学操作的核大小
        min_area_factor: 最小区域因子（相对于ROI面积）
        brightness_thresh: 亮度阈值，用于区分透明水面和非水面
        
    Returns:
        包含可视化结果的图像
    """
    # 使用配置文件的默认值
    if alpha_thresh is None:
        alpha_thresh = DEFAULT_ALPHA_THRESH
    if canny_low is None:
        canny_low = DEFAULT_CANNY_LOW
    if canny_high is None:
        canny_high = DEFAULT_CANNY_HIGH
    if blur_ksize is None:
        blur_ksize = DEFAULT_BLUR_KSIZE
    if morph_kernel_size is None:
        morph_kernel_size = DEFAULT_MORPH_KERNEL_SIZE
    if min_area_factor is None:
        min_area_factor = DEFAULT_MIN_AREA_FACTOR
    if brightness_thresh is None:
        brightness_thresh = DEFAULT_BRIGHTNESS_THRESH
    
    # 创建可视化结果图像
    h, w = roi_image.shape[:2]
    result_img = np.zeros((h * 3, w * 2, 3), dtype=np.uint8)
    
    # 1. 原始ROI图像
    result_img[0:h, 0:w] = roi_image.copy()
    cv2.putText(result_img, "1. Original ROI", (10, 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 2. 灰度图
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    gray_bgr = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
    result_img[0:h, w:w*2] = gray_bgr
    cv2.putText(result_img, "2. Grayscale", (w+10, 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 3. 高斯模糊
    blurred = cv2.GaussianBlur(gray, (blur_ksize, blur_ksize), 0)
    blurred_bgr = cv2.cvtColor(blurred, cv2.COLOR_GRAY2BGR)
    result_img[h:h*2, 0:w] = blurred_bgr
    cv2.putText(result_img, "3. Gaussian Blur", (10, h+20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 4. 改进的亮度掩码 - 使用多层次阈值识别更多水尺部分
    _, bright_mask_high = cv2.threshold(blurred, brightness_thresh, 255, cv2.THRESH_BINARY)
    lower_thresh = max(brightness_thresh - 30, 50)
    _, bright_mask_low = cv2.threshold(blurred, lower_thresh, 255, cv2.THRESH_BINARY)
    adaptive_mask = cv2.adaptiveThreshold(
        blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 
        15, -5
    )
    bright_mask = cv2.bitwise_or(bright_mask_high, 
                                cv2.bitwise_and(bright_mask_low, adaptive_mask))
    bright_mask_bgr = cv2.cvtColor(bright_mask, cv2.COLOR_GRAY2BGR)
    result_img[h:h*2, w:w*2] = bright_mask_bgr
    cv2.putText(result_img, f"4. Enhanced Brightness Mask ({lower_thresh}-{brightness_thresh})", (w+10, h+20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 5. 优化的形态学处理后的掩码
    # 首先创建自适应阈值
    adapt_thresh = cv2.adaptiveThreshold(
        blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 
        11, 2
    )
    
    # 结合亮度掩码和自适应阈值
    combined_mask = cv2.bitwise_or(bright_mask, adapt_thresh)
    
    # 使用优化的形态学操作
    small_kernel = np.ones((max(morph_kernel_size - 2, 3), max(morph_kernel_size - 2, 3)), np.uint8)
    transparency_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, small_kernel, iterations=1)
    transparency_mask = cv2.morphologyEx(transparency_mask, cv2.MORPH_CLOSE, small_kernel, iterations=1)
    
    # 如果结果太碎片化，进行额外处理
    if np.sum(transparency_mask > 0) < roi_image.shape[0] * roi_image.shape[1] * 0.05:
        larger_kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
        transparency_mask = cv2.morphologyEx(transparency_mask, cv2.MORPH_CLOSE, larger_kernel, iterations=1)
    
    mask_bgr = cv2.cvtColor(transparency_mask, cv2.COLOR_GRAY2BGR)
    result_img[h*2:h*3, 0:w] = mask_bgr
    cv2.putText(result_img, "5. Optimized Morphological Processing", (10, h*2+20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 6. 最终结果
    final_result = roi_image.copy()
    
    # 找到所有轮廓
    contours, _ = cv2.findContours(transparency_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    min_area = roi_image.shape[0] * roi_image.shape[1] * min_area_factor
    valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
    
    if valid_contours:
        # 找到最大轮廓
        max_contour = max(valid_contours, key=cv2.contourArea)
        cv2.drawContours(final_result, [max_contour], -1, (0, 255, 0), 2)
        
        # 获取轮廓的边界框
        x, y, w_rect, h_rect = cv2.boundingRect(max_contour)
        cv2.rectangle(final_result, (x, y), (x+w_rect, y+h_rect), (255, 0, 0), 2)
        
        # 找到所有轮廓中y坐标最大的点（最下沿）作为水面线
        bottom_points = []
        for contour in valid_contours:
            # 找到每个轮廓中y坐标最大的点
            contour_bottom = max(contour[:, 0, 1])
            bottom_points.append(contour_bottom)
        
        # 使用所有轮廓最下沿中的最大值作为水面线
        water_line_y = max(bottom_points) if bottom_points else y + h_rect
        
        # 绘制水面线
        cv2.line(final_result, (0, water_line_y), (w, water_line_y), (0, 0, 255), 4)
        cv2.putText(final_result, f"Water line at y={water_line_y}", (10, water_line_y-10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    
    result_img[h*2:h*3, w:w*2] = final_result
    cv2.putText(result_img, "6. Final Detection", (w+10, h*2+20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 保存完整的可视化结果
    cv2.imwrite("debug_trans_visualization_steps.jpg", result_img)
    
    return result_img 

def detect_waterline_by_contrast_change(
    roi_image: np.ndarray,
    blur_ksize: int = None,
    threshold_factor: float = None,
    change_rate_threshold: float = None,
    absolute_diff_threshold: float = None,
    min_consecutive_rows: int = None,
    search_window: int = None,
    debug: bool = False
) -> tuple:
    """
    基于对比度分数变化检测水面线
    
    Args:
        roi_image: ROI图像
        blur_ksize: 高斯模糊核大小
        threshold_factor: 统计差距的倍数因子
        change_rate_threshold: 变化率阈值
        absolute_diff_threshold: 绝对差距阈值
        min_consecutive_rows: 连续满足条件的最小行数
        search_window: 从下往上搜索的窗口大小
        debug: 是否保存调试信息
        
    Returns:
        tuple: (水面线y坐标, 前5个变化明显的位置列表, 统计信息字典)
    """
    # 从配置文件获取参数
    contrast_params = get_contrast_change_params()
    transparency_params = get_default_transparency_params()
    
    # 使用配置文件的默认值
    if blur_ksize is None:
        blur_ksize = transparency_params['blur_ksize']
    if threshold_factor is None:
        threshold_factor = contrast_params['threshold_factor']
    if change_rate_threshold is None:
        change_rate_threshold = contrast_params['change_rate_threshold']
    if absolute_diff_threshold is None:
        absolute_diff_threshold = contrast_params['absolute_diff_threshold']
    if min_consecutive_rows is None:
        min_consecutive_rows = contrast_params['min_consecutive_rows']
    if search_window is None:
        search_window = contrast_params['search_window']
    
    # 转为灰度图并应用高斯模糊
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (blur_ksize, blur_ksize), 0)
    
    roi_height, roi_width = blurred.shape
    
    # 定义水相关区域 - 图片底部区域
    water_region_height = min(200, roi_height // 2)
    water_region_start_y = roi_height - water_region_height
    
    # 计算对比度分数矩阵
    kernel = np.ones((9, 9), np.float32) / 81
    local_mean = cv2.filter2D(blurred.astype(np.float32), -1, kernel)
    local_variance = cv2.filter2D((blurred.astype(np.float32) - local_mean) ** 2, -1, kernel)
    local_std = np.sqrt(local_variance)
    
    # 对比度分数：标准差越小，透明度越高，这里我们直接使用local_std作为对比度指标
    # 注意：这里不做归一化，直接使用原始的局部标准差值
    contrast_score = local_std
    
    if debug:
        # 保存对比度分数的可视化
        contrast_normalized = cv2.normalize(contrast_score, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
        contrast_colored = cv2.applyColorMap(contrast_normalized, cv2.COLORMAP_JET)
        cv2.imwrite("debug_contrast_score_raw.jpg", contrast_colored)
    
    # 1. 计算水区域的对比度基准统计
    water_region_contrast = contrast_score[water_region_start_y:roi_height, :]
    water_contrast_mean = np.mean(water_region_contrast)
    water_contrast_std = np.std(water_region_contrast)
    water_contrast_median = np.median(water_region_contrast)
    
    if debug:
        print(f"[对比度检测] 水区域统计:")
        print(f"  范围: y={water_region_start_y} 到 y={roi_height}")
        print(f"  对比度均值: {water_contrast_mean:.3f}")
        print(f"  对比度标准差: {water_contrast_std:.3f}")
        print(f"  对比度中位数: {water_contrast_median:.3f}")
    
    # 2. 计算每行的对比度均值
    row_contrast_means = np.mean(contrast_score, axis=1)

    
    if debug:
        print(f"行对比度分析数据已保存到 debug_row_contrast_analysis.csv")
    
    # 3. 计算差距指标
    # 3.1 统计差距：与水区域基准的差距
    statistical_diff = np.abs(row_contrast_means - water_contrast_mean)
    statistical_threshold = threshold_factor * water_contrast_std
    
    # 3.2 变化率差距：相邻行之间的变化率
    change_rates = np.zeros(roi_height)
    for i in range(1, roi_height):
        if row_contrast_means[i-1] != 0:
            change_rates[i] = abs(row_contrast_means[i] - row_contrast_means[i-1]) / row_contrast_means[i-1]
    
    # 3.3 绝对差距
    absolute_diff = np.abs(row_contrast_means - water_contrast_mean)

    if debug:
        # 保存行对比度均值到CSV文件
        contrast_data = pd.DataFrame({
            'row_index': np.arange(roi_height),
            'contrast_mean': row_contrast_means,
            'statistical_diff': statistical_diff,
            'change_rate': change_rates,
            'absolute_diff': absolute_diff
        })

        # 保存到CSV文件
        contrast_data.to_csv('debug_row_contrast_analysis.csv', index=False)
    
    # 4. 使用简化的检测逻辑：从底部往上找第一个change_rate > 0.1的位置
    waterline_y = None
    change_rate_threshold_simple = 0.1  # 固定阈值0.1
    
    # 从图像底部开始往上搜索
    for y in range(roi_height - 1, -1, -1):  # 从最大Y值开始递减到0
        if change_rates[y] > change_rate_threshold_simple:
            waterline_y = y
            break
    
    # 生成用于调试的数据
    significant_rows = []
    top_5_changes = []
    
    # 找到所有满足条件的行（用于调试显示）
    for y in range(roi_height):
        if change_rates[y] > change_rate_threshold_simple:
            significant_rows.append({
                'y': y,
                'contrast_mean': row_contrast_means[y],
                'statistical_diff': statistical_diff[y],
                'change_rate': change_rates[y],
                'absolute_diff': absolute_diff[y],
                'score': change_rates[y]  # 简化评分，只使用change_rate
            })
    
    # 按change_rate排序，取前5个（用于调试显示）
    significant_rows.sort(key=lambda x: x['change_rate'], reverse=True)
    top_5_changes = significant_rows[:5]
    
    # 7. 生成统计信息
    stats = {
        'water_region_start_y': water_region_start_y,
        'water_region_height': water_region_height,
        'water_contrast_mean': water_contrast_mean,
        'water_contrast_std': water_contrast_std,
        'water_contrast_median': water_contrast_median,
        'change_rate_threshold_used': change_rate_threshold_simple,
        'total_significant_rows': len(significant_rows),
        'detected_waterline_y': waterline_y,
        'detection_method': 'simple_change_rate'
    }
    
    # 8. 调试输出和可视化
    if debug:
        # 保存详细分析数据到CSV
        csv_filename = "contrast_change_analysis.csv"
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['y', 'contrast_mean', 'change_rate', 'is_significant', 'is_waterline']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for y in range(roi_height):
                is_significant = change_rates[y] > change_rate_threshold_simple
                is_waterline = (y == waterline_y)
                
                writer.writerow({
                    'y': y,
                    'contrast_mean': f"{row_contrast_means[y]:.4f}",
                    'change_rate': f"{change_rates[y]:.4f}",
                    'is_significant': is_significant,
                    'is_waterline': is_waterline
                })
        
        print(f"[对比度检测] 分析数据已保存到: {csv_filename}")
        
        # 创建可视化图像
        debug_img = roi_image.copy()
        
        # 绘制水区域边界
        cv2.line(debug_img, (0, water_region_start_y), (roi_width, water_region_start_y), 
                (255, 255, 0), 2)
        cv2.putText(debug_img, "Water Region Boundary", 
                   (10, water_region_start_y - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 标记所有满足change_rate > 0.1的行
        for row in significant_rows:
            y = row['y']
            cv2.circle(debug_img, (roi_width - 30, y), 3, (0, 255, 0), -1)
            cv2.putText(debug_img, f"{row['change_rate']:.3f}", 
                       (roi_width - 25, y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)
        
        # 特别标记前5个change_rate最大的位置
        for i, row in enumerate(top_5_changes):
            y = row['y']
            color = (0, 0, 255) if i == 0 else (255, 0, 255)  # 第一个用红色，其他用紫色
            cv2.circle(debug_img, (roi_width - 60, y), 5, color, -1)
            cv2.putText(debug_img, f"#{i+1}({row['change_rate']:.3f})", 
                       (roi_width - 55, y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
        
        # 绘制检测到的水面线
        if waterline_y is not None:
            cv2.line(debug_img, (0, waterline_y), (roi_width, waterline_y), 
                    (0, 0, 255), 4)
            cv2.putText(debug_img, f"Waterline: y={waterline_y} (change_rate={change_rates[waterline_y]:.3f})", 
                       (10, waterline_y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        else:
            cv2.putText(debug_img, "No waterline detected (no change_rate > 0.1)", 
                       (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # 添加统计信息
        info_y = 30
        cv2.putText(debug_img, f"Method: Simple Change Rate (threshold=0.1)", 
                   (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(debug_img, f"Water Contrast Mean: {water_contrast_mean:.3f}", 
                   (10, info_y + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(debug_img, f"Rows with change_rate > 0.1: {len(significant_rows)}", 
                   (10, info_y + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        if waterline_y is not None:
            cv2.putText(debug_img, f"First from bottom: y={waterline_y}", 
                       (10, info_y + 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        cv2.imwrite("debug_contrast_change_detection.jpg", debug_img)
        
        # 保存统计信息到文件
        stats_filename = "contrast_change_stats.txt"
        with open(stats_filename, 'w', encoding='utf-8') as f:
            f.write("基于对比度变化率的水面线检测统计信息\n")
            f.write("=" * 50 + "\n")
            f.write(f"检测方法: 简化变化率检测\n")
            f.write(f"检测逻辑: 从底部往上找第一个change_rate > 0.1的位置\n\n")
            
            f.write(f"水区域范围: y={water_region_start_y} 到 y={roi_height}\n")
            f.write(f"水区域对比度统计:\n")
            f.write(f"  均值: {water_contrast_mean:.4f}\n")
            f.write(f"  标准差: {water_contrast_std:.4f}\n")
            f.write(f"  中位数: {water_contrast_median:.4f}\n\n")
            
            f.write(f"检测参数:\n")
            f.write(f"  变化率阈值: {change_rate_threshold_simple}\n\n")
            
            f.write(f"检测结果:\n")
            f.write(f"  满足条件的行数: {len(significant_rows)}\n")
            f.write(f"  检测到的水面线: y={waterline_y}\n")
            if waterline_y is not None:
                f.write(f"  水面线处的变化率: {change_rates[waterline_y]:.4f}\n")
            f.write("\n")
            
            f.write(f"前5个变化率最大的位置:\n")
            for i, row in enumerate(top_5_changes):
                f.write(f"  #{i+1}: y={row['y']}, change_rate={row['change_rate']:.4f}, "
                       f"对比度={row['contrast_mean']:.4f}\n")
        
        print(f"[对比度检测] 统计信息已保存到: {stats_filename}")
        print(f"[对比度检测] 可视化结果已保存到: debug_contrast_change_detection.jpg")
        print(f"[对比度检测] 检测方法: 简化变化率检测 (阈值=0.1)")
        print(f"[对比度检测] 检测到的水面线: y={waterline_y}")
        if waterline_y is not None:
            print(f"[对比度检测] 水面线处的变化率: {change_rates[waterline_y]:.4f}")
        print(f"[对比度检测] 满足条件的行数: {len(significant_rows)}")
        
        # 构建前5个位置的字符串
        top_5_str = []
        for row in top_5_changes:
            top_5_str.append(f"y={row['y']}({row['change_rate']:.3f})")
        print(f"[对比度检测] 前5个变化率最大位置: {top_5_str}")
    
    return waterline_y, [row['y'] for row in top_5_changes], stats 