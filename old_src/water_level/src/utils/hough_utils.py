import cv2
import numpy as np
from src.config.config_utils import config_manager

# 从配置获取默认参数
def get_default_hough_params():
    """从配置文件获取霍夫变换参数"""
    config = config_manager.get_config('hough')
    return {
        'thresh': config.get('thresh', 70),
        'min_line_len_factor': config.get('min_line_len_factor', 0.3),
        'max_gap': config.get('max_gap', 20)
    }

# 霍夫变换相关常量
hough_params = get_default_hough_params()
DEFAULT_HOUGH_THRESH_WATER = hough_params['thresh']
DEFAULT_HOUGH_MIN_LINE_LEN_FACTOR_WATER = hough_params['min_line_len_factor']
DEFAULT_HOUGH_MAX_GAP_WATER = hough_params['max_gap']

def waterline_hough_local(
    preprocessed_channel: np.ndarray, 
    hough_thresh: int = None, 
    min_line_len_factor: float = None, 
    max_gap: int = None
) -> int | None:
    """
    在预处理后ROI图像的下半部分，使用霍夫变换检测水线位置 (相对于 preprocessed_channel 顶部的y坐标)。
    函数名公开化，并为参数提供默认值。
    """
    # 使用配置文件的默认值
    if hough_thresh is None:
        hough_thresh = DEFAULT_HOUGH_THRESH_WATER
    if min_line_len_factor is None:
        min_line_len_factor = DEFAULT_HOUGH_MIN_LINE_LEN_FACTOR_WATER
    if max_gap is None:
        max_gap = DEFAULT_HOUGH_MAX_GAP_WATER
    
    h_roi, w_roi = preprocessed_channel.shape
    if h_roi < 20 or w_roi < 10: return None

    lower_half_start_y = h_roi // 2
    roi_down = preprocessed_channel[lower_half_start_y:, :]
    
    edges = cv2.Canny(roi_down, 5, 25, apertureSize=3)
    # cv2.imwrite("debug_roi_edges_for_waterline.jpg", edges) # 保留用于调试
    
    min_line_length = int(w_roi * min_line_len_factor)
    if min_line_length < 5: min_line_length = 5

    lines = cv2.HoughLinesP(edges, 1, np.pi / 180,
                            threshold=hough_thresh,
                            minLineLength=min_line_length,
                            maxLineGap=max_gap)
    if lines is None:
        return None
    
    horizontal_line_ys = []
    for line_segment in lines:
        x1, y1, x2, y2 = line_segment[0]
        if abs(y1 - y2) < 5:
            horizontal_line_ys.append((y1 + y2) // 2)
            
    if not horizontal_line_ys:
        return None
    
    return int(np.median(horizontal_line_ys) + lower_half_start_y) 