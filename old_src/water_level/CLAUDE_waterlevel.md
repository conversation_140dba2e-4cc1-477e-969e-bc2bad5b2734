# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Water level detection system using computer vision to automatically read water levels from images containing water rulers. The system detects water rulers, identifies water surface lines, and calculates water depth based on pixel-to-cm conversion.

## Key Commands

### Development Commands
```bash
# Install dependencies
uv pip install -r requirements.txt

# Water rule detection (detect ruler and ROI)
python water_rule.py

# Water surface detection using transparency method
python main.py

# Complete water depth calculation
python src/utils/water_level.py

# Water surface detection with specific method
python water_surface.py --method transparency
```

### Configuration
- **Main config**: `config.yaml` - contains all detection parameters
- **Method selection**: `main.default_method` can be "gradient", "hough", "color_threshold", or "transparency"
- **ROI configuration**: Update `main.default_roi` with [x, y, width, height] values

## Architecture Overview

### Core Components
1. **Water Rule Detection** (`water_rule.py`) - Detects water ruler and establishes ROI
2. **Water Surface Detection** (`main.py`) - Detects water surface line using various methods
3. **Water Level Calculation** (`src/utils/water_level.py`) - Integrates both detections for final depth

### Detection Methods
- **Gradient Method**: Uses pixel intensity gradients (`src/utils/gradient_utils.py`)
- **Hough Transform**: Detects horizontal lines (`src/utils/hough_utils.py`)
- **Color Threshold**: HSV-based water detection (`src/utils/color_threshold_utils.py`)
- **Transparency Detection**: Most sophisticated using transparency characteristics (`src/utils/transparency_utils.py`)

### Configuration System
- **Singleton pattern**: `ConfigManager` in `src/config/config_utils.py`
- **YAML-based**: All parameters in `config.yaml`
- **Runtime updates**: ROI automatically updated after water rule detection

## Workflow for New Images

1. **Initial Setup**: Update `config.yaml` with new image filename in `main.image_file`
2. **Detect Water Rule**: Run `python water_rule.py` to get ROI and pixel-to-cm ratio
3. **Detect Water Surface**: Run `python main.py` with appropriate method
4. **Calculate Depth**: Run `python src/utils/water_level.py` for complete analysis

## Key Configuration Sections

### Detection Parameters
```yaml
# Water surface detection methods
transparency.scheme: "contrast_change"  # Most reliable method
gradient.grad_thresh: 25
hough.thresh: 70
color_threshold.hsv_lower: [35, 40, 40]
```

### Water Rule Detection
```yaml
water_rule.white_threshold: 180
water_rule.roi_width_half: 100
water_rule.top_detection_method: "edge_detection"
```

## Debugging

- **Debug mode**: Set `main.debug: true` in config.yaml for detailed visualizations
- **Output files**: Check `app_data/debug/` for intermediate images
- **Grid visualization**: Enable with `main.grid_size: 100` for coordinate reference

## Workflow Core Classes and Methods

### 1. Image Input Handling
- **File**: `src/utils/image_preprocessing_utils.py`
- **Method**: `_preprocess_roi_for_waterline()` - Loads and preprocesses ROI images for water line detection

### 2. Ruler Detection (White Threshold + Hough Transform)
- **File**: `src/utils/water_rule_utils.py`
- **Method**: `detect_water_rule()` - Detects water ruler using E-letter detection and establishes ROI
- **Returns**: ROI coordinates, ruler top position, E-letter information

### 3. Ruler Scale Detection (E-Letter Recognition)
- **File**: `src/utils/shape_recognition_utils.py`
- **Method**: `detect_e_shapes()` - Detects 'E' and reverse 'E' characters using shape analysis
- **File**: `src/utils/water_rule_utils.py`
- **Method**: `calculate_pixel_cm_ratio()` - Calculates pixel-to-cm ratio using detected E-letters

### 4. Water Surface Detection (4 Methods Available)

#### A. Transparency Detection (Most Sophisticated)
- **File**: `src/utils/transparency_utils.py`
- **Method**: `waterline_transparency_local()` - Uses transparency characteristics with 7 detection schemes
- **Key scheme**: `"contrast_change"` - Most reliable method

#### B. Gradient Method
- **File**: `src/utils/gradient_utils.py`
- **Method**: `waterline_grad_local()` - Uses pixel intensity gradients

#### C. Hough Transform
- **File**: `src/utils/hough_utils.py`
- **Method**: `waterline_hough_local()` - Detects horizontal lines using Hough transform

#### D. Color Threshold
- **File**: `src/utils/color_threshold_utils.py`
- **Method**: `waterline_color_threshold_local()` - Uses HSV color space analysis

### 5. Water Level Calculation
- **File**: `src/utils/water_level.py`
- **Method**: `calculate_water_depth()` - Complete water depth calculation integrating all detection results
- **Method**: `detect_water_surface()` - Standalone water surface detection

### 6. Result Output and Visualization
- **File**: `src/utils/water_rule_utils.py`
- **Method**: `visualize_water_rule_detection()` - Visualizes water ruler detection results
- **File**: `src/utils/transparency_utils.py`
- **Method**: `visualize_transparency_detection()` - Creates comprehensive transparency detection visualization

### 7. Configuration Management
- **File**: `src/config/config_utils.py`
- **Class**: `ConfigManager` - Singleton pattern for centralized configuration management
- **Methods**: 
  - `load_config()` - Load configuration from YAML
  - `get_config()` - Get configuration sections
  - `update_config_roi()` - Update ROI in configuration

## File Structure

- **Entry points**: `main.py`, `water_rule.py`, `water_surface.py`
- **Core logic**: `src/utils/` - modular detection methods
- **Configuration**: `src/config/config_utils.py` - centralized config management
- **Sample images**: `water_level.jpg`, `water_level2.jpg` in root directory