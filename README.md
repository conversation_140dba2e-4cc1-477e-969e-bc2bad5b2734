# VR - 视频分析系统

## 项目概要
这是一个部署在站端的视频分析服务，通过API接口对外提供视频流分析能力。支持三种核心分析功能：水位识别、流速识别、异常人为干扰事件识别。

**项目定位**：
- 纯后端API服务，不包含客户端界面
- 接收视频流URL作为输入，返回分析结果
- 不管理数据库，专注于视频分析算法
- 由其他系统调用API接口获取分析结果

## 核心功能

### 1. 水位识别

### 2. 流速识别

### 3. 异常检测


## 快速开始

### 环境要求
- Python 3.10+
- OpenCV 4.5+
- 4GB+ 内存

### 安装和运行
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 测试API
```bash
# 健康检查
curl http://localhost:8000/api/health

# 水位识别示例
curl -X POST http://localhost:8000/api/analyze/water-level \
  -H "Content-Type: application/json" \
  -d '{"video_url": "rtmp://example.com/stream"}'
```

### 运行测试
```bash
pytest tests/ -v
```

## 文档

- **技术设计**: `.augment/docs/technical_design.md`
- **实施计划**: `.augment/docs/implementation_plan.md`