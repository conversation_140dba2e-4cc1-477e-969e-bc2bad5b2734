# VR视频分析系统依赖包

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# 数据验证和序列化
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 异步支持
asyncio-mqtt>=0.13.0
aiofiles>=23.2.0

# 计算机视觉和图像处理
opencv-python>=4.8.0
numpy>=1.24.0
Pillow>=10.0.0

# 视频处理
imageio>=2.31.0
imageio-ffmpeg>=0.4.9

# 流速分析算法
openpiv>=0.25.0
scipy>=1.11.0
scikit-image>=0.21.0

# 机器学习和深度学习
ultralytics>=8.0.0  # YOLO模型
torch>=2.0.0
torchvision>=0.15.0

# 数据处理
pandas>=2.0.0
matplotlib>=3.7.0

# 配置管理
PyYAML>=6.0.1
python-dotenv>=1.0.0

# 日志和监控
structlog>=23.2.0
psutil>=5.9.0  # 系统监控

# HTTP客户端
httpx>=0.25.0
requests>=2.31.0

# 工具库
click>=8.1.0  # 命令行工具
rich>=13.7.0  # 美化输出
tqdm>=4.66.0  # 进度条

# 开发和测试工具
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.9.0  # 代码格式化
flake8>=6.1.0  # 代码检查
mypy>=1.6.0   # 类型检查

# 性能分析
memory-profiler>=0.61.0
line-profiler>=4.1.0

# 数据库支持(可选)
# sqlalchemy>=2.0.0
# alembic>=1.12.0

# 缓存支持(可选)
# redis>=5.0.0
# aioredis>=2.0.0
