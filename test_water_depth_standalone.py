#!/usr/bin/env python3
"""
水深识别独立测试脚本

使用单张图片测试水深识别算法的完整功能，验证算法迁移的正确性。
可以直接运行，不依赖pytest框架。
"""

import sys
import os
import numpy as np
import cv2
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_manager import ConfigManager
from utils.logger import setup_logger
from algorithms.water_level.transparency import detect_waterline_transparency
from algorithms.water_level.gradient import detect_waterline_gradient
from algorithms.water_level.hough import detect_waterline_hough
from algorithms.water_level.color_threshold import detect_waterline_color_threshold


class WaterDepthTester:
    """水深识别测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 设置配置文件
        os.environ["CONFIG_FILE"] = "config/app_config.yaml"
        
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 初始化日志系统
        setup_logger(debug=True, config_manager=self.config_manager)
        self.logger = logging.getLogger("water_depth_tester")
        
        # 设置测试参数
        self.pixel_to_cm_ratio = 2.5  # 假设每像素代表2.5厘米
        
        # 创建输出目录
        self.output_dir = Path("data/test_results")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("水深识别测试器初始化完成")
    
    def create_test_image(self):
        """创建真实的水面测试图像"""
        self.logger.info("创建测试图像...")
        
        # 创建640x480的图像
        height, width = 480, 640
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 天空部分 (0-200行) - 浅蓝色渐变
        for y in range(200):
            intensity = 255 - int(y * 0.3)
            image[y, :] = [intensity, intensity - 20, 200]
        
        # 岸边/建筑物部分 (200-280行) - 灰色
        image[200:280, :] = [120, 130, 140]
        
        # 水面线区域 (280-290行) - 过渡区域
        for y in range(280, 290):
            transition = (y - 280) / 10.0
            sky_color = np.array([200, 180, 200])
            water_color = np.array([80, 120, 60])
            color = sky_color * (1 - transition) + water_color * transition
            image[y, :] = color.astype(np.uint8)
        
        # 水面部分 (290-480行) - 深绿蓝色，带波纹
        base_water_color = [80, 120, 60]
        for y in range(290, height):
            for x in range(width):
                # 添加波纹效果
                wave = int(10 * np.sin(x * 0.02) * np.sin(y * 0.01))
                noise = np.random.randint(-15, 15, 3)
                color = np.array(base_water_color) + wave + noise
                color = np.clip(color, 0, 255)
                image[y, x] = color
        
        # 添加一些反光效果
        for i in range(20):
            x = np.random.randint(0, width)
            y = np.random.randint(300, height)
            cv2.circle(image, (x, y), np.random.randint(2, 8), (150, 180, 120), -1)
        
        # 保存原始测试图像
        cv2.imwrite(str(self.output_dir / "original_test_image.jpg"), image)
        
        self.logger.info(f"测试图像创建完成，尺寸: {image.shape}")
        return image
    
    def create_result_visualization(self, image, waterline_y, method_name, water_depth_cm):
        """创建检测结果可视化图像"""
        result_image = image.copy()
        
        if waterline_y is not None:
            # 绘制水位线
            cv2.line(result_image, (0, int(waterline_y)), (image.shape[1], int(waterline_y)), 
                    (0, 255, 0), 3)
            
            # 添加文字标注
            text = f"{method_name}: {water_depth_cm:.1f}cm"
            cv2.putText(result_image, text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # 添加水深区域标注
            water_region_text = f"Water Depth: {water_depth_cm:.1f}cm"
            cv2.putText(result_image, water_region_text, 
                       (10, int(waterline_y) + 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
            
            # 添加像素到厘米的标注
            ratio_text = f"Ratio: 1px = {1/self.pixel_to_cm_ratio:.2f}cm"
            cv2.putText(result_image, ratio_text, 
                       (10, image.shape[0] - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        else:
            # 如果检测失败，添加失败标注
            fail_text = f"{method_name}: Detection Failed"
            cv2.putText(result_image, fail_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        return result_image
    
    def test_transparency_method(self, image):
        """测试透明度检测方法"""
        self.logger.info("测试透明度检测方法...")
        
        try:
            waterline_y = detect_waterline_transparency(
                image,
                config_manager=self.config_manager,
                debug=True
            )
            
            if waterline_y is not None:
                water_depth_pixels = image.shape[0] - waterline_y
                water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
                
                self.logger.info(f"透明度检测成功: 水位线={waterline_y:.1f}, 水深={water_depth_cm:.2f}cm")
                
                # 创建结果图像
                result_image = self.create_result_visualization(
                    image, waterline_y, "透明度检测", water_depth_cm
                )
                cv2.imwrite(str(self.output_dir / "transparency_result.jpg"), result_image)
                
                return {
                    "method": "透明度检测",
                    "success": True,
                    "waterline_y": waterline_y,
                    "water_depth_cm": water_depth_cm
                }
            else:
                self.logger.warning("透明度检测未找到水位线")
                return {"method": "透明度检测", "success": False}
                
        except Exception as e:
            self.logger.error(f"透明度检测失败: {e}")
            return {"method": "透明度检测", "success": False, "error": str(e)}
    
    def test_gradient_method(self, image):
        """测试梯度检测方法"""
        self.logger.info("测试梯度检测方法...")
        
        try:
            # 转换为灰度图像
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            waterline_y = detect_waterline_gradient(
                gray_image,
                config_manager=self.config_manager,
                debug=True
            )
            
            if waterline_y is not None:
                water_depth_pixels = gray_image.shape[0] - waterline_y
                water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
                
                self.logger.info(f"梯度检测成功: 水位线={waterline_y:.1f}, 水深={water_depth_cm:.2f}cm")
                
                # 创建结果图像
                result_image = self.create_result_visualization(
                    image, waterline_y, "梯度检测", water_depth_cm
                )
                cv2.imwrite(str(self.output_dir / "gradient_result.jpg"), result_image)
                
                return {
                    "method": "梯度检测",
                    "success": True,
                    "waterline_y": waterline_y,
                    "water_depth_cm": water_depth_cm
                }
            else:
                self.logger.warning("梯度检测未找到水位线")
                return {"method": "梯度检测", "success": False}
                
        except Exception as e:
            self.logger.error(f"梯度检测失败: {e}")
            return {"method": "梯度检测", "success": False, "error": str(e)}
    
    def test_hough_method(self, image):
        """测试霍夫变换检测方法"""
        self.logger.info("测试霍夫变换检测方法...")
        
        try:
            # 转换为灰度图像
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            waterline_y = detect_waterline_hough(
                gray_image,
                config_manager=self.config_manager,
                debug=True
            )
            
            if waterline_y is not None:
                water_depth_pixels = gray_image.shape[0] - waterline_y
                water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
                
                self.logger.info(f"霍夫变换检测成功: 水位线={waterline_y:.1f}, 水深={water_depth_cm:.2f}cm")
                
                # 创建结果图像
                result_image = self.create_result_visualization(
                    image, waterline_y, "霍夫变换检测", water_depth_cm
                )
                cv2.imwrite(str(self.output_dir / "hough_result.jpg"), result_image)
                
                return {
                    "method": "霍夫变换检测",
                    "success": True,
                    "waterline_y": waterline_y,
                    "water_depth_cm": water_depth_cm
                }
            else:
                self.logger.warning("霍夫变换检测未找到水位线")
                return {"method": "霍夫变换检测", "success": False}
                
        except Exception as e:
            self.logger.error(f"霍夫变换检测失败: {e}")
            return {"method": "霍夫变换检测", "success": False, "error": str(e)}
    
    def test_color_threshold_method(self, image):
        """测试颜色阈值检测方法"""
        self.logger.info("测试颜色阈值检测方法...")
        
        try:
            waterline_y = detect_waterline_color_threshold(
                image,
                config_manager=self.config_manager,
                debug=True
            )
            
            if waterline_y is not None:
                water_depth_pixels = image.shape[0] - waterline_y
                water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
                
                self.logger.info(f"颜色阈值检测成功: 水位线={waterline_y:.1f}, 水深={water_depth_cm:.2f}cm")
                
                # 创建结果图像
                result_image = self.create_result_visualization(
                    image, waterline_y, "颜色阈值检测", water_depth_cm
                )
                cv2.imwrite(str(self.output_dir / "color_threshold_result.jpg"), result_image)
                
                return {
                    "method": "颜色阈值检测",
                    "success": True,
                    "waterline_y": waterline_y,
                    "water_depth_cm": water_depth_cm
                }
            else:
                self.logger.warning("颜色阈值检测未找到水位线")
                return {"method": "颜色阈值检测", "success": False}
                
        except Exception as e:
            self.logger.error(f"颜色阈值检测失败: {e}")
            return {"method": "颜色阈值检测", "success": False, "error": str(e)}
    
    def run_all_tests(self):
        """运行所有水深检测测试"""
        print("=" * 80)
        print("水深识别算法完整功能测试")
        print("=" * 80)
        
        # 创建测试图像
        test_image = self.create_test_image()
        
        # 运行所有检测方法
        results = []
        
        results.append(self.test_transparency_method(test_image))
        results.append(self.test_gradient_method(test_image))
        results.append(self.test_hough_method(test_image))
        results.append(self.test_color_threshold_method(test_image))
        
        # 分析结果
        successful_results = [r for r in results if r.get("success", False)]
        
        print("\n" + "=" * 80)
        print("测试结果汇总:")
        print("=" * 80)
        
        for result in results:
            if result.get("success", False):
                print(f"{result['method']:15} | ✅ 成功 | 水位线: {result['waterline_y']:6.1f}px | 水深: {result['water_depth_cm']:6.1f}cm")
            else:
                error_msg = result.get("error", "检测失败")
                print(f"{result['method']:15} | ❌ 失败 | {error_msg}")
        
        if successful_results:
            # 计算统计信息
            depths = [r["water_depth_cm"] for r in successful_results]
            avg_depth = np.mean(depths)
            std_depth = np.std(depths)
            
            print("=" * 80)
            print(f"成功检测方法数: {len(successful_results)}/{len(results)}")
            print(f"平均水深: {avg_depth:.2f}cm")
            print(f"标准差: {std_depth:.2f}cm")
            print(f"像素到厘米比例: 1px = {1/self.pixel_to_cm_ratio:.2f}cm")
            print("=" * 80)
            
            # 创建综合结果图像
            self.create_summary_image(test_image, successful_results)
        
        print(f"\n结果图像已保存到: {self.output_dir}")
        print("测试完成！")
        
        return results
    
    def create_summary_image(self, original_image, successful_results):
        """创建综合结果图像"""
        if not successful_results:
            return
        
        # 创建2x2的网格图像
        grid_image = np.zeros((original_image.shape[0] * 2, original_image.shape[1] * 2, 3), dtype=np.uint8)
        
        # 左上角：原始图像
        grid_image[0:original_image.shape[0], 0:original_image.shape[1]] = original_image
        cv2.putText(grid_image, "Original Image", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 其他位置：成功的检测结果
        positions = [
            (0, original_image.shape[1]),  # 右上
            (original_image.shape[0], 0),  # 左下
            (original_image.shape[0], original_image.shape[1])  # 右下
        ]
        
        for i, result in enumerate(successful_results[:3]):
            if i < len(positions):
                y_start, x_start = positions[i]
                y_end = y_start + original_image.shape[0]
                x_end = x_start + original_image.shape[1]
                
                # 创建结果图像
                result_img = self.create_result_visualization(
                    original_image, result["waterline_y"], 
                    result["method"], result["water_depth_cm"]
                )
                
                grid_image[y_start:y_end, x_start:x_end] = result_img
        
        # 保存综合结果
        cv2.imwrite(str(self.output_dir / "summary_results.jpg"), grid_image)
        self.logger.info("综合结果图像已保存")


def main():
    """主函数"""
    try:
        tester = WaterDepthTester()
        results = tester.run_all_tests()
        
        # 检查是否有成功的结果
        successful_count = sum(1 for r in results if r.get("success", False))
        
        if successful_count > 0:
            print(f"\n🎉 测试成功！{successful_count}个算法正常工作。")
            return True
        else:
            print("\n❌ 测试失败！所有算法都未能成功检测水位线。")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
