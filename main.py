"""
VR视频分析系统主入口文件

基于FastAPI的视频分析服务，提供水位识别、流速识别和异常检测功能。
"""

import asyncio
import argparse
import logging
import os
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger
from src.api.health import router as health_router
from src.api.analyze import router as analyze_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理

    启动时初始化配置、日志等系统组件
    关闭时清理资源
    """
    # 启动时初始化
    logger = logging.getLogger(__name__)
    logger.info("正在启动VR视频分析系统...")

    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        app.state.config = config_manager

        # 初始化分析服务
        from src.services.analysis_service import AnalysisService
        analysis_service = AnalysisService(config_manager)
        app.state.analysis_service = analysis_service

        logger.info("VR视频分析系统启动完成")

        yield

    except Exception as e:
        logger.error(f"系统启动失败: {str(e)}")
        raise
    finally:
        # 关闭时清理
        logger.info("正在关闭VR视频分析系统...")
        if hasattr(app.state, 'analysis_service'):
            await app.state.analysis_service.cleanup()
        logger.info("VR视频分析系统已关闭")


def create_app() -> FastAPI:
    """
    创建FastAPI应用实例

    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    app = FastAPI(
        title="VR视频分析系统",
        description="提供水位识别、流速识别和异常检测的视频分析API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册路由
    app.include_router(health_router, prefix="/api", tags=["系统"])
    app.include_router(analyze_router, prefix="/api", tags=["分析"])

    # 全局异常处理
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger = logging.getLogger(__name__)
        logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "内部服务器错误",
                    "details": str(exc) if os.getenv("DEBUG") else "请联系系统管理员"
                }
            }
        )

    return app


def main():
    """
    主函数，解析命令行参数并启动服务
    """
    parser = argparse.ArgumentParser(description="VR视频分析系统")
    parser.add_argument("--config", default="config/app_config.yaml", help="配置文件路径")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")

    args = parser.parse_args()

    # 设置环境变量
    os.environ["CONFIG_FILE"] = args.config
    if args.debug:
        os.environ["DEBUG"] = "true"

    # 初始化日志系统
    setup_logger(debug=args.debug)

    # 创建应用实例
    app = create_app()

    # 启动服务器
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="debug" if args.debug else "info"
    )


if __name__ == "__main__":
    main()
