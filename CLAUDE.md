# CLAUDE.md

本文件为Claude Code (claude.ai/code) 提供VR视频分析系统的开发指导。

## 🎯 项目现状

**当前状态**：项目处于架构设计阶段，已具备：
- 技术设计文档：`.augment/docs/technical_design.md`
- 项目说明：README.md
- 历史代码库：old_src/ （仅作参考，不直接使用）
- 基础配置：pyproject.toml

**项目定位**：纯后端API服务，专注视频流分析，支持水位识别、流速识别、异常检测。

## 🔍 设计合理性分析

### ✅ 原设计文档的合理性确认

经过重新分析，原技术设计文档的**模块设计高度合理**。

#### 1. **分层架构清晰** ⭐⭐⭐⭐⭐
- **API层** (`api/`)：处理HTTP请求和响应，路由分发
- **服务层** (`services/`)：业务逻辑协调，analyzer_service.py作为统筹器
- **处理器层** (`processors/`)：按分析类型独立封装，职责单一
- **算法层** (`algorithms/`)：具体实现细节，可独立替换

#### 2. **职责分离恰当** ⭐⭐⭐⭐⭐
- **analyzer_service.py**：服务协调器，正是应该统筹所有分析类型
- **processor_pool.py**：分析任务处理器池，管理CPU密集型算法任务
- **stream_manager.py**：视频流管理，处理I/O密集型操作
- **三者职责清晰，无重叠**

#### 3. **扩展性设计优秀** ⭐⭐⭐⭐⭐
- **处理器基类** (`base_processor.py`)：便于添加新的分析类型
- **模块化结构**：每个分析类型独立目录，完全解耦
- **协议支持**：RTMP/RTSP/HTTP已覆盖主要场景，架构具备扩展性

## 🚀 开发实施路径

### 第一阶段：基础框架搭建（优先级1）

#### 1.1 环境准备
```bash
# 项目初始化
python -m venv venv
source venv/bin/activate
pip install fastapi uvicorn opencv-python numpy openpiv

# 创建基础目录结构
mkdir -p src/{api,core,processors/{water_level,flow_speed,anomaly},shared,utils,config}
touch src/{__init__.py,main.py}
```

#### 1.2 核心组件开发顺序
1. **配置系统**（复用水位项目的ConfigManager）
2. **异常体系**（定义统一的异常类）
3. **日志系统**（复用流速项目的日志管理）
4. **FastAPI基础架构**（复用CV项目的应用结构）

### 第二阶段：分析引擎开发（优先级2）

#### 2.1 水位识别引擎
- **复用组件**：透明度检测、水尺识别、水位计算
- **集成方式**：封装为WaterLevelEngine类
- **测试策略**：使用历史项目中的测试图像

#### 2.2 流速识别引擎
- **复用组件**：OTV算法、PIV算法、视频处理工具
- **集成方式**：封装为FlowSpeedEngine类
- **优化重点**：算法参数配置化

#### 2.3 异常检测引擎
- **复用组件**：YOLO模型封装、图像预处理
- **集成方式**：封装为AnomalyEngine类
- **扩展支持**：可配置检测目标类型

### 第三阶段：并发与优化（优先级3）

#### 3.1 统一视频流管理
- **目标**：支持5个并发视频流，3种分析类型
- **实现**：基于asyncio的流管理中心
- **优化**：连接复用、内存共享

#### 3.2 任务编排系统
- **功能**：任务调度、优先级管理、超时控制
- **架构**：生产者-消费者模式
- **监控**：实时任务状态跟踪

## 📁 目录结构规范

### 项目根目录
```
VR/
├── src/                          # 主要源码
│   ├── main.py                  # FastAPI应用入口
│   ├── api/                     # API路由
│   │   ├── analyze.py          # 分析接口
│   │   └── health.py           # 健康检查
│   ├── core/                    # 核心业务逻辑
│   ├── processors/              # 分析处理器
│   ├── shared/                  # 共享组件
│   ├── utils/                   # 工具函数
│   └── config/                  # 配置管理
├── models/                      # AI模型文件
├── config/                      # 配置文件
├── tests/                       # 测试代码
├── data/                        # 数据目录（临时文件、日志）
└── docs/                        # 项目文档
```

### 配置文件结构
```yaml
# config/app.yaml - 扁平化配置
server:
  host: "0.0.0.0"
  port: 8000

analyzers:
  water_level:
    enabled: true
    methods: ["transparency", "gradient", "hough"]
    
  flow_speed:
    enabled: true
    methods: ["otv", "piv"]
    
  anomaly:
    enabled: true
    targets: ["person", "boat", "vehicle"]

limits:
  max_concurrent_streams: 5
  max_analysis_duration: 300
```

## 🔧 开发命令指南

### 快速开始
```bash
# 1. 环境初始化
python -m venv venv && source venv/bin/activate
pip install -r requirements.txt

# 2. 启动开发服务器
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 3. 查看API文档
open http://localhost:8000/docs
```

### 开发工作流
```bash
# 代码检查
python -m flake8 src/
python -m mypy src/

# 运行测试
pytest tests/ -v

# 性能测试
python tests/load_test.py
```

### 代码生成模板
```bash
# 创建新处理器
python scripts/create_processor.py --name new_analyzer

# 添加API端点
python scripts/add_endpoint.py --path /api/analyze/new-type
```

## 🎯 关键设计决策

### 1. 架构模式选择
- **选择**：工厂模式 + 策略模式
- **理由**：解耦分析引擎与具体算法实现，便于扩展

### 2. 并发模型
- **选择**：asyncio + 线程池
- **理由**：I/O密集型任务适合asyncio，算法计算使用线程池

### 3. 视频协议支持
- **第一阶段**：RTMP, RTSP, HTTP
- **后续扩展**：HLS, WebRTC, WebSocket

### 4. 错误处理策略
- **统一异常**：定义业务异常类
- **错误码规范**：RESTful标准错误响应
- **日志追踪**：请求ID贯穿全链路

## 📊 性能目标

### 响应时间
- 水位识别：<10秒（单任务），<15秒（并发）
- 流速识别：<60秒（单任务），<90秒（并发）
- 异常检测：<5秒（单任务），<8秒（并发）

### 并发能力
- 最大并发视频流：5个
- 每流最大并发任务：3个
- 总并发任务数：≤15个

### 资源限制
- 内存使用：峰值<4GB，平均<2GB
- CPU使用：峰值<90%，平均<60%

## 🔄 迭代计划

### 迭代1：基础框架（1周）
- [ ] 项目结构搭建
- [ ] 配置系统实现
- [ ] 异常体系建立
- [ ] 日志系统配置

### 迭代2：水位识别（1周）
- [ ] 复用水位检测算法
- [ ] 集成测试验证
- [ ] API接口实现

### 迭代3：流速识别（1周）
- [ ] 复用流速算法
- [ ] 参数配置化
- [ ] 性能优化

### 迭代4：异常检测（1周）
- [ ] YOLO模型集成
- [ ] 检测目标配置
- [ ] 结果格式化

### 迭代5：并发优化（1周）
- [ ] 流管理中心
- [ ] 任务编排系统
- [ ] 性能测试

## 📝 注意事项

### 代码规范
- 使用类型注解
- 遵循PEP 8规范
- 添加docstring
- 单元测试覆盖率>80%

### 配置管理
- 环境变量支持
- 配置热加载
- 敏感信息加密

### 文档要求
- API文档自动生成
- 代码注释中文
- 部署文档详细

### 测试策略
- 单元测试：每个模块独立测试
- 集成测试：端到端流程验证
- 性能测试：并发压力测试