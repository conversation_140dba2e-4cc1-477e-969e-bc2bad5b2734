"""
API数据模型定义

定义所有API接口的请求和响应数据模型，使用Pydantic进行数据验证。
"""

from datetime import datetime
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field, validator


# 基础数据模型
class Point(BaseModel):
    """二维点坐标"""
    x: float = Field(..., description="X坐标")
    y: float = Field(..., description="Y坐标")


class BoundingBox(BaseModel):
    """边界框"""
    x: float = Field(..., description="左上角X坐标")
    y: float = Field(..., description="左上角Y坐标")
    width: float = Field(..., description="宽度")
    height: float = Field(..., description="高度")


class ROI(BaseModel):
    """感兴趣区域"""
    x: int = Field(..., description="左上角X坐标")
    y: int = Field(..., description="左上角Y坐标")
    width: int = Field(..., description="宽度")
    height: int = Field(..., description="高度")


class PolygonROI(BaseModel):
    """多边形感兴趣区域"""
    points: List[Point] = Field(..., description="多边形顶点列表")


# 水位识别相关模型
class WaterLevelParameters(BaseModel):
    """水位识别参数"""
    method: str = Field(default="transparency", description="检测方法")
    analysis_duration: int = Field(default=30, ge=1, le=600, description="分析时长(秒)")
    roi: Optional[ROI] = Field(None, description="感兴趣区域")
    
    @validator('method')
    def validate_method(cls, v):
        allowed_methods = ["transparency", "gradient", "hough", "color_threshold", "ruler_detection"]
        if v not in allowed_methods:
            raise ValueError(f"方法必须是以下之一: {allowed_methods}")
        return v


class WaterLevelRequest(BaseModel):
    """水位识别请求"""
    video_url: str = Field(..., description="视频流URL")
    parameters: WaterLevelParameters = Field(default_factory=WaterLevelParameters, description="分析参数")


class WaterLevelResult(BaseModel):
    """水位识别结果"""
    depth_cm: float = Field(..., description="水位深度(厘米)")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    pixel_to_cm_ratio: Optional[float] = Field(None, description="像素到厘米转换比例")
    water_surface_line: Optional[List[Point]] = Field(None, description="水面线坐标点")


class WaterLevelResponse(BaseModel):
    """水位识别响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    result: Optional[WaterLevelResult] = Field(None, description="分析结果")
    method_used: Optional[str] = Field(None, description="使用的检测方法")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    timestamp: str = Field(..., description="时间戳")


# 流速识别相关模型
class Calibration(BaseModel):
    """标定参数"""
    pixel_to_meter: float = Field(..., gt=0, description="像素到米转换比例")


class FlowSpeedParameters(BaseModel):
    """流速识别参数"""
    method: str = Field(default="otv", description="分析方法")
    analysis_duration: int = Field(default=60, ge=1, le=1200, description="分析时长(秒)")
    roi: Optional[PolygonROI] = Field(None, description="分析区域")
    calibration: Calibration = Field(..., description="标定参数")
    
    @validator('method')
    def validate_method(cls, v):
        allowed_methods = ["otv", "piv"]
        if v not in allowed_methods:
            raise ValueError(f"方法必须是以下之一: {allowed_methods}")
        return v


class FlowSpeedRequest(BaseModel):
    """流速识别请求"""
    video_url: str = Field(..., description="视频流URL")
    parameters: FlowSpeedParameters = Field(..., description="分析参数")


class FlowSpeedResult(BaseModel):
    """流速识别结果"""
    average_speed_ms: float = Field(..., description="平均流速(米/秒)")
    max_speed_ms: float = Field(..., description="最大流速(米/秒)")
    min_speed_ms: float = Field(..., description="最小流速(米/秒)")
    flow_direction: float = Field(..., description="流向角度(度)")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    velocity_field: Optional[List[List[Point]]] = Field(None, description="速度场数据")


class FlowSpeedResponse(BaseModel):
    """流速识别响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    result: Optional[FlowSpeedResult] = Field(None, description="分析结果")
    method_used: Optional[str] = Field(None, description="使用的分析方法")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    timestamp: str = Field(..., description="时间戳")


# 异常检测相关模型
class AnomalyParameters(BaseModel):
    """异常检测参数"""
    detection_targets: List[str] = Field(default=["person", "boat", "vehicle"], description="检测目标列表")
    analysis_duration: int = Field(default=30, ge=1, le=300, description="分析时长(秒)")
    confidence_threshold: float = Field(default=0.5, ge=0, le=1, description="置信度阈值")
    
    @validator('detection_targets')
    def validate_targets(cls, v):
        allowed_targets = ["person", "boat", "vehicle", "bicycle", "motorcycle", "truck", "bus"]
        for target in v:
            if target not in allowed_targets:
                raise ValueError(f"检测目标必须是以下之一: {allowed_targets}")
        return v


class AnomalyRequest(BaseModel):
    """异常检测请求"""
    video_url: str = Field(..., description="视频流URL")
    parameters: AnomalyParameters = Field(default_factory=AnomalyParameters, description="检测参数")


class Detection(BaseModel):
    """单个检测结果"""
    object_type: str = Field(..., description="目标类型")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    bounding_box: BoundingBox = Field(..., description="边界框")
    timestamp: str = Field(..., description="检测时间戳")


class AnomalyResult(BaseModel):
    """异常检测结果"""
    anomalies_detected: bool = Field(..., description="是否检测到异常")
    detections: List[Detection] = Field(default_factory=list, description="检测结果列表")
    risk_level: str = Field(..., description="风险等级")
    total_detections: int = Field(..., description="总检测数量")
    
    @validator('risk_level')
    def validate_risk_level(cls, v):
        allowed_levels = ["low", "medium", "high", "critical"]
        if v not in allowed_levels:
            raise ValueError(f"风险等级必须是以下之一: {allowed_levels}")
        return v


class AnomalyResponse(BaseModel):
    """异常检测响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    result: Optional[AnomalyResult] = Field(None, description="检测结果")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    timestamp: str = Field(..., description="时间戳")


# 错误响应模型
class ErrorDetail(BaseModel):
    """错误详情"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    details: Optional[Union[str, Dict[str, Any]]] = Field(None, description="错误详细信息")


class ErrorResponse(BaseModel):
    """错误响应"""
    error: ErrorDetail = Field(..., description="错误信息")
    task_id: Optional[str] = Field(None, description="任务ID")
    timestamp: str = Field(..., description="时间戳")
