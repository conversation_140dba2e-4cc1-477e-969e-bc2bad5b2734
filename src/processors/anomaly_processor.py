"""
异常检测处理器

整合YOLO检测器，提供统一的异常检测接口。
支持多种目标检测和风险评估。
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..utils.config_manager import ConfigManager
from ..utils.video_utils import VideoStreamProcessor
from ..utils.image_utils import ImageProcessor
from ..algorithms.anomaly.yolo_detector import detect_anomalies_yolo, AnomalyResult, Detection


logger = logging.getLogger(__name__)


class AnomalyProcessor:
    """
    异常检测处理器
    
    整合YOLO检测器，提供统一的异常检测接口
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化异常检测处理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 初始化工具组件
        self.video_processor = VideoStreamProcessor(config_manager)
        self.image_processor = ImageProcessor(config_manager)
        
        # 获取配置
        self.default_confidence_threshold = config_manager.get("analyzers.anomaly.confidence_threshold", 0.5)
        self.max_analysis_duration = config_manager.get("analyzers.anomaly.max_analysis_duration", 300)
        self.default_targets = config_manager.get(
            "analyzers.anomaly.detection_targets", 
            ["person", "boat", "vehicle", "bicycle", "motorcycle"]
        )
        
        # 支持的检测目标
        self.available_targets = [
            "person", "boat", "vehicle", "bicycle", "motorcycle", 
            "truck", "bus", "car", "motorbike", "aeroplane", "train"
        ]
        
        self.logger.info(f"异常检测处理器已初始化，支持目标: {self.available_targets}")
    
    async def analyze(
        self,
        video_url: str,
        detection_targets: Optional[List[str]] = None,
        duration: int = 30,
        confidence_threshold: Optional[float] = None,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        执行异常检测分析
        
        Args:
            video_url: 视频流URL
            detection_targets: 检测目标列表
            duration: 分析时长(秒)
            confidence_threshold: 置信度阈值
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        start_time = time.time()
        
        try:
            # 参数验证
            targets = detection_targets or self.default_targets
            duration = min(duration, self.max_analysis_duration)
            conf_thresh = confidence_threshold or self.default_confidence_threshold
            
            # 验证检测目标
            invalid_targets = [t for t in targets if t not in self.available_targets]
            if invalid_targets:
                self.logger.warning(f"不支持的检测目标: {invalid_targets}")
                targets = [t for t in targets if t in self.available_targets]
            
            if not targets:
                raise ValueError("没有有效的检测目标")
            
            self.logger.info(f"开始异常检测: 目标={targets}, 时长={duration}秒, 任务ID={task_id}")
            
            # 连接视频流
            if not await self.video_processor.connect(video_url):
                raise RuntimeError(f"无法连接到视频源: {video_url}")
            
            try:
                # 获取视频信息
                video_info = self.video_processor.get_video_info()
                self.logger.info(f"视频信息: {video_info}")
                
                # 收集分析帧
                analysis_frames = []
                async for frame in self.video_processor.extract_frames(duration):
                    analysis_frames.append(frame)
                    
                    # 控制帧数以平衡检测质量和性能
                    if len(analysis_frames) >= 100:  # 最多分析100帧
                        break
                
                if not analysis_frames:
                    raise RuntimeError("未能提取到有效的视频帧")
                
                self.logger.info(f"提取了{len(analysis_frames)}帧用于异常检测")
                
                # 执行异常检测
                result = await self._analyze_frames(analysis_frames, targets, conf_thresh, task_id)
                
                # 计算处理时间
                processing_time = time.time() - start_time
                result["processing_time"] = processing_time
                
                self.logger.info(f"异常检测完成: 任务ID={task_id}, 耗时={processing_time:.2f}秒")
                
                return result
                
            finally:
                # 断开视频连接
                self.video_processor.disconnect()
                
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"异常检测失败: 任务ID={task_id}, 错误={str(e)}, 耗时={processing_time:.2f}秒")
            raise
    
    async def _analyze_frames(
        self,
        frames: List,
        detection_targets: List[str],
        confidence_threshold: float,
        task_id: str
    ) -> Dict[str, Any]:
        """
        分析视频帧序列
        
        Args:
            frames: 视频帧列表
            detection_targets: 检测目标列表
            confidence_threshold: 置信度阈值
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            self.logger.info(f"开始YOLO异常检测，目标: {detection_targets}")
            
            # 执行YOLO检测
            result = detect_anomalies_yolo(
                video_frames=frames,
                detection_targets=detection_targets,
                confidence_threshold=confidence_threshold,
                config_manager=self.config_manager,
                debug=True
            )
            
            # 格式化检测结果
            formatted_detections = []
            for detection in result.detections:
                formatted_detection = {
                    "object_type": detection.object_type,
                    "confidence": detection.confidence,
                    "bounding_box": {
                        "x": detection.bbox[0],
                        "y": detection.bbox[1],
                        "width": detection.bbox[2] - detection.bbox[0],
                        "height": detection.bbox[3] - detection.bbox[1]
                    },
                    "timestamp": detection.timestamp
                }
                formatted_detections.append(formatted_detection)
            
            # 生成详细的分析报告
            analysis_report = self._generate_analysis_report(result.detections, detection_targets)
            
            return {
                "success": True,
                "anomalies_detected": result.anomalies_detected,
                "detections": formatted_detections,
                "risk_level": result.risk_level,
                "total_detections": result.total_detections,
                "frames_analyzed": len(frames),
                "detection_targets": detection_targets,
                "confidence_threshold": confidence_threshold,
                "analysis_report": analysis_report
            }
            
        except Exception as e:
            self.logger.error(f"帧序列分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "anomalies_detected": False,
                "detections": [],
                "risk_level": "low",
                "total_detections": 0,
                "frames_analyzed": len(frames),
                "detection_targets": detection_targets,
                "confidence_threshold": confidence_threshold
            }
    
    def _generate_analysis_report(self, detections: List[Detection], targets: List[str]) -> Dict[str, Any]:
        """
        生成详细的分析报告
        
        Args:
            detections: 检测结果列表
            targets: 检测目标列表
            
        Returns:
            Dict[str, Any]: 分析报告
        """
        report = {
            "summary": {},
            "target_statistics": {},
            "confidence_distribution": {},
            "temporal_analysis": {},
            "recommendations": []
        }
        
        if not detections:
            report["summary"] = {
                "status": "无异常检测",
                "total_detections": 0,
                "unique_targets": 0
            }
            return report
        
        # 统计各类目标的检测数量
        target_counts = {}
        confidence_values = []
        
        for detection in detections:
            target_type = detection.object_type
            target_counts[target_type] = target_counts.get(target_type, 0) + 1
            confidence_values.append(detection.confidence)
        
        # 生成摘要
        report["summary"] = {
            "status": "检测到异常",
            "total_detections": len(detections),
            "unique_targets": len(target_counts),
            "most_frequent_target": max(target_counts, key=target_counts.get) if target_counts else None
        }
        
        # 目标统计
        report["target_statistics"] = target_counts
        
        # 置信度分布
        if confidence_values:
            import numpy as np
            report["confidence_distribution"] = {
                "mean": float(np.mean(confidence_values)),
                "std": float(np.std(confidence_values)),
                "min": float(np.min(confidence_values)),
                "max": float(np.max(confidence_values)),
                "high_confidence_count": sum(1 for c in confidence_values if c > 0.8)
            }
        
        # 时间分析（基于帧索引）
        frame_indices = []
        for detection in detections:
            if detection.timestamp.startswith("frame_"):
                try:
                    frame_idx = int(detection.timestamp.split("_")[1])
                    frame_indices.append(frame_idx)
                except:
                    pass
        
        if frame_indices:
            import numpy as np
            report["temporal_analysis"] = {
                "detection_span": max(frame_indices) - min(frame_indices) if frame_indices else 0,
                "detection_density": len(frame_indices) / (max(frame_indices) + 1) if frame_indices else 0,
                "peak_detection_frame": max(set(frame_indices), key=frame_indices.count) if frame_indices else None
            }
        
        # 生成建议
        recommendations = []
        
        if "person" in target_counts:
            person_count = target_counts["person"]
            if person_count >= 3:
                recommendations.append("检测到多人活动，建议加强安全监控")
            elif person_count >= 1:
                recommendations.append("检测到人员活动，建议持续关注")
        
        if any(vehicle in target_counts for vehicle in ["vehicle", "boat", "car", "truck", "bus"]):
            recommendations.append("检测到车辆或船只，建议核实是否为授权活动")
        
        if len(target_counts) >= 3:
            recommendations.append("检测到多种类型目标，建议进行综合风险评估")
        
        high_conf_count = report.get("confidence_distribution", {}).get("high_confidence_count", 0)
        if high_conf_count >= 3:
            recommendations.append("多个高置信度检测，建议立即核实现场情况")
        
        report["recommendations"] = recommendations
        
        return report
    
    def get_supported_targets(self) -> List[str]:
        """
        获取支持的检测目标列表
        
        Returns:
            List[str]: 支持的目标列表
        """
        return self.available_targets.copy()
    
    def get_target_info(self, target: str) -> Dict[str, Any]:
        """
        获取检测目标的详细信息
        
        Args:
            target: 目标名称
            
        Returns:
            Dict[str, Any]: 目标信息
        """
        target_descriptions = {
            "person": {"category": "人员", "risk_level": "high", "description": "人员活动检测"},
            "boat": {"category": "水上交通工具", "risk_level": "medium", "description": "船只检测"},
            "vehicle": {"category": "陆上交通工具", "risk_level": "medium", "description": "一般车辆检测"},
            "car": {"category": "陆上交通工具", "risk_level": "medium", "description": "小汽车检测"},
            "truck": {"category": "陆上交通工具", "risk_level": "high", "description": "卡车检测"},
            "bus": {"category": "陆上交通工具", "risk_level": "medium", "description": "公交车检测"},
            "bicycle": {"category": "非机动车", "risk_level": "low", "description": "自行车检测"},
            "motorcycle": {"category": "机动车", "risk_level": "medium", "description": "摩托车检测"},
            "aeroplane": {"category": "航空器", "risk_level": "high", "description": "飞机检测"},
            "train": {"category": "轨道交通", "risk_level": "medium", "description": "火车检测"}
        }
        
        return target_descriptions.get(target, {
            "category": "未知", 
            "risk_level": "low", 
            "description": "目标信息不可用"
        })
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.video_processor:
                self.video_processor.disconnect()
            self.logger.info("异常检测处理器资源清理完成")
        except Exception as e:
            self.logger.error(f"异常检测处理器资源清理失败: {str(e)}")
