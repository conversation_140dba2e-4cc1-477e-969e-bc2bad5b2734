"""
流速识别处理器

整合OTV和PIV算法，提供统一的流速分析接口。
支持光流跟踪和粒子图像测速两种方法。
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

from ..utils.config_manager import ConfigManager
from ..utils.video_utils import VideoStreamProcessor, sort_polygon_points_clockwise
from ..utils.image_utils import ImageProcessor
from ..algorithms.flow_speed.otv import analyze_flow_otv, FlowResult as OTVResult
from ..algorithms.flow_speed.piv import analyze_flow_piv, PIVResult


logger = logging.getLogger(__name__)


class FlowSpeedProcessor:
    """
    流速识别处理器
    
    整合OTV和PIV算法，提供统一的流速分析接口
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化流速识别处理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 初始化工具组件
        self.video_processor = VideoStreamProcessor(config_manager)
        self.image_processor = ImageProcessor(config_manager)
        
        # 获取配置
        self.default_method = config_manager.get("analyzers.flow_speed.default_method", "otv")
        self.max_analysis_duration = config_manager.get("analyzers.flow_speed.max_analysis_duration", 600)
        
        # 支持的分析方法
        self.available_methods = ["otv", "piv"]
        
        self.logger.info(f"流速识别处理器已初始化，支持方法: {self.available_methods}")
    
    async def analyze(
        self,
        video_url: str,
        method: str = None,
        duration: int = 60,
        roi_points: Optional[List[Tuple[float, float]]] = None,
        pixel_to_meter: float = 1.0,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        执行流速识别分析
        
        Args:
            video_url: 视频流URL
            method: 分析方法 ("otv" 或 "piv")
            duration: 分析时长(秒)
            roi_points: ROI区域顶点列表
            pixel_to_meter: 像素到米的转换比例
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        start_time = time.time()
        
        try:
            # 参数验证
            method = method or self.default_method
            duration = min(duration, self.max_analysis_duration)
            
            if method not in self.available_methods:
                raise ValueError(f"不支持的分析方法: {method}，支持的方法: {self.available_methods}")
            
            if pixel_to_meter <= 0:
                raise ValueError("像素到米的转换比例必须大于0")
            
            self.logger.info(f"开始流速分析: 方法={method}, 时长={duration}秒, 任务ID={task_id}")
            
            # 连接视频流
            if not await self.video_processor.connect(video_url):
                raise RuntimeError(f"无法连接到视频源: {video_url}")
            
            try:
                # 获取视频信息
                video_info = self.video_processor.get_video_info()
                fps = video_info.get("fps", 30.0)
                self.logger.info(f"视频信息: {video_info}")
                
                # 处理ROI点
                processed_roi_points = None
                if roi_points and len(roi_points) >= 3:
                    processed_roi_points = sort_polygon_points_clockwise(roi_points)
                    self.logger.info(f"使用ROI区域，顶点数: {len(processed_roi_points)}")
                
                # 收集分析帧
                analysis_frames = []
                async for frame in self.video_processor.extract_frames(duration):
                    analysis_frames.append(frame)
                    
                    # 控制帧数以平衡分析质量和性能
                    max_frames = 200 if method == "otv" else 50  # PIV需要更少帧
                    if len(analysis_frames) >= max_frames:
                        break
                
                if len(analysis_frames) < 2:
                    raise RuntimeError("需要至少2帧进行流速分析")
                
                self.logger.info(f"提取了{len(analysis_frames)}帧用于{method.upper()}分析")
                
                # 执行流速分析
                result = await self._analyze_frames(
                    analysis_frames, method, processed_roi_points, pixel_to_meter, fps, task_id
                )
                
                # 计算处理时间
                processing_time = time.time() - start_time
                result["processing_time"] = processing_time
                
                self.logger.info(f"流速分析完成: 任务ID={task_id}, 耗时={processing_time:.2f}秒")
                
                return result
                
            finally:
                # 断开视频连接
                self.video_processor.disconnect()
                
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"流速分析失败: 任务ID={task_id}, 错误={str(e)}, 耗时={processing_time:.2f}秒")
            raise
    
    async def _analyze_frames(
        self,
        frames: List,
        method: str,
        roi_points: Optional[List[Tuple[float, float]]],
        pixel_to_meter: float,
        fps: float,
        task_id: str
    ) -> Dict[str, Any]:
        """
        分析视频帧序列
        
        Args:
            frames: 视频帧列表
            method: 分析方法
            roi_points: ROI区域顶点
            pixel_to_meter: 像素到米转换比例
            fps: 视频帧率
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            if method == "otv":
                result = await self._analyze_with_otv(frames, roi_points, pixel_to_meter, fps)
            elif method == "piv":
                result = await self._analyze_with_piv(frames, roi_points, pixel_to_meter)
            else:
                raise ValueError(f"不支持的方法: {method}")
            
            # 格式化结果
            return {
                "success": True,
                "average_speed": result.avg_speed,
                "max_speed": result.max_speed,
                "min_speed": result.min_speed,
                "flow_direction": result.flow_direction,
                "confidence": result.confidence,
                "method_used": method,
                "frames_analyzed": len(frames),
                "vector_count": getattr(result, 'point_count', getattr(result, 'vector_count', 0)),
                "velocity_field": getattr(result, 'velocity_field', None)
            }
            
        except Exception as e:
            self.logger.error(f"帧序列分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "average_speed": 0.0,
                "max_speed": 0.0,
                "min_speed": 0.0,
                "flow_direction": 0.0,
                "confidence": 0.0,
                "method_used": method,
                "frames_analyzed": len(frames),
                "vector_count": 0
            }
    
    async def _analyze_with_otv(
        self,
        frames: List,
        roi_points: Optional[List[Tuple[float, float]]],
        pixel_to_meter: float,
        fps: float
    ) -> OTVResult:
        """
        使用OTV方法分析流速
        
        Args:
            frames: 视频帧列表
            roi_points: ROI区域顶点
            pixel_to_meter: 像素到米转换比例
            fps: 视频帧率
            
        Returns:
            OTVResult: OTV分析结果
        """
        try:
            self.logger.info("开始OTV光流分析")
            
            # 执行OTV分析
            result = analyze_flow_otv(
                video_frames=frames,
                roi_points=roi_points,
                pixel_to_meter=pixel_to_meter,
                fps=fps,
                config_manager=self.config_manager,
                debug=True
            )
            
            self.logger.info(f"OTV分析完成: 平均流速={result.avg_speed:.3f}m/s, 特征点数={result.point_count}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"OTV分析失败: {str(e)}")
            return OTVResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
    
    async def _analyze_with_piv(
        self,
        frames: List,
        roi_points: Optional[List[Tuple[float, float]]],
        pixel_to_meter: float
    ) -> PIVResult:
        """
        使用PIV方法分析流速
        
        Args:
            frames: 视频帧列表
            roi_points: ROI区域顶点
            pixel_to_meter: 像素到米转换比例
            
        Returns:
            PIVResult: PIV分析结果
        """
        try:
            self.logger.info("开始PIV粒子图像测速分析")
            
            # 执行PIV分析
            result = analyze_flow_piv(
                video_frames=frames,
                roi_points=roi_points,
                pixel_to_meter=pixel_to_meter,
                config_manager=self.config_manager,
                debug=True
            )
            
            self.logger.info(f"PIV分析完成: 平均流速={result.avg_speed:.3f}m/s, 向量数={result.vector_count}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"PIV分析失败: {str(e)}")
            return PIVResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
    
    def get_supported_methods(self) -> List[str]:
        """
        获取支持的分析方法列表
        
        Returns:
            List[str]: 支持的方法列表
        """
        return self.available_methods.copy()
    
    def get_method_info(self, method: str) -> Dict[str, Any]:
        """
        获取分析方法的详细信息
        
        Args:
            method: 方法名称
            
        Returns:
            Dict[str, Any]: 方法信息
        """
        method_descriptions = {
            "otv": {
                "name": "光流跟踪测速",
                "description": "基于OpenCV光流跟踪技术分析流速",
                "suitable_for": "特征点丰富的水面，光照条件稳定",
                "advantages": ["实时性好", "计算效率高", "适合长时间分析"],
                "parameters": ["maxCorners", "qualityLevel", "minDistance", "winSize"],
                "typical_duration": "30-300秒"
            },
            "piv": {
                "name": "粒子图像测速",
                "description": "基于OpenPIV库进行互相关分析",
                "suitable_for": "水面有漂浮物或粒子，图像质量高",
                "advantages": ["精度高", "空间分辨率好", "适合科学研究"],
                "parameters": ["window_size", "overlap", "search_area_size", "dt"],
                "typical_duration": "10-60秒"
            }
        }
        
        return method_descriptions.get(method, {"name": "未知方法", "description": "方法信息不可用"})
    
    def validate_calibration(self, pixel_to_meter: float, video_info: Dict) -> Dict[str, Any]:
        """
        验证标定参数的合理性
        
        Args:
            pixel_to_meter: 像素到米转换比例
            video_info: 视频信息
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            "valid": True,
            "warnings": [],
            "recommendations": []
        }
        
        # 检查转换比例的合理性
        if pixel_to_meter < 0.001:  # 1像素 < 1毫米
            validation_result["warnings"].append("像素到米转换比例过小，可能导致测量精度问题")
        elif pixel_to_meter > 0.1:  # 1像素 > 10厘米
            validation_result["warnings"].append("像素到米转换比例过大，可能导致测量范围受限")
        
        # 检查视频分辨率
        width = video_info.get("width", 0)
        height = video_info.get("height", 0)
        
        if width < 640 or height < 480:
            validation_result["warnings"].append("视频分辨率较低，可能影响流速测量精度")
            validation_result["recommendations"].append("建议使用更高分辨率的视频源")
        
        # 检查帧率
        fps = video_info.get("fps", 0)
        if fps < 15:
            validation_result["warnings"].append("视频帧率较低，可能影响流速测量精度")
            validation_result["recommendations"].append("建议使用帧率不低于25fps的视频源")
        
        return validation_result
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.video_processor:
                self.video_processor.disconnect()
            self.logger.info("流速识别处理器资源清理完成")
        except Exception as e:
            self.logger.error(f"流速识别处理器资源清理失败: {str(e)}")
