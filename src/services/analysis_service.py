"""
统一分析服务

作为所有分析任务的统一入口，负责协调水位识别、流速识别和异常检测任务。
提供异步任务管理、并发控制和资源管理功能。
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

try:
    from ..utils.config_manager import ConfigManager
    from ..models.schemas import (
        WaterLevelParameters, FlowSpeedParameters, AnomalyParameters
    )
except ImportError:
    # 回退到绝对导入
    from utils.config_manager import ConfigManager
    from models.schemas import (
        WaterLevelParameters, FlowSpeedParameters, AnomalyParameters
    )


logger = logging.getLogger(__name__)


class AnalysisService:
    """
    统一分析服务
    
    负责协调所有分析任务，管理并发执行和资源分配
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化分析服务
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 获取并发控制配置
        self.max_concurrent_streams = config_manager.get("concurrency.max_concurrent_streams", 5)
        self.max_tasks_per_stream = config_manager.get("concurrency.max_tasks_per_stream", 3)
        self.task_queue_size = config_manager.get("concurrency.task_queue_size", 50)
        
        # 创建信号量控制并发
        self.stream_semaphore = asyncio.Semaphore(self.max_concurrent_streams)
        self.task_semaphore = asyncio.Semaphore(self.max_tasks_per_stream)
        
        # 任务队列和状态跟踪
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, Dict[str, Any]] = {}
        
        # 处理器实例缓存
        self._processors: Dict[str, Any] = {}
        
        self.logger.info("分析服务已初始化")
    
    async def analyze_water_level(
        self,
        video_url: str,
        parameters: WaterLevelParameters,
        task_id: str
    ) -> Dict[str, Any]:
        """
        执行水位识别分析

        Args:
            video_url: 视频流URL
            parameters: 水位分析参数
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 分析结果
        """
        self.logger.debug(f"水位分析任务进入队列: {task_id}")

        async with self.stream_semaphore:
            try:
                start_time = time.time()
                self.logger.info(f"开始水位分析任务: {task_id}")
                self.logger.debug(f"分析参数: method={parameters.method}, duration={parameters.analysis_duration}")

                # 检查分析器是否启用
                if not self.config_manager.get("analyzers.water_level.enabled", True):
                    self.logger.error("水位分析器未启用")
                    raise ValueError("水位分析器未启用")

                # 获取或创建水位处理器
                self.logger.debug("获取水位处理器实例")
                processor = await self._get_water_level_processor()

                # 执行分析
                self.logger.debug(f"开始执行水位分析: {video_url}")
                result = await processor.analyze(
                    video_url=video_url,
                    method=parameters.method,
                    duration=parameters.analysis_duration,
                    roi=parameters.roi.dict() if parameters.roi else None,
                    task_id=task_id
                )

                processing_time = time.time() - start_time

                # 格式化结果
                formatted_result = {
                    "water_level": {
                        "depth_cm": result.get("water_depth_cm", 0.0),
                        "confidence": result.get("confidence", 0.0),
                        "pixel_to_cm_ratio": result.get("pixels_per_cm", None),
                        "water_surface_line": result.get("water_surface_points", None)
                    },
                    "method_used": parameters.method,
                    "processing_time": processing_time
                }

                self.logger.info(f"水位分析任务完成: {task_id}, 耗时: {processing_time:.2f}秒")
                self.logger.debug(f"分析结果摘要: 深度={formatted_result['water_level']['depth_cm']:.2f}cm, 置信度={formatted_result['water_level']['confidence']:.3f}")

                return formatted_result

            except Exception as e:
                self.logger.error(f"水位分析任务失败: {task_id}, 错误: {str(e)}")
                raise
    
    async def analyze_flow_speed(
        self, 
        video_url: str, 
        parameters: FlowSpeedParameters, 
        task_id: str
    ) -> Dict[str, Any]:
        """
        执行流速识别分析
        
        Args:
            video_url: 视频流URL
            parameters: 流速分析参数
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        async with self.stream_semaphore:
            try:
                start_time = time.time()
                self.logger.info(f"开始流速分析任务: {task_id}")
                
                # 检查分析器是否启用
                if not self.config_manager.get("analyzers.flow_speed.enabled", True):
                    raise ValueError("流速分析器未启用")
                
                # 获取或创建流速处理器
                processor = await self._get_flow_speed_processor()
                
                # 准备ROI参数
                roi_points = None
                if parameters.roi:
                    roi_points = [(p.x, p.y) for p in parameters.roi.points]
                
                # 执行分析
                result = await processor.analyze(
                    video_url=video_url,
                    method=parameters.method,
                    duration=parameters.analysis_duration,
                    roi_points=roi_points,
                    pixel_to_meter=parameters.calibration.pixel_to_meter,
                    task_id=task_id
                )
                
                processing_time = time.time() - start_time
                
                # 格式化结果
                formatted_result = {
                    "flow_speed": {
                        "average_speed_ms": result.get("average_speed", 0.0),
                        "max_speed_ms": result.get("max_speed", 0.0),
                        "min_speed_ms": result.get("min_speed", 0.0),
                        "flow_direction": result.get("flow_direction", 0.0),
                        "confidence": result.get("confidence", 0.0),
                        "velocity_field": result.get("velocity_field", None)
                    },
                    "method_used": parameters.method,
                    "processing_time": processing_time
                }
                
                self.logger.info(f"流速分析任务完成: {task_id}, 耗时: {processing_time:.2f}秒")
                return formatted_result
                
            except Exception as e:
                self.logger.error(f"流速分析任务失败: {task_id}, 错误: {str(e)}")
                raise
    
    async def analyze_anomaly(
        self, 
        video_url: str, 
        parameters: AnomalyParameters, 
        task_id: str
    ) -> Dict[str, Any]:
        """
        执行异常检测分析
        
        Args:
            video_url: 视频流URL
            parameters: 异常检测参数
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        async with self.stream_semaphore:
            try:
                start_time = time.time()
                self.logger.info(f"开始异常检测任务: {task_id}")
                
                # 检查分析器是否启用
                if not self.config_manager.get("analyzers.anomaly.enabled", True):
                    raise ValueError("异常检测器未启用")
                
                # 获取或创建异常检测处理器
                processor = await self._get_anomaly_processor()
                
                # 执行分析
                result = await processor.analyze(
                    video_url=video_url,
                    detection_targets=parameters.detection_targets,
                    duration=parameters.analysis_duration,
                    confidence_threshold=parameters.confidence_threshold,
                    task_id=task_id
                )
                
                processing_time = time.time() - start_time
                
                # 计算风险等级
                risk_level = self._calculate_risk_level(result.get("detections", []))
                
                # 格式化结果
                formatted_result = {
                    "anomalies_detected": len(result.get("detections", [])) > 0,
                    "detections": result.get("detections", []),
                    "risk_level": risk_level,
                    "total_detections": len(result.get("detections", [])),
                    "processing_time": processing_time
                }
                
                self.logger.info(f"异常检测任务完成: {task_id}, 耗时: {processing_time:.2f}秒")
                return formatted_result
                
            except Exception as e:
                self.logger.error(f"异常检测任务失败: {task_id}, 错误: {str(e)}")
                raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 取消所有活跃任务
            for task_id, task in self.active_tasks.items():
                if not task.done():
                    task.cancel()
                    self.logger.info(f"取消任务: {task_id}")
            
            # 等待所有任务完成
            if self.active_tasks:
                await asyncio.gather(*self.active_tasks.values(), return_exceptions=True)
            
            # 清理处理器
            for processor_type, processor in self._processors.items():
                if hasattr(processor, 'cleanup'):
                    await processor.cleanup()
                self.logger.info(f"清理处理器: {processor_type}")
            
            self.logger.info("分析服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {str(e)}")
    
    async def _get_water_level_processor(self):
        """获取或创建水位处理器"""
        if "water_level" not in self._processors:
            from ..processors.water_level_processor import WaterLevelProcessor
            self._processors["water_level"] = WaterLevelProcessor(self.config_manager)
        return self._processors["water_level"]
    
    async def _get_flow_speed_processor(self):
        """获取或创建流速处理器"""
        if "flow_speed" not in self._processors:
            from ..processors.flow_speed_processor import FlowSpeedProcessor
            self._processors["flow_speed"] = FlowSpeedProcessor(self.config_manager)
        return self._processors["flow_speed"]
    
    async def _get_anomaly_processor(self):
        """获取或创建异常检测处理器"""
        if "anomaly" not in self._processors:
            from ..processors.anomaly_processor import AnomalyProcessor
            self._processors["anomaly"] = AnomalyProcessor(self.config_manager)
        return self._processors["anomaly"]
    
    def _calculate_risk_level(self, detections: list) -> str:
        """
        根据检测结果计算风险等级
        
        Args:
            detections: 检测结果列表
            
        Returns:
            str: 风险等级 (low/medium/high/critical)
        """
        if not detections:
            return "low"
        
        detection_count = len(detections)
        high_confidence_count = sum(1 for d in detections if d.get("confidence", 0) > 0.8)
        
        # 根据检测数量和置信度计算风险等级
        if detection_count >= 5 or high_confidence_count >= 3:
            return "critical"
        elif detection_count >= 3 or high_confidence_count >= 2:
            return "high"
        elif detection_count >= 1 or high_confidence_count >= 1:
            return "medium"
        else:
            return "low"
