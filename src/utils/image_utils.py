"""
图像处理工具

基于水位项目和CV项目的图像处理工具，提供图像预处理、格式转换、尺寸调整等功能。
适配VR视频分析系统的需求。
"""

import cv2
import numpy as np
import logging
from typing import Optional, Tuple, Dict, Any, List
from enum import Enum
import os
from pathlib import Path

from .config_manager import ConfigManager


logger = logging.getLogger(__name__)


class PreprocessMethod(Enum):
    """图像预处理方法枚举"""
    HISTOGRAM_EQUALIZATION = "histogram_equalization"
    GAUSSIAN_BLUR = "gaussian_blur"
    BILATERAL_FILTER = "bilateral_filter"
    NOISE_REDUCTION = "noise_reduction"
    CONTRAST_ENHANCEMENT = "contrast_enhancement"
    BRIGHTNESS_ADJUSTMENT = "brightness_adjustment"
    SUPER_RESOLUTION = "super_resolution"


class ImageProcessor:
    """
    图像处理器
    
    提供图像预处理、格式转换、尺寸调整等功能
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化图像处理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取配置
        self.brightness_threshold = self.config_manager.get("analyzers.water_level.methods.transparency.brightness_thresh", 80)
        self.target_size = (640, 640)  # 默认目标尺寸
    
    def preprocess_roi_for_waterline(self, roi_bgr_image: np.ndarray) -> np.ndarray:
        """
        对ROI区域的BGR图像进行水线检测预处理
        
        Args:
            roi_bgr_image: BGR格式的ROI图像区域
            
        Returns:
            np.ndarray: 预处理后的单通道(V通道)图像
        """
        try:
            # 获取配置参数
            blur_kernel_size = self.config_manager.get("analyzers.water_level.methods.transparency.blur_ksize", 5)
            morph_kernel_size = self.config_manager.get("analyzers.water_level.methods.transparency.morph_kernel_size", 3)
            
            # 转换到HSV颜色空间并提取V通道(亮度)
            hsv = cv2.cvtColor(roi_bgr_image, cv2.COLOR_BGR2HSV)
            v_channel = hsv[:, :, 2]
            
            # 直方图均衡化
            v_eq = cv2.equalizeHist(v_channel)
            
            # 高斯模糊去噪
            v_blur = cv2.GaussianBlur(v_eq, (blur_kernel_size, blur_kernel_size), 0)
            
            # 形态学开运算去除小的噪点
            kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
            v_morph = cv2.morphologyEx(v_blur, cv2.MORPH_OPEN, kernel)
            
            return v_morph
            
        except Exception as e:
            self.logger.error(f"ROI预处理失败: {str(e)}")
            return roi_bgr_image
    
    def preprocess_image(self, image: np.ndarray, methods: Optional[List[str]] = None) -> np.ndarray:
        """
        根据配置的方法列表处理图像
        
        Args:
            image: 输入图像
            methods: 预处理方法列表
            
        Returns:
            np.ndarray: 预处理后的图像
        """
        if methods is None:
            methods = ["histogram_equalization", "gaussian_blur"]
        
        processed_image = image.copy()
        
        try:
            # 分析图像亮度
            gray = cv2.cvtColor(processed_image, cv2.COLOR_BGR2GRAY) if len(processed_image.shape) == 3 else processed_image
            mean_brightness = np.mean(gray)
            
            # 根据亮度和配置决定预处理方法
            for method in methods:
                if method == PreprocessMethod.HISTOGRAM_EQUALIZATION.value:
                    if mean_brightness < self.brightness_threshold:
                        processed_image = self._histogram_equalization(processed_image)
                
                elif method == PreprocessMethod.GAUSSIAN_BLUR.value:
                    processed_image = self._gaussian_blur(processed_image)
                
                elif method == PreprocessMethod.BILATERAL_FILTER.value:
                    processed_image = self._bilateral_filter(processed_image)
                
                elif method == PreprocessMethod.NOISE_REDUCTION.value:
                    processed_image = self._noise_reduction(processed_image)
                
                elif method == PreprocessMethod.CONTRAST_ENHANCEMENT.value:
                    processed_image = self._contrast_enhancement(processed_image)
                
                elif method == PreprocessMethod.BRIGHTNESS_ADJUSTMENT.value:
                    processed_image = self._brightness_adjustment(processed_image)
            
            return processed_image
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {str(e)}")
            return image
    
    def resize_image(self, image: np.ndarray, target_size: Optional[Tuple[int, int]] = None) -> np.ndarray:
        """
        调整图像尺寸
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)
            
        Returns:
            np.ndarray: 调整尺寸后的图像
        """
        if target_size is None:
            target_size = self.target_size
        
        try:
            return cv2.resize(image, target_size, interpolation=cv2.INTER_LINEAR)
        except Exception as e:
            self.logger.error(f"图像尺寸调整失败: {str(e)}")
            return image
    
    def normalize_image(self, image: np.ndarray) -> np.ndarray:
        """
        归一化图像到[0,1]范围
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 归一化后的图像
        """
        try:
            return image.astype(np.float32) / 255.0
        except Exception as e:
            self.logger.error(f"图像归一化失败: {str(e)}")
            return image
    
    def convert_color_space(self, image: np.ndarray, conversion: int) -> np.ndarray:
        """
        转换颜色空间
        
        Args:
            image: 输入图像
            conversion: OpenCV颜色转换代码
            
        Returns:
            np.ndarray: 转换后的图像
        """
        try:
            return cv2.cvtColor(image, conversion)
        except Exception as e:
            self.logger.error(f"颜色空间转换失败: {str(e)}")
            return image
    
    def apply_mask(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        应用掩码到图像
        
        Args:
            image: 输入图像
            mask: 二值掩码
            
        Returns:
            np.ndarray: 应用掩码后的图像
        """
        try:
            return cv2.bitwise_and(image, image, mask=mask)
        except Exception as e:
            self.logger.error(f"掩码应用失败: {str(e)}")
            return image
    
    def create_roi_mask(self, image_shape: Tuple[int, int], roi: Dict[str, int]) -> np.ndarray:
        """
        创建矩形ROI掩码
        
        Args:
            image_shape: 图像尺寸 (height, width)
            roi: ROI参数 {"x": int, "y": int, "width": int, "height": int}
            
        Returns:
            np.ndarray: 二值掩码
        """
        try:
            mask = np.zeros(image_shape[:2], dtype=np.uint8)
            x, y = roi.get("x", 0), roi.get("y", 0)
            w, h = roi.get("width", image_shape[1]), roi.get("height", image_shape[0])
            
            # 确保ROI在图像范围内
            x = max(0, min(x, image_shape[1] - 1))
            y = max(0, min(y, image_shape[0] - 1))
            w = min(w, image_shape[1] - x)
            h = min(h, image_shape[0] - y)
            
            mask[y:y+h, x:x+w] = 255
            return mask
            
        except Exception as e:
            self.logger.error(f"ROI掩码创建失败: {str(e)}")
            return np.ones(image_shape[:2], dtype=np.uint8) * 255
    
    def save_image(self, image: np.ndarray, file_path: str) -> bool:
        """
        保存图像到文件
        
        Args:
            image: 图像数据
            file_path: 文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 确保文件扩展名有效
            _, ext = os.path.splitext(file_path)
            if not ext or ext.lower() not in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                file_path = os.path.splitext(file_path)[0] + '.png'
                self.logger.warning(f"检测到无效的图像扩展名，已更改为: {file_path}")
            
            success = cv2.imwrite(file_path, image)
            if not success:
                self.logger.error(f"图像保存失败: {file_path}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存图像时出错: {str(e)}")
            return False
    
    def _histogram_equalization(self, image: np.ndarray) -> np.ndarray:
        """直方图均衡化"""
        if len(image.shape) == 3:
            # 彩色图像，在LAB空间进行均衡化
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            lab[:, :, 0] = cv2.equalizeHist(lab[:, :, 0])
            return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        else:
            # 灰度图像
            return cv2.equalizeHist(image)
    
    def _gaussian_blur(self, image: np.ndarray, kernel_size: int = 5) -> np.ndarray:
        """高斯模糊"""
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
    
    def _bilateral_filter(self, image: np.ndarray) -> np.ndarray:
        """双边滤波"""
        return cv2.bilateralFilter(image, 9, 75, 75)
    
    def _noise_reduction(self, image: np.ndarray) -> np.ndarray:
        """噪声减少"""
        if len(image.shape) == 3:
            return cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        else:
            return cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
    
    def _contrast_enhancement(self, image: np.ndarray) -> np.ndarray:
        """对比度增强"""
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB) if len(image.shape) == 3 else image
        if len(image.shape) == 3:
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            lab[:, :, 0] = clahe.apply(lab[:, :, 0])
            return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        else:
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            return clahe.apply(image)
    
    def _brightness_adjustment(self, image: np.ndarray, alpha: float = 1.2, beta: int = 10) -> np.ndarray:
        """亮度调整"""
        return cv2.convertScaleAbs(image, alpha=alpha, beta=beta)
