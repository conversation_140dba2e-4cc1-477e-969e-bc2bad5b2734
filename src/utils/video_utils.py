"""
视频处理工具

基于流速项目的video_trimmer.py，提供视频流连接、帧提取、ROI处理等功能。
适配VR视频分析系统的需求。
"""

import cv2
import numpy as np
import asyncio
import logging
from typing import Optional, List, Tuple, Dict, Any, AsyncGenerator
from pathlib import Path
import time
import re
from urllib.parse import urlparse

from .config_manager import ConfigManager


logger = logging.getLogger(__name__)


class VideoStreamError(Exception):
    """视频流处理异常"""
    pass


class VideoStreamProcessor:
    """
    视频流处理器
    
    支持多种视频源：RTMP、RTSP、HTTP、本地文件
    提供帧提取、ROI处理、连接管理等功能
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化视频流处理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.cap: Optional[cv2.VideoCapture] = None
        self.is_connected = False
        self.last_frame_time = 0
        self.frame_count = 0
        
        # 获取配置
        self.connection_timeout = self.config_manager.get("video_processing.connection_timeout", 30)
        self.reconnect_attempts = self.config_manager.get("video_processing.reconnect_attempts", 3)
        self.frame_extraction_interval = self.config_manager.get("video_processing.frame_extraction_interval", 1)
        self.supported_protocols = self.config_manager.get("video_processing.supported_protocols", ["rtmp", "rtsp", "http"])
    
    async def connect(self, video_url: str) -> bool:
        """
        连接到视频源

        Args:
            video_url: 视频URL或文件路径

        Returns:
            bool: 连接是否成功
        """
        logger.debug(f"开始连接视频源: {video_url}")

        try:
            # 验证URL
            if not self._validate_video_url(video_url):
                logger.error(f"URL验证失败: {video_url}")
                raise VideoStreamError(f"不支持的视频URL: {video_url}")

            logger.debug(f"URL验证通过，开始连接尝试")

            # 尝试连接
            for attempt in range(self.reconnect_attempts):
                try:
                    logger.info(f"尝试连接视频源 (第{attempt + 1}次): {video_url}")

                    self.cap = cv2.VideoCapture(video_url)

                    # 设置缓冲区大小
                    self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    logger.debug(f"设置视频缓冲区大小为1")

                    # 测试读取一帧
                    ret, frame = self.cap.read()
                    if ret and frame is not None:
                        self.is_connected = True
                        self.frame_count = 0
                        logger.info(f"成功连接到视频源: {video_url}")
                        logger.debug(f"首帧尺寸: {frame.shape}")
                        return True
                    else:
                        logger.warning(f"无法读取首帧，连接失败")
                        self.cap.release()
                        self.cap = None

                except Exception as e:
                    logger.warning(f"连接尝试 {attempt + 1} 失败: {str(e)}")
                    if self.cap:
                        self.cap.release()
                        self.cap = None

                # 等待后重试
                if attempt < self.reconnect_attempts - 1:
                    logger.debug(f"等待2秒后重试连接")
                    await asyncio.sleep(2)

            logger.error(f"所有连接尝试均失败")
            raise VideoStreamError(f"无法连接到视频源: {video_url}")

        except Exception as e:
            logger.error(f"连接视频源失败: {str(e)}")
            self.is_connected = False
            return False
    
    async def read_frame(self) -> Optional[np.ndarray]:
        """
        读取一帧
        
        Returns:
            Optional[np.ndarray]: 视频帧，失败时返回None
        """
        if not self.is_connected or not self.cap:
            return None
        
        try:
            ret, frame = self.cap.read()
            if ret and frame is not None:
                self.frame_count += 1
                self.last_frame_time = time.time()
                return frame
            else:
                logger.warning("读取帧失败")
                return None
                
        except Exception as e:
            logger.error(f"读取帧时出错: {str(e)}")
            return None
    
    async def extract_frames(self, duration: int, roi: Optional[Dict] = None) -> AsyncGenerator[np.ndarray, None]:
        """
        提取指定时长的视频帧
        
        Args:
            duration: 提取时长(秒)
            roi: 感兴趣区域 {"x": int, "y": int, "width": int, "height": int}
            
        Yields:
            np.ndarray: 视频帧
        """
        if not self.is_connected:
            return
        
        start_time = time.time()
        frame_interval = self.frame_extraction_interval
        last_extract_time = 0
        
        while time.time() - start_time < duration:
            current_time = time.time()
            
            # 控制帧提取间隔
            if current_time - last_extract_time < frame_interval:
                await asyncio.sleep(0.1)
                continue
            
            frame = await self.read_frame()
            if frame is not None:
                # 应用ROI
                if roi:
                    frame = self._apply_roi(frame, roi)
                
                yield frame
                last_extract_time = current_time
            else:
                # 读取失败，尝试重连
                logger.warning("帧读取失败，尝试重连...")
                break
            
            await asyncio.sleep(0.01)  # 避免CPU占用过高
    
    def get_video_info(self) -> Dict[str, Any]:
        """
        获取视频信息
        
        Returns:
            Dict[str, Any]: 视频信息
        """
        if not self.cap:
            return {}
        
        try:
            return {
                "width": int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                "height": int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                "fps": self.cap.get(cv2.CAP_PROP_FPS),
                "frame_count": int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                "is_connected": self.is_connected,
                "frames_read": self.frame_count
            }
        except Exception as e:
            logger.error(f"获取视频信息失败: {str(e)}")
            return {}
    
    def disconnect(self):
        """断开连接"""
        if self.cap:
            self.cap.release()
            self.cap = None
        self.is_connected = False
        logger.info("已断开视频连接")
    
    def _validate_video_url(self, video_url: str) -> bool:
        """
        验证视频URL
        
        Args:
            video_url: 视频URL
            
        Returns:
            bool: URL是否有效
        """
        try:
            # 检查是否是本地文件
            if Path(video_url).exists():
                return True
            
            # 检查URL协议
            parsed = urlparse(video_url)
            if parsed.scheme.lower() in self.supported_protocols:
                return True
            
            logger.error(f"不支持的协议: {parsed.scheme}")
            return False
            
        except Exception as e:
            logger.error(f"URL验证失败: {str(e)}")
            return False
    
    def _apply_roi(self, frame: np.ndarray, roi: Dict) -> np.ndarray:
        """
        应用感兴趣区域
        
        Args:
            frame: 输入帧
            roi: ROI参数
            
        Returns:
            np.ndarray: 裁剪后的帧
        """
        try:
            x = roi.get("x", 0)
            y = roi.get("y", 0)
            width = roi.get("width", frame.shape[1])
            height = roi.get("height", frame.shape[0])
            
            # 确保ROI在图像范围内
            x = max(0, min(x, frame.shape[1] - 1))
            y = max(0, min(y, frame.shape[0] - 1))
            width = min(width, frame.shape[1] - x)
            height = min(height, frame.shape[0] - y)
            
            return frame[y:y+height, x:x+width]
            
        except Exception as e:
            logger.error(f"应用ROI失败: {str(e)}")
            return frame


def sort_polygon_points_clockwise(points: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    """
    将多边形顶点按顺时针方向排序
    
    Args:
        points: 多边形顶点列表
        
    Returns:
        List[Tuple[float, float]]: 排序后的顶点列表
    """
    if len(points) < 3:
        return points
    
    # 计算多边形的中心点
    center_x = sum(p[0] for p in points) / len(points)
    center_y = sum(p[1] for p in points) / len(points)
    
    # 计算每个点相对于中心点的角度
    def get_angle(point):
        return np.arctan2(point[1] - center_y, point[0] - center_x)
    
    # 按角度排序（逆时针）
    sorted_points = sorted(points, key=get_angle, reverse=True)
    return sorted_points


def time_to_seconds(time_str: str) -> float:
    """
    将时间字符串转换为秒数
    
    Args:
        time_str: 时间字符串，支持格式：
                 - "分:秒" (如 "2:30")
                 - "时:分:秒" (如 "1:2:30")
                 - 纯数字 (如 "150")
                 
    Returns:
        float: 秒数
    """
    try:
        parts = time_str.split(':')
        
        if len(parts) == 1:
            # 纯数字，直接返回
            return float(parts[0])
        elif len(parts) == 2:
            # 分:秒格式
            minutes, seconds = float(parts[0]), float(parts[1])
            return minutes * 60 + seconds
        elif len(parts) == 3:
            # 时:分:秒格式
            hours, minutes, seconds = float(parts[0]), float(parts[1]), float(parts[2])
            return hours * 3600 + minutes * 60 + seconds
        else:
            raise ValueError(f"不支持的时间格式: {time_str}")
            
    except ValueError as e:
        logger.error(f"时间格式转换失败: {str(e)}")
        return 0.0


def create_polygon_mask(frame_shape: Tuple[int, int], points: List[Tuple[float, float]]) -> np.ndarray:
    """
    创建多边形掩码
    
    Args:
        frame_shape: 帧尺寸 (height, width)
        points: 多边形顶点
        
    Returns:
        np.ndarray: 二值掩码
    """
    mask = np.zeros(frame_shape[:2], dtype=np.uint8)
    
    if len(points) >= 3:
        # 转换为整数坐标
        polygon_points = np.array([(int(p[0]), int(p[1])) for p in points], dtype=np.int32)
        cv2.fillPoly(mask, [polygon_points], 255)
    
    return mask


def apply_polygon_roi(frame: np.ndarray, points: List[Tuple[float, float]]) -> np.ndarray:
    """
    应用多边形ROI
    
    Args:
        frame: 输入帧
        points: 多边形顶点
        
    Returns:
        np.ndarray: 应用ROI后的帧
    """
    if len(points) < 3:
        return frame
    
    # 创建掩码
    mask = create_polygon_mask(frame.shape, points)
    
    # 应用掩码
    result = frame.copy()
    result[mask == 0] = 0  # 将ROI外的区域设为黑色
    
    return result
