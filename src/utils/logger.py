"""
日志管理系统

基于流速项目和CV项目的日志系统，提供统一的日志配置、多级别输出和文件轮转功能。
"""

import logging
import logging.handlers
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Optional

from .config_manager import ConfigManager


class CustomFormatter(logging.Formatter):
    """
    自定义日志格式器，支持彩色输出
    """
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\x1b[38;21m',      # 灰色
        'INFO': '\x1b[34;21m',       # 蓝色
        'WARNING': '\x1b[33;21m',    # 黄色
        'ERROR': '\x1b[31;21m',      # 红色
        'CRITICAL': '\x1b[31;1m',    # 粗体红色
    }
    RESET = '\x1b[0m'
    
    def __init__(self, use_color: bool = True):
        """
        初始化格式器
        
        Args:
            use_color: 是否使用彩色输出
        """
        super().__init__()
        self.use_color = use_color
        self.format_str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    def format(self, record):
        """
        格式化日志记录
        
        Args:
            record: 日志记录
            
        Returns:
            str: 格式化后的日志字符串
        """
        if self.use_color and record.levelname in self.COLORS:
            colored_format = (
                self.COLORS[record.levelname] + 
                self.format_str + 
                self.RESET
            )
            formatter = logging.Formatter(colored_format)
        else:
            formatter = logging.Formatter(self.format_str)
        
        return formatter.format(record)


def setup_logger(debug: bool = False, config_manager: Optional[ConfigManager] = None) -> logging.Logger:
    """
    设置全局日志系统
    
    Args:
        debug: 是否启用调试模式
        config_manager: 配置管理器实例
        
    Returns:
        logging.Logger: 根日志器
    """
    # 获取配置
    if config_manager is None:
        config_manager = ConfigManager()
    
    # 获取日志配置
    log_level = config_manager.get("logging.level", "INFO")
    log_file_path = config_manager.get("logging.file_path", "data/logs/app.log")
    max_file_size = config_manager.get("logging.max_file_size", "10MB")
    
    # 如果是调试模式，覆盖日志级别
    if debug:
        log_level = "DEBUG"
    
    # 转换日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()  # 清除现有处理器
    root_logger.setLevel(level)
    
    # 设置第三方库的日志级别，抑制不必要的信息
    _suppress_third_party_logs()
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(CustomFormatter(use_color=True))
    console_handler.setLevel(level)
    root_logger.addHandler(console_handler)
    
    # 添加文件处理器
    if log_file_path:
        file_handler = _create_file_handler(log_file_path, max_file_size, level)
        if file_handler:
            root_logger.addHandler(file_handler)
    
    # 记录日志系统启动信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已启动 - 级别: {log_level}, 文件: {log_file_path}")
    
    return root_logger


def _create_file_handler(log_file_path: str, max_file_size: str, level: int) -> Optional[logging.Handler]:
    """
    创建文件日志处理器，按天分割日志文件

    Args:
        log_file_path: 日志文件路径
        max_file_size: 最大文件大小（保留参数，但使用时间轮转）
        level: 日志级别

    Returns:
        Optional[logging.Handler]: 文件处理器
    """
    try:
        # 确保日志目录存在
        log_dir = Path(log_file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # 构建带日期的日志文件名
        log_path = Path(log_file_path)
        base_name = log_path.stem  # 不带扩展名的文件名
        extension = log_path.suffix  # 扩展名

        # 创建按天轮转的文件处理器
        file_handler = logging.handlers.TimedRotatingFileHandler(
            log_file_path,
            when='midnight',  # 每天午夜轮转
            interval=1,       # 间隔1天
            backupCount=30,   # 保留30天的日志文件
            encoding='utf-8',
            delay=False,
            utc=False
        )

        # 设置日志文件命名格式：app_2025-08-20.log
        file_handler.suffix = "_%Y-%m-%d" + extension

        # 设置文件日志格式（不使用颜色）
        file_formatter = CustomFormatter(use_color=False)
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(level)

        return file_handler

    except Exception as e:
        print(f"创建文件日志处理器失败: {e}")
        return None


def _parse_file_size(size_str: str) -> int:
    """
    解析文件大小字符串
    
    Args:
        size_str: 文件大小字符串，如 "10MB", "1GB"
        
    Returns:
        int: 字节数
    """
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(float(size_str[:-2]) * 1024)
    elif size_str.endswith('MB'):
        return int(float(size_str[:-2]) * 1024 * 1024)
    elif size_str.endswith('GB'):
        return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
    else:
        # 默认按字节处理
        return int(size_str)


def _suppress_third_party_logs():
    """
    抑制第三方库的日志输出
    """
    # 抑制常见的第三方库日志
    third_party_loggers = [
        'matplotlib',
        'urllib3',
        'requests',
        'PIL',
        'cv2',
        'numpy',
        'asyncio'
    ]
    
    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(logging.WARNING)
    
    # 设置OpenCV和FFmpeg环境变量，抑制视频处理相关的警告
    try:
        os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '-8'  # AV_LOG_QUIET
        os.environ['OPENCV_LOG_LEVEL'] = 'SILENT'
        os.environ['OPENCV_FFMPEG_READ_ATTEMPTS'] = '10'
        
        # 尝试设置OpenCV日志级别
        try:
            import cv2
            if hasattr(cv2, 'setLogLevel'):
                cv2.setLogLevel(0)  # 静默模式
        except ImportError:
            pass  # OpenCV未安装时忽略
            
    except Exception:
        pass  # 忽略环境变量设置失败


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)
