"""
分析API接口

提供水位识别、流速识别和异常检测的API接口。
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse

from ..models.schemas import (
    WaterLevelRequest, WaterLevelResponse,
    FlowSpeedRequest, FlowSpeedResponse,
    AnomalyRequest, AnomalyResponse
)
from ..services.analysis_service import AnalysisService
from ..utils.config_manager import ConfigManager


router = APIRouter()


def get_analysis_service(request: Request) -> AnalysisService:
    """
    获取分析服务实例
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        AnalysisService: 分析服务实例
    """
    return request.app.state.analysis_service


@router.post("/analyze/water-level", response_model=WaterLevelResponse)
async def analyze_water_level(
    request: WaterLevelRequest,
    analysis_service: AnalysisService = Depends(get_analysis_service)
) -> WaterLevelResponse:
    """
    水位识别分析
    
    Args:
        request: 水位分析请求
        analysis_service: 分析服务实例
        
    Returns:
        WaterLevelResponse: 水位分析结果
    """
    try:
        # 生成任务ID
        task_id = f"wl_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # 执行水位分析
        result = await analysis_service.analyze_water_level(
            video_url=request.video_url,
            parameters=request.parameters,
            task_id=task_id
        )
        
        return WaterLevelResponse(
            task_id=task_id,
            status="completed",
            result=result,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": {
                    "code": "WATER_LEVEL_ANALYSIS_ERROR",
                    "message": "水位分析失败",
                    "details": str(e)
                },
                "task_id": task_id if 'task_id' in locals() else None,
                "timestamp": datetime.now().isoformat()
            }
        )


@router.post("/analyze/flow-speed", response_model=FlowSpeedResponse)
async def analyze_flow_speed(
    request: FlowSpeedRequest,
    analysis_service: AnalysisService = Depends(get_analysis_service)
) -> FlowSpeedResponse:
    """
    流速识别分析
    
    Args:
        request: 流速分析请求
        analysis_service: 分析服务实例
        
    Returns:
        FlowSpeedResponse: 流速分析结果
    """
    try:
        # 生成任务ID
        task_id = f"fs_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # 执行流速分析
        result = await analysis_service.analyze_flow_speed(
            video_url=request.video_url,
            parameters=request.parameters,
            task_id=task_id
        )
        
        return FlowSpeedResponse(
            task_id=task_id,
            status="completed",
            result=result,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": {
                    "code": "FLOW_SPEED_ANALYSIS_ERROR",
                    "message": "流速分析失败",
                    "details": str(e)
                },
                "task_id": task_id if 'task_id' in locals() else None,
                "timestamp": datetime.now().isoformat()
            }
        )


@router.post("/analyze/anomaly", response_model=AnomalyResponse)
async def analyze_anomaly(
    request: AnomalyRequest,
    analysis_service: AnalysisService = Depends(get_analysis_service)
) -> AnomalyResponse:
    """
    异常检测分析
    
    Args:
        request: 异常检测请求
        analysis_service: 分析服务实例
        
    Returns:
        AnomalyResponse: 异常检测结果
    """
    try:
        # 生成任务ID
        task_id = f"ad_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
        # 执行异常检测
        result = await analysis_service.analyze_anomaly(
            video_url=request.video_url,
            parameters=request.parameters,
            task_id=task_id
        )
        
        return AnomalyResponse(
            task_id=task_id,
            status="completed",
            result=result,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": {
                    "code": "ANOMALY_ANALYSIS_ERROR",
                    "message": "异常检测失败",
                    "details": str(e)
                },
                "task_id": task_id if 'task_id' in locals() else None,
                "timestamp": datetime.now().isoformat()
            }
        )
