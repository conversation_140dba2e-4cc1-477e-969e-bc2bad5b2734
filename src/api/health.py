"""
健康检查API接口

提供系统健康状态检查和配置信息查询功能。
"""

from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse

from ..utils.config_manager import ConfigManager


router = APIRouter()


def get_config_manager() -> ConfigManager:
    """
    获取配置管理器实例
    
    Returns:
        ConfigManager: 配置管理器实例
    """
    return ConfigManager()


@router.get("/health")
async def health_check(config_manager: ConfigManager = Depends(get_config_manager)) -> Dict[str, Any]:
    """
    系统健康检查
    
    Returns:
        Dict[str, Any]: 系统健康状态信息
    """
    try:
        # 获取分析器配置
        analyzers_config = config_manager.get("analyzers", {})
        
        # 检查各个分析器状态
        services_status = {}
        for analyzer_name, analyzer_config in analyzers_config.items():
            services_status[analyzer_name] = "active" if analyzer_config.get("enabled", False) else "inactive"
        
        return {
            "status": "healthy",
            "version": "1.0.0",
            "services": services_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )


@router.get("/config")
async def get_config(config_manager: ConfigManager = Depends(get_config_manager)) -> Dict[str, Any]:
    """
    获取系统配置信息
    
    Returns:
        Dict[str, Any]: 系统配置信息
    """
    try:
        analyzers_config = config_manager.get("analyzers", {})
        
        # 构建配置响应
        config_response = {"analyzers": {}}
        
        for analyzer_name, analyzer_config in analyzers_config.items():
            if analyzer_name == "water_level":
                config_response["analyzers"][analyzer_name] = {
                    "enabled": analyzer_config.get("enabled", False),
                    "default_method": analyzer_config.get("default_method", "transparency"),
                    "available_methods": ["transparency", "gradient", "hough", "color_threshold", "ruler_detection"]
                }
            elif analyzer_name == "flow_speed":
                config_response["analyzers"][analyzer_name] = {
                    "enabled": analyzer_config.get("enabled", False),
                    "default_method": analyzer_config.get("default_method", "otv"),
                    "available_methods": ["otv", "piv"]
                }
            elif analyzer_name == "anomaly":
                config_response["analyzers"][analyzer_name] = {
                    "enabled": analyzer_config.get("enabled", False),
                    "model_path": analyzer_config.get("model_path", "models/yolo11n.pt"),
                    "supported_targets": ["person", "boat", "vehicle", "bicycle", "motorcycle"]
                }
        
        return config_response
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "CONFIG_ERROR",
                    "message": "配置获取失败",
                    "details": str(e)
                }
            }
        )
