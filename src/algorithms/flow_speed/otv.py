"""
OTV (Optical Tracking Velocimetry) 算法

基于流速项目的OTV算法，使用OpenCV光流跟踪计算流速。
"""

import cv2
import numpy as np
import pandas as pd
import logging
from typing import Optional, List, Tuple, Dict, Any
from dataclasses import dataclass
import time

from ...utils.config_manager import ConfigManager


logger = logging.getLogger(__name__)


@dataclass
class TrackingPoint:
    """特征点数据结构"""
    point_id: int
    time: float
    x: float
    y: float
    displacement: float
    angle: float
    start_x: float
    start_y: float


@dataclass
class FlowResult:
    """流速分析结果"""
    min_speed: float
    avg_speed: float
    max_speed: float
    flow_direction: float
    confidence: float
    point_count: int
    velocity_field: Optional[List[List[Tuple[float, float]]]] = None


class OTVAnalyzer:
    """
    OTV光流分析器
    
    基于OpenCV光流跟踪技术分析视频流速
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化OTV分析器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取OTV配置参数
        self.feature_params = self._get_feature_params()
        self.lk_params = self._get_lk_params()
        self.tracking_params = self._get_tracking_params()
        
        # 初始化CLAHE对比度增强
        self.clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    
    def analyze_flow(
        self, 
        video_frames: List[np.ndarray], 
        roi_points: Optional[List[Tuple[float, float]]] = None,
        pixel_to_meter: float = 1.0,
        fps: float = 30.0,
        debug: bool = False
    ) -> FlowResult:
        """
        分析视频帧序列的流速
        
        Args:
            video_frames: 视频帧列表
            roi_points: ROI区域顶点列表
            pixel_to_meter: 像素到米的转换比例
            fps: 视频帧率
            debug: 调试模式
            
        Returns:
            FlowResult: 流速分析结果
        """
        try:
            if len(video_frames) < 2:
                self.logger.error("需要至少2帧进行光流分析")
                return FlowResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
            
            # 创建ROI掩码
            roi_mask = self._create_roi_mask(video_frames[0], roi_points)
            
            # 检测初始特征点
            first_frame_gray = cv2.cvtColor(video_frames[0], cv2.COLOR_BGR2GRAY)
            first_frame_enhanced = self.clahe.apply(first_frame_gray)
            
            # 检测特征点
            corners = cv2.goodFeaturesToTrack(
                first_frame_enhanced,
                mask=roi_mask,
                **self.feature_params
            )
            
            if corners is None or len(corners) == 0:
                self.logger.warning("未检测到有效特征点")
                return FlowResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
            
            # 过滤特征点
            filtered_corners = self._filter_features(corners, roi_mask)
            
            if len(filtered_corners) == 0:
                self.logger.warning("过滤后无有效特征点")
                return FlowResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
            
            # 跟踪特征点
            tracking_data = self._track_features(
                video_frames, filtered_corners, fps, debug
            )
            
            if tracking_data.empty:
                self.logger.warning("特征点跟踪失败")
                return FlowResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
            
            # 计算流速
            flow_result = self._calculate_flow_statistics(
                tracking_data, pixel_to_meter, debug
            )
            
            if debug:
                self.logger.info(f"OTV分析完成: 平均流速={flow_result.avg_speed:.3f}m/s, 特征点数={flow_result.point_count}")
            
            return flow_result
            
        except Exception as e:
            self.logger.error(f"OTV流速分析失败: {str(e)}")
            return FlowResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
    
    def _get_feature_params(self) -> Dict[str, Any]:
        """获取特征点检测参数"""
        return {
            'maxCorners': self.config_manager.get("analyzers.flow_speed.otv.feature_params.maxCorners", 100),
            'qualityLevel': self.config_manager.get("analyzers.flow_speed.otv.feature_params.qualityLevel", 0.3),
            'minDistance': self.config_manager.get("analyzers.flow_speed.otv.feature_params.minDistance", 7),
            'blockSize': self.config_manager.get("analyzers.flow_speed.otv.feature_params.blockSize", 7)
        }
    
    def _get_lk_params(self) -> Dict[str, Any]:
        """获取Lucas-Kanade光流参数"""
        win_size = self.config_manager.get("analyzers.flow_speed.otv.lk_params.winSize", [15, 15])
        return {
            'winSize': tuple(win_size),
            'maxLevel': self.config_manager.get("analyzers.flow_speed.otv.lk_params.maxLevel", 2),
            'criteria': (
                cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT,
                self.config_manager.get("analyzers.flow_speed.otv.lk_params.criteria_count", 10),
                self.config_manager.get("analyzers.flow_speed.otv.lk_params.criteria_eps", 0.03)
            )
        }
    
    def _get_tracking_params(self) -> Dict[str, Any]:
        """获取跟踪参数"""
        return {
            'max_track_len': self.config_manager.get("analyzers.flow_speed.otv.tracking.max_track_len", 10),
            'track_interval': self.config_manager.get("analyzers.flow_speed.otv.tracking.track_interval", 5),
            'detect_interval': self.config_manager.get("analyzers.flow_speed.otv.tracking.detect_interval", 5)
        }
    
    def _create_roi_mask(self, frame: np.ndarray, roi_points: Optional[List[Tuple[float, float]]]) -> np.ndarray:
        """创建ROI掩码"""
        mask = np.ones(frame.shape[:2], dtype=np.uint8) * 255
        
        if roi_points and len(roi_points) >= 3:
            # 转换为整数坐标
            points = np.array([(int(p[0]), int(p[1])) for p in roi_points], dtype=np.int32)
            mask = np.zeros(frame.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask, [points], 255)
        
        return mask
    
    def _filter_features(self, corners: np.ndarray, roi_mask: np.ndarray) -> np.ndarray:
        """过滤特征点，移除网格交点和文字区域"""
        if corners is None:
            return np.array([])
        
        filtered_corners = []
        grid_step = 50  # 网格步长
        
        for corner in corners:
            x, y = corner.ravel()
            
            # 过滤网格交点附近的点（容差5像素）
            is_near_grid_x = abs(x % grid_step) < 5 or abs(x % grid_step) > (grid_step - 5)
            is_near_grid_y = abs(y % grid_step) < 5 or abs(y % grid_step) > (grid_step - 5)
            
            # 过滤文字区域（左上角区域）
            is_in_text_area = (x < 200 and y < 150)
            
            # 检查是否在ROI内
            is_in_roi = True
            if 0 <= int(y) < roi_mask.shape[0] and 0 <= int(x) < roi_mask.shape[1]:
                is_in_roi = roi_mask[int(y), int(x)] > 0
            
            # 保留有效点
            if not (is_near_grid_x and is_near_grid_y) and not is_in_text_area and is_in_roi:
                filtered_corners.append(corner)
        
        return np.array(filtered_corners) if filtered_corners else np.array([])
    
    def _track_features(
        self, 
        video_frames: List[np.ndarray], 
        initial_corners: np.ndarray,
        fps: float,
        debug: bool = False
    ) -> pd.DataFrame:
        """跟踪特征点"""
        tracking_data = []
        
        # 初始化跟踪点
        p0 = initial_corners.copy()
        prev_gray = cv2.cvtColor(video_frames[0], cv2.COLOR_BGR2GRAY)
        prev_gray_enhanced = self.clahe.apply(prev_gray)
        
        # 为每个特征点分配ID
        point_ids = list(range(len(p0)))
        start_positions = {i: p0[i].ravel() for i in point_ids}
        
        for frame_idx in range(1, len(video_frames)):
            frame = video_frames[frame_idx]
            frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            frame_gray_enhanced = self.clahe.apply(frame_gray)
            
            current_time = frame_idx / fps
            
            if len(p0) > 0:
                # 光流跟踪
                p1, st, err = cv2.calcOpticalFlowPyrLK(
                    prev_gray_enhanced, frame_gray_enhanced, p0, None, **self.lk_params
                )
                
                # 处理跟踪结果
                good_new = p1[st == 1]
                good_old = p0[st == 1]
                good_ids = [point_ids[i] for i in range(len(st)) if st[i] == 1]
                
                # 计算位移和角度
                for i, (new_pt, old_pt, point_id) in enumerate(zip(good_new, good_old, good_ids)):
                    x_new, y_new = new_pt.ravel()
                    x_old, y_old = old_pt.ravel()
                    
                    # 计算位移
                    dx = x_new - x_old
                    dy = y_new - y_old
                    displacement = np.sqrt(dx**2 + dy**2)
                    
                    # 计算角度
                    angle = np.arctan2(dy, dx) * 180 / np.pi
                    
                    # 记录跟踪数据
                    start_x, start_y = start_positions[point_id]
                    tracking_data.append({
                        'point_id': point_id,
                        'time': current_time,
                        'x': x_new,
                        'y': y_new,
                        'displacement': displacement,
                        'angle': angle,
                        'start_x': start_x,
                        'start_y': start_y,
                        'frame_idx': frame_idx
                    })
                
                # 更新跟踪点
                p0 = good_new.reshape(-1, 1, 2)
                point_ids = good_ids
            
            prev_gray_enhanced = frame_gray_enhanced
        
        return pd.DataFrame(tracking_data)
    
    def _calculate_flow_statistics(
        self, 
        tracking_data: pd.DataFrame, 
        pixel_to_meter: float,
        debug: bool = False
    ) -> FlowResult:
        """计算流速统计信息"""
        if tracking_data.empty:
            return FlowResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
        
        # 计算每个点的速度
        speeds = []
        angles = []
        
        for point_id in tracking_data['point_id'].unique():
            point_data = tracking_data[tracking_data['point_id'] == point_id].sort_values('time')
            
            if len(point_data) < 2:
                continue
            
            # 计算总位移和时间
            total_displacement = 0
            total_time = 0
            point_angles = []
            
            for i in range(1, len(point_data)):
                curr_row = point_data.iloc[i]
                prev_row = point_data.iloc[i-1]
                
                dx = curr_row['x'] - prev_row['x']
                dy = curr_row['y'] - prev_row['y']
                dt = curr_row['time'] - prev_row['time']
                
                if dt > 0:
                    displacement = np.sqrt(dx**2 + dy**2) * pixel_to_meter
                    total_displacement += displacement
                    total_time += dt
                    
                    # 记录角度
                    angle = np.arctan2(dy, dx) * 180 / np.pi
                    point_angles.append(angle)
            
            if total_time > 0:
                speed = total_displacement / total_time
                speeds.append(speed)
                
                if point_angles:
                    avg_angle = np.mean(point_angles)
                    angles.append(avg_angle)
        
        if not speeds:
            return FlowResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
        
        # 计算统计值
        min_speed = float(np.min(speeds))
        avg_speed = float(np.mean(speeds))
        max_speed = float(np.max(speeds))
        
        # 计算主流向
        flow_direction = float(np.mean(angles)) if angles else 0.0
        
        # 计算置信度（基于数据点数量和速度一致性）
        confidence = min(1.0, len(speeds) / 50.0)  # 50个点为满分
        if len(speeds) > 1:
            speed_std = np.std(speeds)
            speed_cv = speed_std / avg_speed if avg_speed > 0 else 1.0
            confidence *= max(0.1, 1.0 - speed_cv)  # 变异系数越小置信度越高
        
        if debug:
            self.logger.info(f"流速统计: 最小={min_speed:.3f}, 平均={avg_speed:.3f}, 最大={max_speed:.3f}, 方向={flow_direction:.1f}°")
        
        return FlowResult(
            min_speed=min_speed,
            avg_speed=avg_speed,
            max_speed=max_speed,
            flow_direction=flow_direction,
            confidence=float(confidence),
            point_count=len(speeds)
        )


def analyze_flow_otv(
    video_frames: List[np.ndarray],
    roi_points: Optional[List[Tuple[float, float]]] = None,
    pixel_to_meter: float = 1.0,
    fps: float = 30.0,
    config_manager: Optional[ConfigManager] = None,
    debug: bool = False
) -> FlowResult:
    """
    OTV流速分析的便捷函数
    
    Args:
        video_frames: 视频帧列表
        roi_points: ROI区域顶点
        pixel_to_meter: 像素到米转换比例
        fps: 视频帧率
        config_manager: 配置管理器
        debug: 调试模式
        
    Returns:
        FlowResult: 流速分析结果
    """
    analyzer = OTVAnalyzer(config_manager)
    return analyzer.analyze_flow(video_frames, roi_points, pixel_to_meter, fps, debug)
