"""
PIV (Particle Image Velocimetry) 算法

基于流速项目的PIV算法，使用OpenPIV库进行互相关分析计算流速。
"""

import cv2
import numpy as np
import logging
from typing import Optional, List, Tuple, Dict, Any
from dataclasses import dataclass

try:
    from openpiv import tools, pyprocess, validation, filters, scaling
    OPENPIV_AVAILABLE = True
except ImportError:
    OPENPIV_AVAILABLE = False
    logging.warning("OpenPIV库未安装，PIV算法将不可用")

from ...utils.config_manager import ConfigManager


logger = logging.getLogger(__name__)


@dataclass
class PIVResult:
    """PIV分析结果"""
    min_speed: float
    avg_speed: float
    max_speed: float
    flow_direction: float
    confidence: float
    vector_count: int
    velocity_field: Optional[List[List[Tuple[float, float]]]] = None


class PIVAnalyzer:
    """
    PIV粒子图像测速分析器
    
    基于OpenPIV库进行互相关分析
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化PIV分析器
        
        Args:
            config_manager: 配置管理器实例
        """
        if not OPENPIV_AVAILABLE:
            raise ImportError("OpenPIV库未安装，无法使用PIV算法")
        
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取PIV配置参数
        self.window_size = self.config_manager.get("analyzers.flow_speed.piv.window_size", 32)
        self.overlap = self.config_manager.get("analyzers.flow_speed.piv.overlap", 16)
        self.search_area_size = self.config_manager.get("analyzers.flow_speed.piv.search_area_size", 64)
        self.sig2noise_threshold = self.config_manager.get("analyzers.flow_speed.piv.sig2noise_threshold", 1.05)
        self.correlation_method = self.config_manager.get("analyzers.flow_speed.piv.correlation_method", "circular")
        self.outlier_method = self.config_manager.get("analyzers.flow_speed.piv.outlier_method", "localmean")
        self.outlier_max_iter = self.config_manager.get("analyzers.flow_speed.piv.outlier_max_iter", 3)
        self.outlier_kernel_size = self.config_manager.get("analyzers.flow_speed.piv.outlier_kernel_size", 3)
        
        # 获取时间间隔参数
        self.dt = self.config_manager.get("analyzers.flow_speed.piv.dt", 0.02)
    
    def analyze_flow(
        self, 
        video_frames: List[np.ndarray], 
        roi_points: Optional[List[Tuple[float, float]]] = None,
        pixel_to_meter: float = 1.0,
        debug: bool = False
    ) -> PIVResult:
        """
        分析视频帧序列的流速
        
        Args:
            video_frames: 视频帧列表
            roi_points: ROI区域顶点列表
            pixel_to_meter: 像素到米的转换比例
            debug: 调试模式
            
        Returns:
            PIVResult: PIV分析结果
        """
        try:
            if len(video_frames) < 2:
                self.logger.error("需要至少2帧进行PIV分析")
                return PIVResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
            
            # 分析所有帧对
            all_speeds = []
            all_directions = []
            total_vectors = 0
            
            for i in range(len(video_frames) - 1):
                frame_a = video_frames[i]
                frame_b = video_frames[i + 1]
                
                # 分析单个帧对
                result = self._analyze_frame_pair(
                    frame_a, frame_b, roi_points, pixel_to_meter, debug
                )
                
                if result['success']:
                    all_speeds.extend(result['speeds'])
                    all_directions.extend(result['directions'])
                    total_vectors += result['vector_count']
            
            if not all_speeds:
                self.logger.warning("所有帧对分析均失败")
                return PIVResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
            
            # 计算统计值
            min_speed = float(np.min(all_speeds))
            avg_speed = float(np.mean(all_speeds))
            max_speed = float(np.max(all_speeds))
            
            # 计算主流向
            flow_direction = float(np.mean(all_directions)) if all_directions else 0.0
            
            # 计算置信度
            confidence = min(1.0, total_vectors / 100.0)  # 100个向量为满分
            if len(all_speeds) > 1:
                speed_std = np.std(all_speeds)
                speed_cv = speed_std / avg_speed if avg_speed > 0 else 1.0
                confidence *= max(0.1, 1.0 - speed_cv)
            
            if debug:
                self.logger.info(
                    f"PIV分析完成: 最小={min_speed:.3f}, 平均={avg_speed:.3f}, "
                    f"最大={max_speed:.3f}, 方向={flow_direction:.1f}°, 向量数={total_vectors}"
                )
            
            return PIVResult(
                min_speed=min_speed,
                avg_speed=avg_speed,
                max_speed=max_speed,
                flow_direction=flow_direction,
                confidence=float(confidence),
                vector_count=total_vectors
            )
            
        except Exception as e:
            self.logger.error(f"PIV流速分析失败: {str(e)}")
            return PIVResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
    
    def _analyze_frame_pair(
        self, 
        frame_a: np.ndarray, 
        frame_b: np.ndarray,
        roi_points: Optional[List[Tuple[float, float]]],
        pixel_to_meter: float,
        debug: bool = False
    ) -> Dict[str, Any]:
        """
        分析单个帧对
        
        Args:
            frame_a: 第一帧
            frame_b: 第二帧
            roi_points: ROI区域顶点
            pixel_to_meter: 像素到米转换比例
            debug: 调试模式
            
        Returns:
            Dict: 分析结果
        """
        try:
            # 转换为灰度图
            if len(frame_a.shape) == 3:
                frame_a_gray = cv2.cvtColor(frame_a, cv2.COLOR_BGR2GRAY)
            else:
                frame_a_gray = frame_a
                
            if len(frame_b.shape) == 3:
                frame_b_gray = cv2.cvtColor(frame_b, cv2.COLOR_BGR2GRAY)
            else:
                frame_b_gray = frame_b
            
            # 应用ROI裁剪
            offset_x, offset_y = 0, 0
            if roi_points and len(roi_points) >= 3:
                frame_a_processed, frame_b_processed, offset_x, offset_y = self._apply_roi_cropping(
                    frame_a_gray, frame_b_gray, roi_points
                )
            else:
                frame_a_processed = frame_a_gray
                frame_b_processed = frame_b_gray
            
            # 执行PIV分析
            u0, v0, sig2noise = pyprocess.extended_search_area_piv(
                frame_a_processed.astype(np.int32),
                frame_b_processed.astype(np.int32),
                window_size=self.window_size,
                overlap=self.overlap,
                dt=self.dt,
                search_area_size=self.search_area_size,
                sig2noise_method='peak2peak',
                correlation_method=self.correlation_method
            )
            
            # 获取坐标
            x_relative, y_relative = pyprocess.get_coordinates(
                image_size=frame_a_processed.shape,
                search_area_size=self.search_area_size,
                overlap=self.overlap
            )
            
            # 验证和滤波
            mask = validation.sig2noise_val(sig2noise, threshold=self.sig2noise_threshold)
            u_filtered, v_filtered = filters.replace_outliers(
                u0, v0, mask,
                method=self.outlier_method,
                max_iter=self.outlier_max_iter,
                kernel_size=self.outlier_kernel_size
            )
            
            # 标记无效向量
            u_filtered[mask] = np.nan
            v_filtered[mask] = np.nan
            
            # 缩放到物理单位
            u_scaled = u_filtered * pixel_to_meter
            v_scaled = v_filtered * pixel_to_meter
            
            # 计算速度和方向
            speeds = []
            directions = []
            
            for r_idx in range(u_scaled.shape[0]):
                for c_idx in range(u_scaled.shape[1]):
                    u_val = u_scaled[r_idx, c_idx]
                    v_val = v_scaled[r_idx, c_idx]
                    
                    if not (np.isnan(u_val) or np.isnan(v_val)):
                        speed = np.sqrt(u_val**2 + v_val**2)
                        direction = np.arctan2(v_val, u_val) * 180 / np.pi
                        
                        speeds.append(speed)
                        directions.append(direction)
            
            if debug and speeds:
                self.logger.debug(f"帧对分析: 有效向量数={len(speeds)}, 平均速度={np.mean(speeds):.3f}m/s")
            
            return {
                'success': len(speeds) > 0,
                'speeds': speeds,
                'directions': directions,
                'vector_count': len(speeds),
                'u_field': u_scaled,
                'v_field': v_scaled
            }
            
        except Exception as e:
            self.logger.error(f"帧对分析失败: {str(e)}")
            return {
                'success': False,
                'speeds': [],
                'directions': [],
                'vector_count': 0
            }
    
    def _apply_roi_cropping(
        self, 
        frame_a: np.ndarray, 
        frame_b: np.ndarray, 
        roi_points: List[Tuple[float, float]]
    ) -> Tuple[np.ndarray, np.ndarray, int, int]:
        """
        应用ROI裁剪
        
        Args:
            frame_a: 第一帧
            frame_b: 第二帧
            roi_points: ROI顶点
            
        Returns:
            Tuple: (裁剪后的frame_a, 裁剪后的frame_b, x偏移, y偏移)
        """
        try:
            # 计算ROI边界框
            roi_array = np.array(roi_points)
            ymin = max(0, int(np.min(roi_array[:, 1])))
            ymax = min(frame_a.shape[0], int(np.max(roi_array[:, 1])))
            xmin = max(0, int(np.min(roi_array[:, 0])))
            xmax = min(frame_a.shape[1], int(np.max(roi_array[:, 0])))
            
            # 裁剪帧
            frame_a_cropped = frame_a[ymin:ymax, xmin:xmax]
            frame_b_cropped = frame_b[ymin:ymax, xmin:xmax]
            
            return frame_a_cropped, frame_b_cropped, xmin, ymin
            
        except Exception as e:
            self.logger.error(f"ROI裁剪失败: {str(e)}")
            return frame_a, frame_b, 0, 0


def analyze_flow_piv(
    video_frames: List[np.ndarray],
    roi_points: Optional[List[Tuple[float, float]]] = None,
    pixel_to_meter: float = 1.0,
    config_manager: Optional[ConfigManager] = None,
    debug: bool = False
) -> PIVResult:
    """
    PIV流速分析的便捷函数
    
    Args:
        video_frames: 视频帧列表
        roi_points: ROI区域顶点
        pixel_to_meter: 像素到米转换比例
        config_manager: 配置管理器
        debug: 调试模式
        
    Returns:
        PIVResult: PIV分析结果
    """
    if not OPENPIV_AVAILABLE:
        logger.error("OpenPIV库未安装，无法使用PIV算法")
        return PIVResult(0.0, 0.0, 0.0, 0.0, 0.0, 0)
    
    analyzer = PIVAnalyzer(config_manager)
    return analyzer.analyze_flow(video_frames, roi_points, pixel_to_meter, debug)
