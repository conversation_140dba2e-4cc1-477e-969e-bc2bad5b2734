"""
YOLO异常检测算法

基于CV项目的YOLO检测器，使用Ultralytics YOLO模型进行目标检测。
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import time

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    logging.warning("Ultralytics YOLO库未安装，YOLO检测器将不可用")

from ...utils.config_manager import ConfigManager


logger = logging.getLogger(__name__)


@dataclass
class Detection:
    """单个检测结果"""
    object_type: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    timestamp: str


@dataclass
class AnomalyResult:
    """异常检测结果"""
    anomalies_detected: bool
    detections: List[Detection]
    risk_level: str
    total_detections: int
    processing_time: float


class YOLODetector:
    """
    YOLO异常检测器
    
    基于Ultralytics YOLO模型进行目标检测和异常识别
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化YOLO检测器
        
        Args:
            config_manager: 配置管理器实例
        """
        if not ULTRALYTICS_AVAILABLE:
            raise ImportError("Ultralytics YOLO库未安装，无法使用YOLO检测器")
        
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取配置参数
        self.model_path = self.config_manager.get("analyzers.anomaly.model_path", "models/yolo11n.pt")
        self.confidence_threshold = self.config_manager.get("analyzers.anomaly.confidence_threshold", 0.5)
        self.nms_threshold = self.config_manager.get("analyzers.anomaly.yolo.nms_threshold", 0.45)
        self.input_size = self.config_manager.get("analyzers.anomaly.yolo.input_size", 640)
        self.max_detections = self.config_manager.get("analyzers.anomaly.yolo.max_detections", 100)
        
        # 支持的检测目标
        self.detection_targets = self.config_manager.get(
            "analyzers.anomaly.detection_targets", 
            ["person", "boat", "vehicle", "bicycle", "motorcycle"]
        )
        
        # 加载模型
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载YOLO模型"""
        try:
            self.logger.info(f"正在加载YOLO模型: {self.model_path}")
            self.model = YOLO(self.model_path)
            self.logger.info("YOLO模型加载成功")
        except Exception as e:
            self.logger.error(f"YOLO模型加载失败: {str(e)}")
            raise
    
    def reload_model(self, model_path: Optional[str] = None) -> bool:
        """
        重新加载模型
        
        Args:
            model_path: 新模型路径，如果为None则使用当前路径
            
        Returns:
            bool: 是否成功重新加载
        """
        try:
            if model_path is None:
                model_path = self.model_path
            
            self.logger.info(f"开始重新加载模型: {model_path}")
            
            # 保存旧模型引用
            old_model = self.model
            
            # 加载新模型
            new_model = YOLO(model_path)
            
            # 更新模型引用
            self.model = new_model
            self.model_path = model_path
            
            self.logger.info(f"模型重新加载成功: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型重新加载失败: {str(e)}")
            # 恢复旧模型
            if 'old_model' in locals():
                self.model = old_model
                self.logger.info("已恢复使用旧模型")
            return False
    
    def detect_anomalies(
        self, 
        video_frames: List[np.ndarray],
        detection_targets: Optional[List[str]] = None,
        confidence_threshold: Optional[float] = None,
        debug: bool = False
    ) -> AnomalyResult:
        """
        检测视频帧中的异常
        
        Args:
            video_frames: 视频帧列表
            detection_targets: 检测目标列表
            confidence_threshold: 置信度阈值
            debug: 调试模式
            
        Returns:
            AnomalyResult: 异常检测结果
        """
        start_time = time.time()
        
        try:
            if not video_frames:
                self.logger.warning("没有提供视频帧")
                return AnomalyResult(False, [], "low", 0, 0.0)
            
            # 使用提供的参数或默认值
            targets = detection_targets or self.detection_targets
            conf_thresh = confidence_threshold or self.confidence_threshold
            
            all_detections = []
            
            # 处理每一帧
            for frame_idx, frame in enumerate(video_frames):
                frame_detections = self._detect_frame(
                    frame, targets, conf_thresh, frame_idx, debug
                )
                all_detections.extend(frame_detections)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 分析结果
            anomalies_detected = len(all_detections) > 0
            risk_level = self._calculate_risk_level(all_detections)
            
            if debug:
                self.logger.info(
                    f"异常检测完成: 检测到{len(all_detections)}个目标, "
                    f"风险等级={risk_level}, 处理时间={processing_time:.2f}秒"
                )
            
            return AnomalyResult(
                anomalies_detected=anomalies_detected,
                detections=all_detections,
                risk_level=risk_level,
                total_detections=len(all_detections),
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"异常检测失败: {str(e)}")
            processing_time = time.time() - start_time
            return AnomalyResult(False, [], "low", 0, processing_time)
    
    def _detect_frame(
        self, 
        frame: np.ndarray, 
        targets: List[str], 
        confidence_threshold: float,
        frame_idx: int,
        debug: bool = False
    ) -> List[Detection]:
        """
        检测单帧中的目标
        
        Args:
            frame: 输入帧
            targets: 检测目标列表
            confidence_threshold: 置信度阈值
            frame_idx: 帧索引
            debug: 调试模式
            
        Returns:
            List[Detection]: 检测结果列表
        """
        try:
            # 执行YOLO检测
            results = self.model(frame, conf=confidence_threshold, iou=self.nms_threshold)
            
            detections = []
            
            for result in results:
                if result.boxes is None:
                    continue
                
                for box in result.boxes:
                    # 获取类别名称
                    cls_id = int(box.cls)
                    cls_name = self.model.names[cls_id]
                    confidence = float(box.conf)
                    
                    # 检查是否是目标类别
                    if cls_name.lower() in [t.lower() for t in targets]:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = map(int, box.xyxy.tolist()[0])
                        
                        # 创建检测结果
                        detection = Detection(
                            object_type=cls_name,
                            confidence=confidence,
                            bbox=(x1, y1, x2, y2),
                            timestamp=f"frame_{frame_idx}"
                        )
                        
                        detections.append(detection)
                        
                        if debug:
                            self.logger.debug(
                                f"帧{frame_idx}: 检测到{cls_name}, 置信度={confidence:.3f}, "
                                f"位置=({x1},{y1},{x2},{y2})"
                            )
            
            return detections
            
        except Exception as e:
            self.logger.error(f"帧{frame_idx}检测失败: {str(e)}")
            return []
    
    def _calculate_risk_level(self, detections: List[Detection]) -> str:
        """
        根据检测结果计算风险等级
        
        Args:
            detections: 检测结果列表
            
        Returns:
            str: 风险等级 (low/medium/high/critical)
        """
        if not detections:
            return "low"
        
        detection_count = len(detections)
        high_confidence_count = sum(1 for d in detections if d.confidence > 0.8)
        
        # 统计不同类型的目标
        person_count = sum(1 for d in detections if d.object_type.lower() == "person")
        vehicle_count = sum(1 for d in detections if d.object_type.lower() in ["vehicle", "boat", "bicycle", "motorcycle"])
        
        # 根据检测数量和类型计算风险等级
        if person_count >= 3 or vehicle_count >= 2 or high_confidence_count >= 3:
            return "critical"
        elif person_count >= 2 or vehicle_count >= 1 or high_confidence_count >= 2:
            return "high"
        elif detection_count >= 1 or high_confidence_count >= 1:
            return "medium"
        else:
            return "low"
    
    def detect_single_frame(
        self, 
        frame: np.ndarray, 
        targets: List[str],
        confidence_threshold: Optional[float] = None
    ) -> List[Detection]:
        """
        检测单帧图像
        
        Args:
            frame: 输入帧
            targets: 检测目标列表
            confidence_threshold: 置信度阈值
            
        Returns:
            List[Detection]: 检测结果列表
        """
        conf_thresh = confidence_threshold or self.confidence_threshold
        return self._detect_frame(frame, targets, conf_thresh, 0, False)
    
    def annotate_frame(self, frame: np.ndarray, detections: List[Detection]) -> np.ndarray:
        """
        在帧上标注检测结果
        
        Args:
            frame: 输入帧
            detections: 检测结果列表
            
        Returns:
            np.ndarray: 标注后的帧
        """
        annotated_frame = frame.copy()
        
        for detection in detections:
            x1, y1, x2, y2 = detection.bbox
            
            # 绘制边界框
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{detection.object_type} {detection.confidence:.2f}"
            cv2.putText(
                annotated_frame, label, (x1, y1 - 10),
                cv2.FONT_HERSHEY_SIMPLEX, 0.9, (36, 255, 12), 2
            )
        
        return annotated_frame


def detect_anomalies_yolo(
    video_frames: List[np.ndarray],
    detection_targets: Optional[List[str]] = None,
    confidence_threshold: Optional[float] = None,
    config_manager: Optional[ConfigManager] = None,
    debug: bool = False
) -> AnomalyResult:
    """
    YOLO异常检测的便捷函数
    
    Args:
        video_frames: 视频帧列表
        detection_targets: 检测目标列表
        confidence_threshold: 置信度阈值
        config_manager: 配置管理器
        debug: 调试模式
        
    Returns:
        AnomalyResult: 异常检测结果
    """
    if not ULTRALYTICS_AVAILABLE:
        logger.error("Ultralytics YOLO库未安装，无法使用YOLO检测器")
        return AnomalyResult(False, [], "low", 0, 0.0)
    
    detector = YOLODetector(config_manager)
    return detector.detect_anomalies(video_frames, detection_targets, confidence_threshold, debug)
