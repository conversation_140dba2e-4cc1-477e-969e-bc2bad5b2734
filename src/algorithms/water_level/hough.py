"""
霍夫变换检测算法

基于水位项目的霍夫变换算法，利用霍夫直线检测水面线。
"""

import cv2
import numpy as np
import logging
from typing import Optional, List, Tuple

try:
    from ...utils.config_manager import ConfigManager
except ImportError:
    from utils.config_manager import ConfigManager


logger = logging.getLogger(__name__)


# 默认参数配置
DEFAULT_HOUGH_THRESH = 70
DEFAULT_MIN_LINE_LEN_FACTOR = 0.3
DEFAULT_MAX_GAP = 20


class HoughDetector:
    """
    霍夫变换检测器
    
    基于霍夫直线变换检测水面线位置
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化霍夫变换检测器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取配置参数
        self.hough_thresh = self.config_manager.get(
            "analyzers.water_level.methods.hough.thresh", DEFAULT_HOUGH_THRESH
        )
        self.min_line_len_factor = self.config_manager.get(
            "analyzers.water_level.methods.hough.min_line_len_factor", DEFAULT_MIN_LINE_LEN_FACTOR
        )
        self.max_gap = self.config_manager.get(
            "analyzers.water_level.methods.hough.max_gap", DEFAULT_MAX_GAP
        )
    
    def detect_waterline(self, preprocessed_channel: np.ndarray, debug: bool = False) -> Optional[int]:
        """
        使用霍夫变换检测水面线
        
        Args:
            preprocessed_channel: 预处理后的单通道图像(通常是V通道)
            debug: 是否启用调试模式
            
        Returns:
            Optional[int]: 水面线在ROI中的y坐标，检测失败返回None
        """
        try:
            h_roi, w_roi = preprocessed_channel.shape
            
            # 检查图像尺寸
            if h_roi < 20 or w_roi < 10:
                self.logger.warning(f"图像尺寸过小: {h_roi}x{w_roi}")
                return None
            
            # 只在下半部分检测水面线
            lower_half_start_y = h_roi // 2
            roi_down = preprocessed_channel[lower_half_start_y:, :]
            
            # 边缘检测
            edges = cv2.Canny(roi_down, 5, 25, apertureSize=3)
            
            if debug:
                self.logger.info(f"霍夫变换检测: 在下半部分({lower_half_start_y}:{h_roi})进行边缘检测")
            
            # 计算最小线长
            min_line_length = int(w_roi * self.min_line_len_factor)
            if min_line_length < 5:
                min_line_length = 5
            
            # 霍夫直线检测
            lines = cv2.HoughLinesP(
                edges, 
                rho=1, 
                theta=np.pi / 180,
                threshold=self.hough_thresh,
                minLineLength=min_line_length,
                maxLineGap=self.max_gap
            )
            
            if lines is None:
                if debug:
                    self.logger.info("霍夫变换未检测到任何直线")
                return None
            
            # 过滤水平线
            horizontal_line_ys = []
            for line_segment in lines:
                x1, y1, x2, y2 = line_segment[0]
                
                # 检查是否为水平线（y坐标差异小于5像素）
                if abs(y1 - y2) < 5:
                    horizontal_line_ys.append((y1 + y2) // 2)
            
            if not horizontal_line_ys:
                if debug:
                    self.logger.info(f"在{len(lines)}条检测到的直线中未找到水平线")
                return None
            
            # 使用中位数作为最终结果
            waterline_y_relative = int(np.median(horizontal_line_ys))
            waterline_y_absolute = waterline_y_relative + lower_half_start_y
            
            if debug:
                self.logger.info(
                    f"霍夫变换检测完成: 找到{len(horizontal_line_ys)}条水平线, "
                    f"水面线位置 y={waterline_y_absolute}"
                )
            
            return waterline_y_absolute
            
        except Exception as e:
            self.logger.error(f"霍夫变换检测失败: {str(e)}")
            return None
    
    def detect_all_lines(self, preprocessed_channel: np.ndarray, debug: bool = False) -> List[Tuple[int, int, int, int]]:
        """
        检测所有直线
        
        Args:
            preprocessed_channel: 预处理后的单通道图像
            debug: 调试模式
            
        Returns:
            List[Tuple[int, int, int, int]]: 检测到的直线列表，每个元素为(x1, y1, x2, y2)
        """
        try:
            h_roi, w_roi = preprocessed_channel.shape
            
            if h_roi < 20 or w_roi < 10:
                return []
            
            # 边缘检测
            edges = cv2.Canny(preprocessed_channel, 5, 25, apertureSize=3)
            
            # 计算最小线长
            min_line_length = int(w_roi * self.min_line_len_factor)
            if min_line_length < 5:
                min_line_length = 5
            
            # 霍夫直线检测
            lines = cv2.HoughLinesP(
                edges, 
                rho=1, 
                theta=np.pi / 180,
                threshold=self.hough_thresh,
                minLineLength=min_line_length,
                maxLineGap=self.max_gap
            )
            
            if lines is None:
                return []
            
            # 转换为列表格式
            detected_lines = []
            for line_segment in lines:
                x1, y1, x2, y2 = line_segment[0]
                detected_lines.append((x1, y1, x2, y2))
            
            if debug:
                self.logger.info(f"霍夫变换检测到{len(detected_lines)}条直线")
            
            return detected_lines
            
        except Exception as e:
            self.logger.error(f"霍夫变换直线检测失败: {str(e)}")
            return []
    
    def filter_horizontal_lines(self, lines: List[Tuple[int, int, int, int]], angle_threshold: float = 15.0) -> List[Tuple[int, int, int, int, float]]:
        """
        过滤出水平线
        
        Args:
            lines: 直线列表
            angle_threshold: 角度阈值(度)，小于此角度的线被认为是水平线
            
        Returns:
            List[Tuple[int, int, int, int, float]]: 水平线列表，每个元素为(x1, y1, x2, y2, angle)
        """
        horizontal_lines = []
        
        for x1, y1, x2, y2 in lines:
            # 计算直线角度
            if x2 - x1 != 0:
                angle = abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)
            else:
                angle = 90.0  # 垂直线
            
            # 检查是否为水平线
            if angle < angle_threshold or angle > (180 - angle_threshold):
                horizontal_lines.append((x1, y1, x2, y2, angle))
        
        return horizontal_lines
    
    def get_line_statistics(self, lines: List[Tuple[int, int, int, int]]) -> dict:
        """
        获取直线统计信息
        
        Args:
            lines: 直线列表
            
        Returns:
            dict: 统计信息
        """
        if not lines:
            return {
                "total_lines": 0,
                "horizontal_lines": 0,
                "vertical_lines": 0,
                "diagonal_lines": 0,
                "average_length": 0.0
            }
        
        horizontal_count = 0
        vertical_count = 0
        diagonal_count = 0
        total_length = 0.0
        
        for x1, y1, x2, y2 in lines:
            # 计算长度
            length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            total_length += length
            
            # 计算角度
            if x2 - x1 != 0:
                angle = abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)
            else:
                angle = 90.0
            
            # 分类
            if angle < 15 or angle > 165:
                horizontal_count += 1
            elif 75 < angle < 105:
                vertical_count += 1
            else:
                diagonal_count += 1
        
        return {
            "total_lines": len(lines),
            "horizontal_lines": horizontal_count,
            "vertical_lines": vertical_count,
            "diagonal_lines": diagonal_count,
            "average_length": total_length / len(lines) if lines else 0.0
        }


def detect_waterline_hough(
    preprocessed_channel: np.ndarray,
    config_manager: Optional[ConfigManager] = None,
    debug: bool = False
) -> Optional[int]:
    """
    霍夫变换检测水面线的便捷函数
    
    Args:
        preprocessed_channel: 预处理后的单通道图像
        config_manager: 配置管理器
        debug: 调试模式
        
    Returns:
        Optional[int]: 水面线y坐标
    """
    detector = HoughDetector(config_manager)
    return detector.detect_waterline(preprocessed_channel, debug)
