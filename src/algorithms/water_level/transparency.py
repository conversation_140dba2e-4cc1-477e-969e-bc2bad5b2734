"""
透明度检测算法

基于水位项目的透明度检测算法，利用水面较浅较透明的特性检测水面线。
支持多种透明度检测方案。
"""

import cv2
import numpy as np
import logging
from typing import Optional, Tuple, Dict, Any
import pandas as pd

try:
    from ...utils.config_manager import ConfigManager
except ImportError:
    from utils.config_manager import ConfigManager


logger = logging.getLogger(__name__)


# 默认参数配置
DEFAULT_ALPHA_THRESH = 50
DEFAULT_CANNY_LOW = 30
DEFAULT_CANNY_HIGH = 100
DEFAULT_BLUR_KSIZE = 5
DEFAULT_MORPH_KERNEL_SIZE = 5
DEFAULT_MIN_AREA_FACTOR = 0.01
DEFAULT_BRIGHTNESS_THRESH = 80
DEFAULT_BLACK_PIXEL_RATIO_THRESH = 0.3
DEFAULT_AVG_CHANGE_RATE_THRESH = 0.5


class TransparencyDetector:
    """
    透明度检测器
    
    基于水面透明度特性检测水面线位置
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化透明度检测器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取配置参数
        self.alpha_thresh = self.config_manager.get(
            "analyzers.water_level.methods.transparency.alpha_thresh", DEFAULT_ALPHA_THRESH
        )
        self.canny_low = self.config_manager.get(
            "analyzers.water_level.methods.transparency.canny_low", DEFAULT_CANNY_LOW
        )
        self.canny_high = self.config_manager.get(
            "analyzers.water_level.methods.transparency.canny_high", DEFAULT_CANNY_HIGH
        )
        self.blur_ksize = self.config_manager.get(
            "analyzers.water_level.methods.transparency.blur_ksize", DEFAULT_BLUR_KSIZE
        )
        self.morph_kernel_size = self.config_manager.get(
            "analyzers.water_level.methods.transparency.morph_kernel_size", DEFAULT_MORPH_KERNEL_SIZE
        )
        self.min_area_factor = self.config_manager.get(
            "analyzers.water_level.methods.transparency.min_area_factor", DEFAULT_MIN_AREA_FACTOR
        )
        self.brightness_thresh = self.config_manager.get(
            "analyzers.water_level.methods.transparency.brightness_thresh", DEFAULT_BRIGHTNESS_THRESH
        )
        self.scheme = self.config_manager.get(
            "analyzers.water_level.methods.transparency.scheme", "contrast_change"
        )
    
    def detect_waterline(self, roi_image: np.ndarray, debug: bool = False) -> Optional[int]:
        """
        检测水面线位置

        Args:
            roi_image: ROI区域图像
            debug: 是否启用调试模式

        Returns:
            Optional[int]: 水面线在ROI中的y坐标，检测失败返回None
        """
        self.logger.debug(f"开始透明度检测，图像尺寸: {roi_image.shape}, 检测方案: {self.scheme}")

        try:
            if self.scheme == "contrast_change":
                self.logger.debug("使用对比度变化检测方案")
                result = self._detect_by_contrast_change(roi_image, debug)
            else:
                self.logger.debug("使用透明度掩码检测方案")
                result = self._detect_by_transparency_mask(roi_image, debug)

            if result is not None:
                self.logger.debug(f"透明度检测成功，水面线位置: y={result}")
            else:
                self.logger.debug("透明度检测未找到水面线")

            return result

        except Exception as e:
            self.logger.error(f"透明度检测失败: {str(e)}")
            return None
    
    def _detect_by_contrast_change(self, roi_image: np.ndarray, debug: bool = False) -> Optional[int]:
        """
        基于对比度变化检测水面线
        
        Args:
            roi_image: ROI区域图像
            debug: 是否启用调试模式
            
        Returns:
            Optional[int]: 水面线y坐标
        """
        try:
            # 转为灰度图并应用高斯模糊
            gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (self.blur_ksize, self.blur_ksize), 0)
            
            roi_height, roi_width = blurred.shape
            
            # 定义水相关区域 - 图片底部区域
            water_region_height = min(200, roi_height // 2)
            water_region_start_y = roi_height - water_region_height
            
            # 计算对比度分数矩阵
            kernel = np.ones((9, 9), np.float32) / 81
            local_mean = cv2.filter2D(blurred.astype(np.float32), -1, kernel)
            local_variance = cv2.filter2D((blurred.astype(np.float32) - local_mean) ** 2, -1, kernel)
            local_std = np.sqrt(local_variance)
            
            # 对比度分数：使用局部标准差作为对比度指标
            contrast_score = local_std
            
            # 计算每行的对比度均值
            row_contrast_means = np.mean(contrast_score, axis=1)
            
            # 计算水区域的对比度均值作为参考
            water_contrast_mean = np.mean(row_contrast_means[water_region_start_y:])
            
            # 计算变化率
            change_rates = np.zeros(roi_height)
            for i in range(1, roi_height):
                if row_contrast_means[i-1] != 0:
                    change_rates[i] = (row_contrast_means[i] - row_contrast_means[i-1]) / row_contrast_means[i-1]
            
            # 从底部往上找第一个变化率大于阈值的位置
            waterline_y = None
            change_rate_threshold = 0.1
            
            for y in range(roi_height - 1, -1, -1):
                if change_rates[y] > change_rate_threshold:
                    waterline_y = y
                    break
            
            if debug and waterline_y is not None:
                self.logger.info(f"透明度检测 - 对比度变化法: 水面线位置 y={waterline_y}")
            
            return waterline_y
            
        except Exception as e:
            self.logger.error(f"对比度变化检测失败: {str(e)}")
            return None
    
    def _detect_by_transparency_mask(self, roi_image: np.ndarray, debug: bool = False) -> Optional[int]:
        """
        基于透明度掩码检测水面线
        
        Args:
            roi_image: ROI区域图像
            debug: 是否启用调试模式
            
        Returns:
            Optional[int]: 水面线y坐标
        """
        try:
            # 转为灰度图并应用高斯模糊
            gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (self.blur_ksize, self.blur_ksize), 0)
            
            roi_height, roi_width = blurred.shape
            roi_area = roi_height * roi_width
            
            # 创建亮度掩码
            bright_mask = cv2.threshold(blurred, self.brightness_thresh, 255, cv2.THRESH_BINARY)[1]
            
            # 应用自适应阈值
            adapt_thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # 组合掩码
            transparency_mask = cv2.bitwise_or(bright_mask, adapt_thresh)
            
            # 形态学操作
            kernel = np.ones((self.morph_kernel_size, self.morph_kernel_size), np.uint8)
            transparency_mask = cv2.morphologyEx(transparency_mask, cv2.MORPH_CLOSE, kernel)
            transparency_mask = cv2.morphologyEx(transparency_mask, cv2.MORPH_OPEN, kernel)
            
            # 找到轮廓
            contours, _ = cv2.findContours(transparency_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # 过滤轮廓
            min_area = roi_area * self.min_area_factor
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]
            
            if not valid_contours:
                return None
            
            # 找到最大轮廓
            max_contour = max(valid_contours, key=cv2.contourArea)
            
            # 获取轮廓边界框
            x, y, w, h = cv2.boundingRect(max_contour)
            
            # 寻找水面转换点
            waterline_y = self._find_water_surface_transition(
                roi_image, transparency_mask, max_contour, x, y, w, h, debug
            )
            
            if waterline_y is None:
                # 回退方案：使用轮廓底部
                waterline_y = y + h
            
            if debug:
                self.logger.info(f"透明度检测 - 掩码法: 水面线位置 y={waterline_y}")
            
            return waterline_y
            
        except Exception as e:
            self.logger.error(f"透明度掩码检测失败: {str(e)}")
            return None
    
    def _find_water_surface_transition(
        self, 
        roi_image: np.ndarray, 
        transparency_mask: np.ndarray, 
        max_contour: np.ndarray,
        x: int, y: int, w: int, h: int,
        debug: bool = False
    ) -> Optional[int]:
        """
        寻找水面转换点
        
        Args:
            roi_image: ROI图像
            transparency_mask: 透明度掩码
            max_contour: 最大轮廓
            x, y, w, h: 轮廓边界框
            debug: 调试模式
            
        Returns:
            Optional[int]: 水面转换点y坐标
        """
        try:
            # 创建轮廓掩码
            contour_mask = np.zeros(roi_image.shape[:2], dtype=np.uint8)
            cv2.drawContours(contour_mask, [max_contour], -1, 255, -1)
            
            roi_height, roi_width = roi_image.shape[:2]
            
            # 计算每行的黑色像素数量
            black_pixels_data = np.sum(contour_mask == 0, axis=1)
            
            if len(black_pixels_data) < 10:
                return None
            
            # 计算变化率
            avg_change_rate = np.zeros(len(black_pixels_data))
            window_size = 5
            
            for i in range(window_size, len(black_pixels_data) - window_size):
                before_avg = np.mean(black_pixels_data[i-window_size:i])
                after_avg = np.mean(black_pixels_data[i+1:i+1+window_size])
                
                if before_avg != 0:
                    change_rate = (after_avg - before_avg) / before_avg
                    avg_change_rate[i] = change_rate
            
            # 过滤条件
            black_pixel_ratio = black_pixels_data / roi_width
            
            valid_mask = (
                (black_pixel_ratio <= DEFAULT_BLACK_PIXEL_RATIO_THRESH) & 
                (avg_change_rate >= DEFAULT_AVG_CHANGE_RATE_THRESH) &
                (~np.isnan(avg_change_rate))
            )
            
            valid_indices = np.where(valid_mask)[0]
            
            if len(valid_indices) > 0:
                waterline_y = int(np.mean(valid_indices))
                
                if debug:
                    self.logger.info(f"找到水面转换点: y={waterline_y}, 符合条件的行数: {len(valid_indices)}")
                
                return waterline_y
            
            return None
            
        except Exception as e:
            self.logger.error(f"水面转换点检测失败: {str(e)}")
            return None


def detect_waterline_transparency(
    roi_image: np.ndarray,
    config_manager: Optional[ConfigManager] = None,
    debug: bool = False
) -> Optional[int]:
    """
    透明度检测水面线的便捷函数
    
    Args:
        roi_image: ROI区域图像
        config_manager: 配置管理器
        debug: 调试模式
        
    Returns:
        Optional[int]: 水面线y坐标
    """
    detector = TransparencyDetector(config_manager)
    return detector.detect_waterline(roi_image, debug)
