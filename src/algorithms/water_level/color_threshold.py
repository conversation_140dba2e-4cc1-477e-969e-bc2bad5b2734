"""
颜色阈值检测算法

基于水位项目的颜色阈值算法，利用HSV颜色空间分析检测水面线。
"""

import cv2
import numpy as np
import logging
from typing import Optional, Tuple, List

try:
    from ...utils.config_manager import ConfigManager
except ImportError:
    from utils.config_manager import ConfigManager


logger = logging.getLogger(__name__)


# 默认参数配置
DEFAULT_HSV_LOWER = np.array([35, 40, 40])   # 水体的HSV下限（绿色调范围）
DEFAULT_HSV_UPPER = np.array([85, 255, 255]) # 水体的HSV上限
DEFAULT_MORPH_KERNEL_SIZE = 5                # 形态学操作的核大小
DEFAULT_MIN_AREA_FACTOR = 0.01               # 最小区域因子（相对于ROI面积）


class ColorThresholdDetector:
    """
    颜色阈值检测器
    
    基于HSV颜色空间分析检测水面线位置
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化颜色阈值检测器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取配置参数
        self.hsv_lower = np.array(self.config_manager.get(
            "analyzers.water_level.methods.color_threshold.hsv_lower", DEFAULT_HSV_LOWER.tolist()
        ))
        self.hsv_upper = np.array(self.config_manager.get(
            "analyzers.water_level.methods.color_threshold.hsv_upper", DEFAULT_HSV_UPPER.tolist()
        ))
        self.morph_kernel_size = self.config_manager.get(
            "analyzers.water_level.methods.color_threshold.morph_kernel_size", DEFAULT_MORPH_KERNEL_SIZE
        )
        self.min_area_factor = self.config_manager.get(
            "analyzers.water_level.methods.color_threshold.min_area_factor", DEFAULT_MIN_AREA_FACTOR
        )
    
    def detect_waterline(self, roi_image: np.ndarray, debug: bool = False) -> Optional[int]:
        """
        使用颜色阈值检测水面线
        
        Args:
            roi_image: ROI区域图像(BGR格式)
            debug: 是否启用调试模式
            
        Returns:
            Optional[int]: 水面线在ROI中的y坐标，检测失败返回None
        """
        try:
            # 转换到HSV颜色空间
            hsv_roi = cv2.cvtColor(roi_image, cv2.COLOR_BGR2HSV)
            
            # 根据HSV阈值创建掩码
            water_mask = cv2.inRange(hsv_roi, self.hsv_lower, self.hsv_upper)
            
            if debug:
                self.logger.info(f"HSV阈值范围: 下限{self.hsv_lower}, 上限{self.hsv_upper}")
            
            # 形态学操作，先开运算（去噪），再闭运算（填充小孔）
            kernel = np.ones((self.morph_kernel_size, self.morph_kernel_size), np.uint8)
            water_mask = cv2.morphologyEx(water_mask, cv2.MORPH_OPEN, kernel)
            water_mask = cv2.morphologyEx(water_mask, cv2.MORPH_CLOSE, kernel)
            
            # 找到所有轮廓
            contours, _ = cv2.findContours(water_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                if debug:
                    self.logger.info("颜色阈值检测: 未找到任何轮廓")
                return None
            
            # 过滤掉太小的轮廓
            min_area = roi_image.shape[0] * roi_image.shape[1] * self.min_area_factor
            valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
            
            if not valid_contours:
                if debug:
                    self.logger.info(f"颜色阈值检测: 所有轮廓面积都小于最小阈值 {min_area}")
                return None
            
            # 找到最大面积的轮廓
            max_contour = max(valid_contours, key=cv2.contourArea)
            
            # 获取轮廓的边界框，使用其顶部作为水面线
            x, y, w, h = cv2.boundingRect(max_contour)
            water_line_y = y
            
            if debug:
                self.logger.info(
                    f"颜色阈值检测完成: 找到{len(valid_contours)}个有效轮廓, "
                    f"最大轮廓面积={cv2.contourArea(max_contour):.0f}, "
                    f"水面线位置 y={water_line_y}"
                )
            
            return water_line_y
            
        except Exception as e:
            self.logger.error(f"颜色阈值检测失败: {str(e)}")
            return None
    
    def get_water_mask(self, roi_image: np.ndarray) -> np.ndarray:
        """
        获取水体掩码
        
        Args:
            roi_image: ROI区域图像
            
        Returns:
            np.ndarray: 水体掩码
        """
        try:
            # 转换到HSV颜色空间
            hsv_roi = cv2.cvtColor(roi_image, cv2.COLOR_BGR2HSV)
            
            # 根据HSV阈值创建掩码
            water_mask = cv2.inRange(hsv_roi, self.hsv_lower, self.hsv_upper)
            
            # 形态学操作
            kernel = np.ones((self.morph_kernel_size, self.morph_kernel_size), np.uint8)
            water_mask = cv2.morphologyEx(water_mask, cv2.MORPH_OPEN, kernel)
            water_mask = cv2.morphologyEx(water_mask, cv2.MORPH_CLOSE, kernel)
            
            return water_mask
            
        except Exception as e:
            self.logger.error(f"获取水体掩码失败: {str(e)}")
            return np.zeros(roi_image.shape[:2], dtype=np.uint8)
    
    def get_water_contours(self, roi_image: np.ndarray) -> List[np.ndarray]:
        """
        获取水体轮廓
        
        Args:
            roi_image: ROI区域图像
            
        Returns:
            List[np.ndarray]: 有效的水体轮廓列表
        """
        try:
            water_mask = self.get_water_mask(roi_image)
            
            # 找到所有轮廓
            contours, _ = cv2.findContours(water_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return []
            
            # 过滤掉太小的轮廓
            min_area = roi_image.shape[0] * roi_image.shape[1] * self.min_area_factor
            valid_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
            
            return valid_contours
            
        except Exception as e:
            self.logger.error(f"获取水体轮廓失败: {str(e)}")
            return []
    
    def visualize_detection_steps(self, roi_image: np.ndarray) -> np.ndarray:
        """
        可视化检测过程的每个步骤
        
        Args:
            roi_image: ROI图像
            
        Returns:
            np.ndarray: 包含可视化结果的图像
        """
        try:
            h, w = roi_image.shape[:2]
            result_img = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
            
            # 1. 原始ROI图像
            result_img[0:h, 0:w] = roi_image.copy()
            cv2.putText(result_img, "1. Original ROI", (10, 20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 2. HSV转换
            hsv_roi = cv2.cvtColor(roi_image, cv2.COLOR_BGR2HSV)
            hsv_display = cv2.cvtColor(hsv_roi, cv2.COLOR_HSV2BGR)  # 转回BGR用于显示
            result_img[0:h, w:w*2] = hsv_display
            cv2.putText(result_img, "2. HSV Conversion", (w+10, 20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 3. 阈值掩码
            water_mask = cv2.inRange(hsv_roi, self.hsv_lower, self.hsv_upper)
            water_mask_bgr = cv2.cvtColor(water_mask, cv2.COLOR_GRAY2BGR)
            result_img[h:h*2, 0:w] = water_mask_bgr
            cv2.putText(result_img, "3. HSV Threshold Mask", (10, h+20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 4. 形态学处理后的掩码
            kernel = np.ones((self.morph_kernel_size, self.morph_kernel_size), np.uint8)
            morph_mask = cv2.morphologyEx(water_mask, cv2.MORPH_OPEN, kernel)
            morph_mask = cv2.morphologyEx(morph_mask, cv2.MORPH_CLOSE, kernel)
            morph_mask_bgr = cv2.cvtColor(morph_mask, cv2.COLOR_GRAY2BGR)
            result_img[h:h*2, w:w*2] = morph_mask_bgr
            cv2.putText(result_img, "4. After Morphology", (w+10, h+20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return result_img
            
        except Exception as e:
            self.logger.error(f"可视化检测步骤失败: {str(e)}")
            return roi_image.copy()
    
    def create_detection_result_image(self, roi_image: np.ndarray, waterline_y: Optional[int] = None) -> np.ndarray:
        """
        创建检测结果图像
        
        Args:
            roi_image: ROI图像
            waterline_y: 水面线y坐标
            
        Returns:
            np.ndarray: 标注了检测结果的图像
        """
        try:
            result_img = roi_image.copy()
            
            # 获取水体轮廓
            valid_contours = self.get_water_contours(roi_image)
            
            if valid_contours:
                # 绘制所有有效轮廓
                cv2.drawContours(result_img, valid_contours, -1, (0, 255, 0), 2)
                
                # 找到最大轮廓并绘制边界框
                max_contour = max(valid_contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(max_contour)
                cv2.rectangle(result_img, (x, y), (x+w, y+h), (255, 0, 0), 2)
                
                # 绘制水面线
                if waterline_y is not None:
                    cv2.line(result_img, (0, waterline_y), (result_img.shape[1], waterline_y), (0, 0, 255), 2)
                    cv2.putText(result_img, f"Water line at y={waterline_y}", (10, waterline_y-10), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            return result_img
            
        except Exception as e:
            self.logger.error(f"创建检测结果图像失败: {str(e)}")
            return roi_image.copy()


def detect_waterline_color_threshold(
    roi_image: np.ndarray,
    config_manager: Optional[ConfigManager] = None,
    debug: bool = False
) -> Optional[int]:
    """
    颜色阈值检测水面线的便捷函数
    
    Args:
        roi_image: ROI区域图像
        config_manager: 配置管理器
        debug: 调试模式
        
    Returns:
        Optional[int]: 水面线y坐标
    """
    detector = ColorThresholdDetector(config_manager)
    return detector.detect_waterline(roi_image, debug)
