"""
梯度检测算法

基于水位项目的梯度检测算法，利用像素强度梯度变化检测水面线。
"""

import cv2
import numpy as np
import logging
from typing import Optional, List, Tuple

try:
    from ...utils.config_manager import ConfigManager
except ImportError:
    from utils.config_manager import ConfigManager


logger = logging.getLogger(__name__)


# 默认参数配置
DEFAULT_GRAD_THRESH = 25


class GradientDetector:
    """
    梯度检测器
    
    基于像素强度梯度变化检测水面线位置
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化梯度检测器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logger
        
        # 获取配置参数
        self.grad_thresh = self.config_manager.get(
            "analyzers.water_level.methods.gradient.grad_thresh", DEFAULT_GRAD_THRESH
        )
        self.blur_kernel_size = self.config_manager.get(
            "analyzers.water_level.methods.gradient.blur_kernel_size", 5
        )
        self.morph_kernel_size = self.config_manager.get(
            "analyzers.water_level.methods.gradient.morph_kernel_size", 3
        )
    
    def detect_waterline(self, preprocessed_channel: np.ndarray, debug: bool = False) -> Optional[int]:
        """
        使用梯度法检测水面线
        
        Args:
            preprocessed_channel: 预处理后的单通道图像(通常是V通道)
            debug: 是否启用调试模式
            
        Returns:
            Optional[int]: 水面线在ROI中的y坐标，检测失败返回None
        """
        try:
            roi_h, roi_w = preprocessed_channel.shape
            
            if roi_w == 0:
                self.logger.warning("预处理通道宽度为0，无法进行梯度分析")
                return None
            
            # 选择要分析的列数，最多5列
            num_cols_to_analyze = min(5, roi_w)
            if num_cols_to_analyze == 0:
                self.logger.warning("ROI宽度不足以选择任何列进行分析")
                return None
            
            # 确定要分析的列索引
            if num_cols_to_analyze == 1:
                col_indices = [roi_w // 2]
            else:
                col_indices = np.linspace(0, roi_w - 1, num_cols_to_analyze, dtype=int)
            
            # 找到最接近中心的列
            center_roi_x_idx = roi_w // 2
            closest_col_idx_to_center = col_indices[np.argmin(np.abs(col_indices - center_roi_x_idx))]
            
            y_grad_primary_center = None
            all_detections = []
            
            if debug:
                self.logger.info(f"将在以下X列索引处分析梯度: {col_indices}")
            
            # 分析每一列
            for col_x_idx in col_indices:
                detection_result = self._analyze_column_gradient(
                    preprocessed_channel, col_x_idx, debug
                )
                
                if detection_result is not None:
                    all_detections.append(detection_result)
                    
                    # 如果是中心列，记录为主要检测结果
                    if col_x_idx == closest_col_idx_to_center:
                        y_grad_primary_center = detection_result
            
            # 如果中心列没有检测到，使用其他列的结果
            if y_grad_primary_center is None and all_detections:
                y_grad_primary_center = int(np.median(all_detections))
                if debug:
                    self.logger.info(f"中心列未检测到水面线，使用其他列的中位数: y={y_grad_primary_center}")
            
            if debug and y_grad_primary_center is not None:
                self.logger.info(f"梯度检测完成: 水面线位置 y={y_grad_primary_center}")
            
            return y_grad_primary_center
            
        except Exception as e:
            self.logger.error(f"梯度检测失败: {str(e)}")
            return None
    
    def _analyze_column_gradient(
        self, 
        preprocessed_channel: np.ndarray, 
        col_x_idx: int, 
        debug: bool = False
    ) -> Optional[int]:
        """
        分析单列的梯度
        
        Args:
            preprocessed_channel: 预处理后的单通道图像
            col_x_idx: 列索引
            debug: 调试模式
            
        Returns:
            Optional[int]: 检测到的水面线y坐标
        """
        try:
            # 提取列像素
            col_pixels = preprocessed_channel[:, col_x_idx].astype(np.int16)
            
            if len(col_pixels) < 2:
                if debug:
                    self.logger.warning(f"列 X={col_x_idx} 的像素数据不足，跳过梯度计算")
                return None
            
            # 计算梯度
            grad = np.abs(np.diff(col_pixels))
            
            if grad.size == 0:
                if debug:
                    self.logger.warning(f"列 X={col_x_idx}: 梯度数组为空")
                return None
            
            # 检查是否有满足阈值的梯度
            if grad.max() >= self.grad_thresh:
                y_detected = int(np.argmax(grad))
                
                if debug:
                    self.logger.info(
                        f"列 X={col_x_idx}: 检测到满足阈值的水线 y={y_detected} "
                        f"(梯度={grad.max():.2f})"
                    )
                
                return y_detected
            else:
                if debug:
                    self.logger.info(
                        f"列 X={col_x_idx}: 所有梯度值均小于阈值 {self.grad_thresh} "
                        f"(最大梯度={grad.max():.2f})"
                    )
                return None
                
        except Exception as e:
            self.logger.error(f"列 {col_x_idx} 梯度分析失败: {str(e)}")
            return None
    
    def get_gradient_profile(self, preprocessed_channel: np.ndarray, col_x_idx: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取指定列的梯度剖面
        
        Args:
            preprocessed_channel: 预处理后的单通道图像
            col_x_idx: 列索引
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: (像素值数组, 梯度值数组)
        """
        try:
            col_pixels = preprocessed_channel[:, col_x_idx].astype(np.int16)
            
            if len(col_pixels) < 2:
                return np.array([]), np.array([])
            
            grad = np.abs(np.diff(col_pixels))
            
            return col_pixels, grad
            
        except Exception as e:
            self.logger.error(f"获取梯度剖面失败: {str(e)}")
            return np.array([]), np.array([])
    
    def find_top_gradient_points(
        self, 
        preprocessed_channel: np.ndarray, 
        col_x_idx: int, 
        top_n: int = 20
    ) -> List[int]:
        """
        找到指定列中梯度最大的前N个点
        
        Args:
            preprocessed_channel: 预处理后的单通道图像
            col_x_idx: 列索引
            top_n: 返回前N个点
            
        Returns:
            List[int]: 梯度最大的前N个点的y坐标列表
        """
        try:
            col_pixels = preprocessed_channel[:, col_x_idx].astype(np.int16)
            
            if len(col_pixels) < 2:
                return []
            
            grad = np.abs(np.diff(col_pixels))
            
            if grad.size == 0:
                return []
            
            # 获取梯度值排序的索引
            sorted_indices = np.argsort(grad)[::-1]
            
            # 返回前N个点
            top_indices = sorted_indices[:min(top_n, len(sorted_indices))]
            
            return top_indices.tolist()
            
        except Exception as e:
            self.logger.error(f"查找顶部梯度点失败: {str(e)}")
            return []


def detect_waterline_gradient(
    preprocessed_channel: np.ndarray,
    config_manager: Optional[ConfigManager] = None,
    debug: bool = False
) -> Optional[int]:
    """
    梯度检测水面线的便捷函数
    
    Args:
        preprocessed_channel: 预处理后的单通道图像
        config_manager: 配置管理器
        debug: 调试模式
        
    Returns:
        Optional[int]: 水面线y坐标
    """
    detector = GradientDetector(config_manager)
    return detector.detect_waterline(preprocessed_channel, debug)
