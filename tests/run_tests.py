#!/usr/bin/env python3
"""
测试运行脚本

运行VR视频分析系统的所有测试，并生成测试报告。
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def run_tests():
    """运行所有测试"""
    print("VR视频分析系统测试运行器")
    print("=" * 50)
    
    # 设置环境变量
    os.environ["CONFIG_FILE"] = "config/app_config.yaml"
    
    # 测试目录
    test_dirs = [
        "tests/test_utils",
        "tests/test_algorithms", 
        "tests/test_processors",
        "tests/test_services",
        "tests/test_api"
    ]
    
    # 运行测试的命令
    cmd = [
        sys.executable, "-m", "pytest",
        "-v",  # 详细输出
        "--tb=short",  # 简短的错误回溯
        "--color=yes",  # 彩色输出
        "--durations=10",  # 显示最慢的10个测试
    ]
    
    # 添加存在的测试目录
    existing_dirs = []
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            existing_dirs.append(test_dir)
            print(f"✓ 找到测试目录: {test_dir}")
        else:
            print(f"⚠ 测试目录不存在: {test_dir}")
    
    if not existing_dirs:
        print("❌ 没有找到任何测试目录")
        return False
    
    # 添加测试目录到命令
    cmd.extend(existing_dirs)
    
    print(f"\n运行命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # 运行测试
        result = subprocess.run(cmd, cwd=Path(__file__).parent.parent)
        
        if result.returncode == 0:
            print("\n🎉 所有测试通过！")
            return True
        else:
            print(f"\n❌ 测试失败，退出码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n❌ 运行测试时出错: {e}")
        return False

def run_specific_test(test_path):
    """运行特定测试"""
    print(f"运行特定测试: {test_path}")
    print("=" * 50)
    
    # 设置环境变量
    os.environ["CONFIG_FILE"] = "config/app_config.yaml"
    
    cmd = [
        sys.executable, "-m", "pytest",
        test_path,
        "-v",
        "--tb=long",
        "--color=yes"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=Path(__file__).parent.parent)
        return result.returncode == 0
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 运行特定测试
        test_path = sys.argv[1]
        success = run_specific_test(test_path)
    else:
        # 运行所有测试
        success = run_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
