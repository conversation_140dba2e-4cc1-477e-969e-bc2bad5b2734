"""
水位识别处理器测试

测试WaterLevelProcessor类的各种功能，包括算法整合、分析流程等。
"""

import pytest
import asyncio
import numpy as np
from pathlib import Path
import sys
from unittest.mock import Mock, AsyncMock, patch

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.config_manager import ConfigManager
from processors.water_level_processor import WaterLevelProcessor


class TestWaterLevelProcessor:
    """水位识别处理器测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """创建测试用配置管理器"""
        config_manager = ConfigManager()
        
        # 设置测试配置
        config_manager.update("analyzers.water_level.enabled", True)
        config_manager.update("analyzers.water_level.default_method", "transparency")
        config_manager.update("analyzers.water_level.max_analysis_duration", 300)
        
        return config_manager
    
    @pytest.fixture
    def processor(self, config_manager):
        """创建水位处理器实例"""
        return WaterLevelProcessor(config_manager)
    
    @pytest.fixture
    def mock_video_frames(self):
        """创建模拟视频帧"""
        frames = []
        for i in range(10):
            # 创建模拟水面图像
            frame = np.zeros((200, 300, 3), dtype=np.uint8)
            frame[:100, :] = [200, 150, 100]  # 上半部分
            frame[100:, :] = [100, 50, 30]    # 下半部分（水面）
            frames.append(frame)
        return frames
    
    def test_processor_initialization(self, processor):
        """测试处理器初始化"""
        assert processor is not None
        assert processor.config_manager is not None
        assert processor.video_processor is not None
        assert processor.image_processor is not None
        assert len(processor.available_methods) > 0
        assert processor.default_method in processor.available_methods
    
    def test_get_supported_methods(self, processor):
        """测试获取支持的方法列表"""
        methods = processor.get_supported_methods()
        
        assert isinstance(methods, list)
        assert len(methods) > 0
        assert "transparency" in methods
        assert "gradient" in methods
        assert "hough" in methods
        assert "color_threshold" in methods
    
    def test_get_method_info(self, processor):
        """测试获取方法信息"""
        # 测试有效方法
        info = processor.get_method_info("transparency")
        assert isinstance(info, dict)
        assert "name" in info
        assert "description" in info
        assert "suitable_for" in info
        
        # 测试无效方法
        invalid_info = processor.get_method_info("invalid_method")
        assert isinstance(invalid_info, dict)
        assert "name" in invalid_info
    
    @pytest.mark.asyncio
    async def test_analyze_frames_transparency(self, processor, mock_video_frames):
        """测试透明度方法的帧分析"""
        result = await processor._analyze_frames(
            frames=mock_video_frames,
            method="transparency",
            roi=None,
            task_id="test_task"
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        assert "water_depth_cm" in result
        assert "confidence" in result
        assert "method_used" in result
        assert result["method_used"] == "transparency"
        assert result["frames_analyzed"] == len(mock_video_frames)
    
    @pytest.mark.asyncio
    async def test_analyze_frames_with_roi(self, processor, mock_video_frames):
        """测试带ROI的帧分析"""
        roi = {"x": 50, "y": 50, "width": 200, "height": 100}
        
        result = await processor._analyze_frames(
            frames=mock_video_frames,
            method="transparency",
            roi=roi,
            task_id="test_task_roi"
        )
        
        assert isinstance(result, dict)
        assert result["success"] in [True, False]  # 可能成功也可能失败
    
    def test_apply_roi(self, processor, mock_video_frames):
        """测试ROI应用"""
        frame = mock_video_frames[0]
        roi = {"x": 50, "y": 50, "width": 100, "height": 80}
        
        roi_frame = processor._apply_roi(frame, roi)
        
        assert roi_frame.shape[0] == 80  # height
        assert roi_frame.shape[1] == 100  # width
        assert roi_frame.shape[2] == 3   # channels
    
    def test_apply_roi_boundary_conditions(self, processor, mock_video_frames):
        """测试ROI边界条件"""
        frame = mock_video_frames[0]
        
        # 测试超出边界的ROI
        roi_out_of_bounds = {"x": 250, "y": 150, "width": 100, "height": 100}
        roi_frame = processor._apply_roi(frame, roi_out_of_bounds)
        
        # 应该返回有效的帧（可能被裁剪）
        assert roi_frame.shape[0] > 0
        assert roi_frame.shape[1] > 0
        
        # 测试负坐标ROI
        roi_negative = {"x": -10, "y": -10, "width": 50, "height": 50}
        roi_frame = processor._apply_roi(frame, roi_negative)
        
        assert roi_frame.shape[0] > 0
        assert roi_frame.shape[1] > 0
    
    @pytest.mark.asyncio
    async def test_analyze_with_mock_video_processor(self, processor):
        """测试使用模拟视频处理器的分析"""
        # 模拟视频处理器
        with patch.object(processor.video_processor, 'connect', return_value=True), \
             patch.object(processor.video_processor, 'get_video_info', return_value={
                 'width': 640, 'height': 480, 'fps': 30.0
             }), \
             patch.object(processor.video_processor, 'extract_frames') as mock_extract, \
             patch.object(processor.video_processor, 'disconnect'):
            
            # 设置模拟帧提取
            mock_frames = [np.zeros((200, 300, 3), dtype=np.uint8) for _ in range(5)]
            
            async def mock_frame_generator(duration, roi=None):
                for frame in mock_frames:
                    yield frame
            
            mock_extract.return_value = mock_frame_generator(30)
            
            # 执行分析
            result = await processor.analyze(
                video_url="test://mock_video",
                method="transparency",
                duration=30,
                roi=None,
                task_id="test_mock"
            )
            
            assert isinstance(result, dict)
            assert "processing_time" in result
    
    @pytest.mark.asyncio
    async def test_analyze_invalid_method(self, processor):
        """测试无效方法分析"""
        with pytest.raises(ValueError, match="不支持的检测方法"):
            await processor.analyze(
                video_url="test://mock_video",
                method="invalid_method",
                duration=30,
                task_id="test_invalid"
            )
    
    @pytest.mark.asyncio
    async def test_analyze_connection_failure(self, processor):
        """测试视频连接失败"""
        with patch.object(processor.video_processor, 'connect', return_value=False):
            with pytest.raises(RuntimeError, match="无法连接到视频源"):
                await processor.analyze(
                    video_url="invalid://video_url",
                    method="transparency",
                    duration=30,
                    task_id="test_connection_fail"
                )
    
    @pytest.mark.asyncio
    async def test_analyze_no_frames(self, processor):
        """测试无帧情况"""
        with patch.object(processor.video_processor, 'connect', return_value=True), \
             patch.object(processor.video_processor, 'get_video_info', return_value={
                 'width': 640, 'height': 480, 'fps': 30.0
             }), \
             patch.object(processor.video_processor, 'extract_frames') as mock_extract, \
             patch.object(processor.video_processor, 'disconnect'):
            
            # 设置空帧生成器
            async def empty_frame_generator(duration, roi=None):
                return
                yield  # 这行永远不会执行
            
            mock_extract.return_value = empty_frame_generator(30)
            
            with pytest.raises(RuntimeError, match="未能提取到有效的视频帧"):
                await processor.analyze(
                    video_url="test://empty_video",
                    method="transparency",
                    duration=30,
                    task_id="test_no_frames"
                )
    
    def test_analyze_frames_all_methods(self, processor, mock_video_frames):
        """测试所有方法的帧分析"""
        methods = processor.get_supported_methods()
        
        for method in methods:
            # 使用asyncio.run来运行异步测试
            async def test_method():
                result = await processor._analyze_frames(
                    frames=mock_video_frames,
                    method=method,
                    roi=None,
                    task_id=f"test_{method}"
                )
                return result
            
            result = asyncio.run(test_method())
            
            assert isinstance(result, dict)
            assert "method_used" in result
            assert result["method_used"] == method
    
    @pytest.mark.asyncio
    async def test_cleanup(self, processor):
        """测试资源清理"""
        # 模拟视频处理器
        processor.video_processor = Mock()
        processor.video_processor.disconnect = Mock()
        
        # 执行清理
        await processor.cleanup()
        
        # 验证清理方法被调用
        processor.video_processor.disconnect.assert_called_once()
    
    def test_processor_configuration_validation(self, config_manager):
        """测试处理器配置验证"""
        # 测试默认配置
        processor = WaterLevelProcessor(config_manager)
        assert processor.default_method is not None
        assert processor.max_analysis_duration > 0
        
        # 测试自定义配置
        config_manager.update("analyzers.water_level.default_method", "gradient")
        config_manager.update("analyzers.water_level.max_analysis_duration", 600)
        
        processor2 = WaterLevelProcessor(config_manager)
        assert processor2.default_method == "gradient"
        assert processor2.max_analysis_duration == 600


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
