"""
基础功能测试

测试VR视频分析系统的基础功能是否正常工作。
"""

import pytest
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# 设置环境变量以避免相对导入问题
os.environ["PYTHONPATH"] = str(Path(__file__).parent.parent / "src")

try:
    from utils.config_manager import ConfigManager
    from utils.logger import setup_logger
    from services.analysis_service import AnalysisService
except ImportError as e:
    print(f"导入错误: {e}")
    # 尝试绝对导入
    import utils.config_manager as config_manager_module
    import utils.logger as logger_module
    import services.analysis_service as analysis_service_module

    ConfigManager = config_manager_module.ConfigManager
    setup_logger = logger_module.setup_logger
    AnalysisService = analysis_service_module.AnalysisService


class TestBasicFunctionality:
    """基础功能测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        # 设置测试环境
        os.environ["CONFIG_FILE"] = "config/app_config.yaml"
        
        # 初始化日志
        setup_logger(debug=True)
        
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 初始化分析服务
        self.analysis_service = AnalysisService(self.config_manager)
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        assert self.config_manager is not None
        
        # 测试获取配置
        server_config = self.config_manager.get("server")
        assert server_config is not None
        assert "host" in server_config
        assert "port" in server_config
        
        # 测试嵌套配置获取
        water_level_enabled = self.config_manager.get("analyzers.water_level.enabled")
        assert water_level_enabled is not None
    
    def test_analysis_service_initialization(self):
        """测试分析服务初始化"""
        assert self.analysis_service is not None
        assert self.analysis_service.config_manager is not None
        
        # 测试并发控制配置
        assert hasattr(self.analysis_service, 'stream_semaphore')
        assert hasattr(self.analysis_service, 'task_semaphore')
    
    def test_water_level_processor_creation(self):
        """测试水位处理器创建"""
        try:
            from processors.water_level_processor import WaterLevelProcessor
            processor = WaterLevelProcessor(self.config_manager)
            assert processor is not None
            assert len(processor.get_supported_methods()) > 0
            
            # 测试方法信息获取
            method_info = processor.get_method_info("transparency")
            assert "name" in method_info
            assert "description" in method_info
            
        except ImportError as e:
            pytest.skip(f"水位处理器导入失败: {e}")
    
    def test_flow_speed_processor_creation(self):
        """测试流速处理器创建"""
        try:
            from processors.flow_speed_processor import FlowSpeedProcessor
            processor = FlowSpeedProcessor(self.config_manager)
            assert processor is not None
            assert len(processor.get_supported_methods()) > 0
            
            # 测试方法信息获取
            method_info = processor.get_method_info("otv")
            assert "name" in method_info
            assert "description" in method_info
            
        except ImportError as e:
            pytest.skip(f"流速处理器导入失败: {e}")
    
    def test_anomaly_processor_creation(self):
        """测试异常检测处理器创建"""
        try:
            from processors.anomaly_processor import AnomalyProcessor
            processor = AnomalyProcessor(self.config_manager)
            assert processor is not None
            assert len(processor.get_supported_targets()) > 0
            
            # 测试目标信息获取
            target_info = processor.get_target_info("person")
            assert "category" in target_info
            assert "risk_level" in target_info
            
        except ImportError as e:
            pytest.skip(f"异常检测处理器导入失败: {e}")
    
    def test_video_utils_import(self):
        """测试视频工具导入"""
        try:
            from utils.video_utils import VideoStreamProcessor, sort_polygon_points_clockwise
            assert VideoStreamProcessor is not None
            assert sort_polygon_points_clockwise is not None
            
            # 测试多边形点排序
            points = [(0, 0), (1, 0), (1, 1), (0, 1)]
            sorted_points = sort_polygon_points_clockwise(points)
            assert len(sorted_points) == 4
            
        except ImportError as e:
            pytest.skip(f"视频工具导入失败: {e}")
    
    def test_image_utils_import(self):
        """测试图像工具导入"""
        try:
            from utils.image_utils import ImageProcessor
            processor = ImageProcessor(self.config_manager)
            assert processor is not None
            
        except ImportError as e:
            pytest.skip(f"图像工具导入失败: {e}")
    
    def test_algorithm_imports(self):
        """测试算法模块导入"""
        # 测试水位算法
        try:
            from algorithms.water_level.transparency import detect_waterline_transparency
            from algorithms.water_level.gradient import detect_waterline_gradient
            from algorithms.water_level.hough import detect_waterline_hough
            from algorithms.water_level.color_threshold import detect_waterline_color_threshold
            
            assert detect_waterline_transparency is not None
            assert detect_waterline_gradient is not None
            assert detect_waterline_hough is not None
            assert detect_waterline_color_threshold is not None
            
        except ImportError as e:
            pytest.skip(f"水位算法导入失败: {e}")
        
        # 测试流速算法
        try:
            from algorithms.flow_speed.otv import analyze_flow_otv
            assert analyze_flow_otv is not None
            
        except ImportError as e:
            pytest.skip(f"OTV算法导入失败: {e}")
        
        try:
            from algorithms.flow_speed.piv import analyze_flow_piv
            assert analyze_flow_piv is not None
            
        except ImportError as e:
            pytest.skip(f"PIV算法导入失败（可能缺少OpenPIV库）: {e}")
        
        # 测试异常检测算法
        try:
            from algorithms.anomaly.yolo_detector import detect_anomalies_yolo
            assert detect_anomalies_yolo is not None
            
        except ImportError as e:
            pytest.skip(f"YOLO检测器导入失败（可能缺少Ultralytics库）: {e}")
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试必要的配置项是否存在
        required_configs = [
            "server.host",
            "server.port",
            "analyzers.water_level.enabled",
            "analyzers.flow_speed.enabled", 
            "analyzers.anomaly.enabled",
            "concurrency.max_concurrent_streams",
            "logging.level"
        ]
        
        for config_key in required_configs:
            value = self.config_manager.get(config_key)
            assert value is not None, f"必要配置项缺失: {config_key}"
    
    def test_logger_functionality(self):
        """测试日志功能"""
        import logging
        
        # 获取测试日志器
        test_logger = logging.getLogger("test_logger")
        
        # 测试不同级别的日志
        test_logger.debug("这是一条调试信息")
        test_logger.info("这是一条信息")
        test_logger.warning("这是一条警告")
        test_logger.error("这是一条错误信息")
        
        # 如果没有异常抛出，说明日志系统工作正常
        assert True


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
