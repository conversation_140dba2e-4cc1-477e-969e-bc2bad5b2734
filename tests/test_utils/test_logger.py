"""
日志系统测试

测试日志系统的各种功能，包括日志级别、文件轮转、格式化等。
"""

import pytest
import logging
import os
import tempfile
import time
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.logger import setup_logger, CustomFormatter, get_logger
from utils.config_manager import ConfigManager


class TestLogger:
    """日志系统测试类"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def test_config_manager(self, temp_log_dir):
        """创建测试用配置管理器"""
        config_manager = ConfigManager()
        
        # 设置测试日志路径
        log_path = os.path.join(temp_log_dir, "test_app.log")
        config_manager.update("logging.file_path", log_path)
        config_manager.update("logging.level", "DEBUG")
        config_manager.update("logging.max_file_size", "1MB")
        
        return config_manager
    
    def test_setup_logger_basic(self, test_config_manager):
        """测试基础日志设置"""
        # 设置日志
        root_logger = setup_logger(debug=True, config_manager=test_config_manager)
        
        assert root_logger is not None
        assert root_logger.level == logging.DEBUG
        
        # 验证处理器数量（控制台 + 文件）
        assert len(root_logger.handlers) >= 2
    
    def test_custom_formatter(self):
        """测试自定义格式器"""
        # 测试彩色格式器
        color_formatter = CustomFormatter(use_color=True)
        assert color_formatter is not None
        
        # 测试无彩色格式器
        no_color_formatter = CustomFormatter(use_color=False)
        assert no_color_formatter is not None
        
        # 创建测试日志记录
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        # 测试格式化
        color_output = color_formatter.format(record)
        no_color_output = no_color_formatter.format(record)
        
        assert "Test message" in color_output
        assert "Test message" in no_color_output
        assert len(color_output) >= len(no_color_output)  # 彩色版本可能更长
    
    def test_file_logging(self, test_config_manager, temp_log_dir):
        """测试文件日志功能"""
        # 设置日志
        setup_logger(debug=True, config_manager=test_config_manager)
        
        # 获取测试日志器
        test_logger = get_logger("test_file_logging")
        
        # 写入测试日志
        test_messages = [
            "Debug message",
            "Info message", 
            "Warning message",
            "Error message"
        ]
        
        test_logger.debug(test_messages[0])
        test_logger.info(test_messages[1])
        test_logger.warning(test_messages[2])
        test_logger.error(test_messages[3])
        
        # 强制刷新日志
        for handler in test_logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
        
        # 检查日志文件是否存在
        log_file_path = test_config_manager.get("logging.file_path")
        
        # 等待文件写入
        time.sleep(0.1)
        
        # 验证日志文件内容
        if os.path.exists(log_file_path):
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_content = f.read()
                
            for message in test_messages:
                assert message in log_content
    
    def test_log_levels(self, test_config_manager):
        """测试不同日志级别"""
        # 测试DEBUG级别
        setup_logger(debug=True, config_manager=test_config_manager)
        debug_logger = get_logger("test_debug")
        
        assert debug_logger.isEnabledFor(logging.DEBUG)
        assert debug_logger.isEnabledFor(logging.INFO)
        assert debug_logger.isEnabledFor(logging.WARNING)
        assert debug_logger.isEnabledFor(logging.ERROR)
        
        # 测试INFO级别
        test_config_manager.update("logging.level", "INFO")
        setup_logger(debug=False, config_manager=test_config_manager)
        info_logger = get_logger("test_info")
        
        assert not info_logger.isEnabledFor(logging.DEBUG)
        assert info_logger.isEnabledFor(logging.INFO)
        assert info_logger.isEnabledFor(logging.WARNING)
        assert info_logger.isEnabledFor(logging.ERROR)
    
    def test_timed_rotating_file_handler(self, test_config_manager, temp_log_dir):
        """测试按时间轮转的文件处理器"""
        # 设置日志
        setup_logger(debug=True, config_manager=test_config_manager)
        
        # 获取根日志器
        root_logger = logging.getLogger()
        
        # 查找TimedRotatingFileHandler
        timed_handler = None
        for handler in root_logger.handlers:
            if isinstance(handler, logging.handlers.TimedRotatingFileHandler):
                timed_handler = handler
                break
        
        assert timed_handler is not None, "应该有TimedRotatingFileHandler"
        
        # 验证轮转配置
        assert timed_handler.when.upper() == 'MIDNIGHT'
        assert timed_handler.interval == 86400  # 1天 = 86400秒
        assert timed_handler.backupCount == 30
    
    def test_get_logger_function(self):
        """测试get_logger函数"""
        # 获取不同名称的日志器
        logger1 = get_logger("test_module_1")
        logger2 = get_logger("test_module_2")
        logger3 = get_logger("test_module_1")  # 相同名称
        
        assert logger1 is not None
        assert logger2 is not None
        assert logger1 is logger3  # 相同名称应该返回相同实例
        assert logger1 is not logger2  # 不同名称应该返回不同实例
        
        assert logger1.name == "test_module_1"
        assert logger2.name == "test_module_2"
    
    def test_third_party_log_suppression(self, test_config_manager):
        """测试第三方库日志抑制"""
        # 设置日志
        setup_logger(debug=True, config_manager=test_config_manager)
        
        # 检查第三方库日志级别
        third_party_loggers = [
            'matplotlib',
            'urllib3', 
            'requests',
            'PIL',
            'cv2',
            'numpy',
            'asyncio'
        ]
        
        for logger_name in third_party_loggers:
            third_party_logger = logging.getLogger(logger_name)
            # 第三方库日志级别应该被设置为WARNING或更高
            assert third_party_logger.level >= logging.WARNING
    
    def test_log_file_naming_with_date(self, test_config_manager, temp_log_dir):
        """测试日志文件按日期命名"""
        # 设置日志
        setup_logger(debug=True, config_manager=test_config_manager)
        
        # 获取根日志器
        root_logger = logging.getLogger()
        
        # 查找TimedRotatingFileHandler
        timed_handler = None
        for handler in root_logger.handlers:
            if isinstance(handler, logging.handlers.TimedRotatingFileHandler):
                timed_handler = handler
                break
        
        if timed_handler:
            # 验证文件名后缀格式
            assert timed_handler.suffix == "_%Y-%m-%d.log"
    
    def test_logger_exception_handling(self, test_config_manager):
        """测试日志系统异常处理"""
        # 测试无效日志路径
        test_config_manager.update("logging.file_path", "/invalid/path/test.log")
        
        # 应该能够正常设置日志（回退到控制台）
        try:
            root_logger = setup_logger(debug=True, config_manager=test_config_manager)
            assert root_logger is not None
        except Exception as e:
            pytest.fail(f"日志设置不应该因为无效路径而失败: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
