"""
配置管理器测试

测试ConfigManager类的各种功能，包括配置加载、获取、更新等。
"""

import pytest
import os
import tempfile
import yaml
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器测试类"""
    
    @pytest.fixture
    def temp_config_file(self):
        """创建临时配置文件"""
        config_data = {
            'server': {
                'host': '127.0.0.1',
                'port': 8080,
                'debug': True
            },
            'analyzers': {
                'water_level': {
                    'enabled': True,
                    'default_method': 'transparency'
                },
                'flow_speed': {
                    'enabled': False,
                    'default_method': 'otv'
                }
            },
            'logging': {
                'level': 'DEBUG',
                'file_path': 'test.log'
            }
        }
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            temp_file_path = f.name
        
        yield temp_file_path
        
        # 清理临时文件
        os.unlink(temp_file_path)
    
    def test_config_manager_singleton(self):
        """测试配置管理器单例模式"""
        config1 = ConfigManager()
        config2 = ConfigManager()
        
        assert config1 is config2, "ConfigManager应该是单例模式"
    
    def test_load_config_from_file(self, temp_config_file):
        """测试从文件加载配置"""
        # 设置环境变量
        os.environ["CONFIG_FILE"] = temp_config_file
        
        config_manager = ConfigManager()
        config_manager.load_config(temp_config_file)
        
        # 验证配置加载
        assert config_manager.get("server.host") == "127.0.0.1"
        assert config_manager.get("server.port") == 8080
        assert config_manager.get("server.debug") is True
    
    def test_get_nested_config(self, temp_config_file):
        """测试获取嵌套配置"""
        os.environ["CONFIG_FILE"] = temp_config_file
        
        config_manager = ConfigManager()
        config_manager.load_config(temp_config_file)
        
        # 测试嵌套键获取
        assert config_manager.get("analyzers.water_level.enabled") is True
        assert config_manager.get("analyzers.water_level.default_method") == "transparency"
        assert config_manager.get("analyzers.flow_speed.enabled") is False
    
    def test_get_config_with_default(self, temp_config_file):
        """测试获取配置时使用默认值"""
        os.environ["CONFIG_FILE"] = temp_config_file
        
        config_manager = ConfigManager()
        config_manager.load_config(temp_config_file)
        
        # 测试不存在的键返回默认值
        assert config_manager.get("nonexistent.key", "default_value") == "default_value"
        assert config_manager.get("server.nonexistent", 9999) == 9999
    
    def test_get_section(self, temp_config_file):
        """测试获取配置部分"""
        os.environ["CONFIG_FILE"] = temp_config_file
        
        config_manager = ConfigManager()
        config_manager.load_config(temp_config_file)
        
        # 测试获取整个部分
        server_config = config_manager.get_section("server")
        assert isinstance(server_config, dict)
        assert server_config["host"] == "127.0.0.1"
        assert server_config["port"] == 8080
        
        analyzers_config = config_manager.get_section("analyzers")
        assert isinstance(analyzers_config, dict)
        assert "water_level" in analyzers_config
        assert "flow_speed" in analyzers_config
    
    def test_update_config(self, temp_config_file):
        """测试更新配置"""
        os.environ["CONFIG_FILE"] = temp_config_file
        
        config_manager = ConfigManager()
        config_manager.load_config(temp_config_file)
        
        # 测试更新现有配置
        assert config_manager.update("server.port", 9000) is True
        assert config_manager.get("server.port") == 9000
        
        # 测试更新嵌套配置
        assert config_manager.update("analyzers.water_level.enabled", False) is True
        assert config_manager.get("analyzers.water_level.enabled") is False
        
        # 测试创建新配置
        assert config_manager.update("new.config.key", "new_value") is True
        assert config_manager.get("new.config.key") == "new_value"
    
    def test_save_config(self, temp_config_file):
        """测试保存配置"""
        os.environ["CONFIG_FILE"] = temp_config_file
        
        config_manager = ConfigManager()
        config_manager.load_config(temp_config_file)
        
        # 修改配置
        config_manager.update("server.port", 9001)
        config_manager.update("test.new_key", "test_value")
        
        # 保存配置
        assert config_manager.save_config() is True
        
        # 重新加载验证
        config_manager.reload_config()
        assert config_manager.get("server.port") == 9001
        assert config_manager.get("test.new_key") == "test_value"
    
    def test_reload_config(self, temp_config_file):
        """测试重新加载配置"""
        os.environ["CONFIG_FILE"] = temp_config_file
        
        config_manager = ConfigManager()
        config_manager.load_config(temp_config_file)
        
        original_port = config_manager.get("server.port")
        
        # 修改内存中的配置
        config_manager.update("server.port", 9002)
        assert config_manager.get("server.port") == 9002
        
        # 重新加载配置文件
        assert config_manager.reload_config() is True
        assert config_manager.get("server.port") == original_port
    
    def test_default_config_creation(self):
        """测试默认配置创建"""
        # 使用不存在的配置文件路径
        with tempfile.TemporaryDirectory() as temp_dir:
            nonexistent_config = os.path.join(temp_dir, "nonexistent.yaml")
            
            config_manager = ConfigManager()
            config_manager.load_config(nonexistent_config)
            
            # 验证默认配置被创建
            assert os.path.exists(nonexistent_config)
            
            # 验证默认配置内容
            assert config_manager.get("server.host") is not None
            assert config_manager.get("server.port") is not None
            assert config_manager.get("analyzers.water_level.enabled") is not None
    
    def test_invalid_config_file(self):
        """测试无效配置文件处理"""
        # 创建无效的YAML文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            invalid_config_path = f.name
        
        try:
            config_manager = ConfigManager()
            config_manager.load_config(invalid_config_path)
            
            # 应该回退到默认配置
            assert config_manager.get("server.host") is not None
            
        finally:
            os.unlink(invalid_config_path)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
