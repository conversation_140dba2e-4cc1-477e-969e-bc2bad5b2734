"""
水深识别完整功能测试

使用单张图片测试水深识别算法的完整功能，验证算法迁移的正确性。
包括所有水位检测方法的测试和结果验证。
"""

import pytest
import numpy as np
import cv2
import os
from pathlib import Path
import sys
import logging

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.config_manager import ConfigManager
from utils.logger import setup_logger
from algorithms.water_level.transparency import detect_waterline_transparency
from algorithms.water_level.gradient import detect_waterline_gradient
from algorithms.water_level.hough import detect_waterline_hough
from algorithms.water_level.color_threshold import detect_waterline_color_threshold


class TestWaterDepthDetection:
    """水深识别完整功能测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """测试设置"""
        # 设置配置文件
        os.environ["CONFIG_FILE"] = "config/app_config.yaml"
        
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 初始化日志系统
        setup_logger(debug=True, config_manager=self.config_manager)
        self.logger = logging.getLogger("test_water_depth")
        
        # 创建测试图片
        self.test_image = self._create_realistic_water_image()
        
        # 设置测试参数
        self.pixel_to_cm_ratio = 2.5  # 假设每像素代表2.5厘米
        
        self.logger.info("水深识别测试环境初始化完成")
    
    def _create_realistic_water_image(self):
        """创建更真实的水面测试图像"""
        # 创建640x480的图像
        height, width = 480, 640
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 天空部分 (0-200行) - 浅蓝色渐变
        for y in range(200):
            intensity = 255 - int(y * 0.3)
            image[y, :] = [intensity, intensity - 20, 200]
        
        # 岸边/建筑物部分 (200-280行) - 灰色
        image[200:280, :] = [120, 130, 140]
        
        # 水面线区域 (280-290行) - 过渡区域
        for y in range(280, 290):
            transition = (y - 280) / 10.0
            sky_color = np.array([200, 180, 200])
            water_color = np.array([80, 120, 60])
            color = sky_color * (1 - transition) + water_color * transition
            image[y, :] = color.astype(np.uint8)
        
        # 水面部分 (290-480行) - 深绿蓝色，带波纹
        base_water_color = [80, 120, 60]
        for y in range(290, height):
            for x in range(width):
                # 添加波纹效果
                wave = int(10 * np.sin(x * 0.02) * np.sin(y * 0.01))
                noise = np.random.randint(-15, 15, 3)
                color = np.array(base_water_color) + wave + noise
                color = np.clip(color, 0, 255)
                image[y, x] = color
        
        # 添加一些反光效果
        for i in range(20):
            x = np.random.randint(0, width)
            y = np.random.randint(300, height)
            cv2.circle(image, (x, y), np.random.randint(2, 8), (150, 180, 120), -1)
        
        self.logger.debug(f"创建测试图像，尺寸: {image.shape}")
        return image
    
    def _save_test_image(self, image, filename):
        """保存测试图像到临时目录"""
        temp_dir = Path("data/temp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        filepath = temp_dir / filename
        cv2.imwrite(str(filepath), image)
        self.logger.debug(f"保存测试图像: {filepath}")
        return str(filepath)
    
    def test_transparency_detection_complete(self):
        """测试透明度检测的完整流程"""
        self.logger.info("开始透明度检测完整测试")
        
        # 保存测试图像
        image_path = self._save_test_image(self.test_image, "transparency_test.jpg")
        
        # 执行透明度检测
        waterline_y = detect_waterline_transparency(
            self.test_image,
            config_manager=self.config_manager,
            debug=True
        )
        
        # 验证结果
        assert waterline_y is not None, "透明度检测应该返回水位线位置"
        assert isinstance(waterline_y, (int, float)), "水位线位置应该是数值"
        assert 0 <= waterline_y < self.test_image.shape[0], "水位线位置应该在图像范围内"
        
        # 计算水深
        water_depth_pixels = self.test_image.shape[0] - waterline_y
        water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
        
        self.logger.info(f"透明度检测结果: 水位线位置={waterline_y}, 水深={water_depth_cm:.2f}cm")
        
        # 创建结果可视化图像
        result_image = self._create_result_visualization(
            self.test_image, waterline_y, "透明度检测", water_depth_cm
        )
        self._save_test_image(result_image, "transparency_result.jpg")
        
        return {
            "method": "transparency",
            "waterline_y": waterline_y,
            "water_depth_cm": water_depth_cm,
            "success": True
        }
    
    def test_gradient_detection_complete(self):
        """测试梯度检测的完整流程"""
        self.logger.info("开始梯度检测完整测试")
        
        # 转换为灰度图像
        gray_image = cv2.cvtColor(self.test_image, cv2.COLOR_BGR2GRAY)
        
        # 执行梯度检测
        waterline_y = detect_waterline_gradient(
            gray_image,
            config_manager=self.config_manager,
            debug=True
        )
        
        # 验证结果
        if waterline_y is not None:
            assert isinstance(waterline_y, (int, float)), "水位线位置应该是数值"
            assert 0 <= waterline_y < gray_image.shape[0], "水位线位置应该在图像范围内"
            
            # 计算水深
            water_depth_pixels = gray_image.shape[0] - waterline_y
            water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
            
            self.logger.info(f"梯度检测结果: 水位线位置={waterline_y}, 水深={water_depth_cm:.2f}cm")
            
            # 创建结果可视化图像
            result_image = self._create_result_visualization(
                self.test_image, waterline_y, "梯度检测", water_depth_cm
            )
            self._save_test_image(result_image, "gradient_result.jpg")
            
            return {
                "method": "gradient",
                "waterline_y": waterline_y,
                "water_depth_cm": water_depth_cm,
                "success": True
            }
        else:
            self.logger.warning("梯度检测未找到水位线")
            return {
                "method": "gradient",
                "waterline_y": None,
                "water_depth_cm": 0.0,
                "success": False
            }
    
    def test_hough_detection_complete(self):
        """测试霍夫变换检测的完整流程"""
        self.logger.info("开始霍夫变换检测完整测试")
        
        # 转换为灰度图像
        gray_image = cv2.cvtColor(self.test_image, cv2.COLOR_BGR2GRAY)
        
        # 执行霍夫变换检测
        waterline_y = detect_waterline_hough(
            gray_image,
            config_manager=self.config_manager,
            debug=True
        )
        
        # 验证结果
        if waterline_y is not None:
            assert isinstance(waterline_y, (int, float)), "水位线位置应该是数值"
            assert 0 <= waterline_y < gray_image.shape[0], "水位线位置应该在图像范围内"
            
            # 计算水深
            water_depth_pixels = gray_image.shape[0] - waterline_y
            water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
            
            self.logger.info(f"霍夫变换检测结果: 水位线位置={waterline_y}, 水深={water_depth_cm:.2f}cm")
            
            # 创建结果可视化图像
            result_image = self._create_result_visualization(
                self.test_image, waterline_y, "霍夫变换检测", water_depth_cm
            )
            self._save_test_image(result_image, "hough_result.jpg")
            
            return {
                "method": "hough",
                "waterline_y": waterline_y,
                "water_depth_cm": water_depth_cm,
                "success": True
            }
        else:
            self.logger.warning("霍夫变换检测未找到水位线")
            return {
                "method": "hough",
                "waterline_y": None,
                "water_depth_cm": 0.0,
                "success": False
            }
    
    def test_color_threshold_detection_complete(self):
        """测试颜色阈值检测的完整流程"""
        self.logger.info("开始颜色阈值检测完整测试")
        
        # 执行颜色阈值检测
        waterline_y = detect_waterline_color_threshold(
            self.test_image,
            config_manager=self.config_manager,
            debug=True
        )
        
        # 验证结果
        if waterline_y is not None:
            assert isinstance(waterline_y, (int, float)), "水位线位置应该是数值"
            assert 0 <= waterline_y < self.test_image.shape[0], "水位线位置应该在图像范围内"
            
            # 计算水深
            water_depth_pixels = self.test_image.shape[0] - waterline_y
            water_depth_cm = water_depth_pixels / self.pixel_to_cm_ratio
            
            self.logger.info(f"颜色阈值检测结果: 水位线位置={waterline_y}, 水深={water_depth_cm:.2f}cm")
            
            # 创建结果可视化图像
            result_image = self._create_result_visualization(
                self.test_image, waterline_y, "颜色阈值检测", water_depth_cm
            )
            self._save_test_image(result_image, "color_threshold_result.jpg")
            
            return {
                "method": "color_threshold",
                "waterline_y": waterline_y,
                "water_depth_cm": water_depth_cm,
                "success": True
            }
        else:
            self.logger.warning("颜色阈值检测未找到水位线")
            return {
                "method": "color_threshold",
                "waterline_y": None,
                "water_depth_cm": 0.0,
                "success": False
            }
    
    def _create_result_visualization(self, image, waterline_y, method_name, water_depth_cm):
        """创建检测结果可视化图像"""
        result_image = image.copy()
        
        if waterline_y is not None:
            # 绘制水位线
            cv2.line(result_image, (0, int(waterline_y)), (image.shape[1], int(waterline_y)), 
                    (0, 255, 0), 3)
            
            # 添加文字标注
            text = f"{method_name}: {water_depth_cm:.1f}cm"
            cv2.putText(result_image, text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # 添加水深区域标注
            water_region_text = f"Water Depth: {water_depth_cm:.1f}cm"
            cv2.putText(result_image, water_region_text, 
                       (10, int(waterline_y) + 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        return result_image
    
    def test_all_methods_comparison(self):
        """测试所有方法并比较结果"""
        self.logger.info("开始所有水深检测方法的对比测试")
        
        # 执行所有检测方法
        results = []
        
        # 透明度检测
        try:
            result = self.test_transparency_detection_complete()
            results.append(result)
        except Exception as e:
            self.logger.error(f"透明度检测失败: {e}")
            results.append({"method": "transparency", "success": False, "error": str(e)})
        
        # 梯度检测
        try:
            result = self.test_gradient_detection_complete()
            results.append(result)
        except Exception as e:
            self.logger.error(f"梯度检测失败: {e}")
            results.append({"method": "gradient", "success": False, "error": str(e)})
        
        # 霍夫变换检测
        try:
            result = self.test_hough_detection_complete()
            results.append(result)
        except Exception as e:
            self.logger.error(f"霍夫变换检测失败: {e}")
            results.append({"method": "hough", "success": False, "error": str(e)})
        
        # 颜色阈值检测
        try:
            result = self.test_color_threshold_detection_complete()
            results.append(result)
        except Exception as e:
            self.logger.error(f"颜色阈值检测失败: {e}")
            results.append({"method": "color_threshold", "success": False, "error": str(e)})
        
        # 分析结果
        successful_results = [r for r in results if r.get("success", False)]
        
        self.logger.info("=" * 60)
        self.logger.info("水深检测方法对比结果:")
        self.logger.info("=" * 60)
        
        for result in results:
            if result.get("success", False):
                self.logger.info(f"{result['method']:15} | 水位线: {result['waterline_y']:6.1f} | 水深: {result['water_depth_cm']:6.1f}cm")
            else:
                error_msg = result.get("error", "未知错误")
                self.logger.info(f"{result['method']:15} | 检测失败: {error_msg}")
        
        if successful_results:
            # 计算平均水深
            avg_depth = np.mean([r["water_depth_cm"] for r in successful_results])
            std_depth = np.std([r["water_depth_cm"] for r in successful_results])
            
            self.logger.info("=" * 60)
            self.logger.info(f"成功检测方法数: {len(successful_results)}/{len(results)}")
            self.logger.info(f"平均水深: {avg_depth:.2f}cm")
            self.logger.info(f"标准差: {std_depth:.2f}cm")
            self.logger.info("=" * 60)
        
        # 验证至少有一种方法成功
        assert len(successful_results) > 0, "至少应该有一种检测方法成功"
        
        return results


if __name__ == "__main__":
    # 直接运行测试
    test_instance = TestWaterDepthDetection()
    test_instance.setup()
    
    print("开始水深识别完整功能测试...")
    results = test_instance.test_all_methods_comparison()
    
    print("\n测试完成！检查 data/temp/ 目录查看结果图像。")
