"""
水位识别算法测试

测试各种水位识别算法的功能，包括透明度、梯度、霍夫变换、颜色阈值等。
"""

import pytest
import numpy as np
import cv2
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from utils.config_manager import ConfigManager
from algorithms.water_level.transparency import detect_waterline_transparency, TransparencyDetector
from algorithms.water_level.gradient import detect_waterline_gradient, GradientDetector
from algorithms.water_level.hough import detect_waterline_hough, HoughDetector
from algorithms.water_level.color_threshold import detect_waterline_color_threshold, ColorThresholdDetector


class TestWaterLevelAlgorithms:
    """水位识别算法测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """创建测试用配置管理器"""
        return ConfigManager()
    
    @pytest.fixture
    def test_image_bgr(self):
        """创建测试用BGR图像"""
        # 创建一个模拟水面的图像
        image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # 上半部分：天空/背景（浅蓝色）
        image[:100, :] = [200, 150, 100]
        
        # 下半部分：水面（深蓝色）
        image[100:, :] = [100, 50, 30]
        
        # 在水面线附近添加一些噪声
        noise = np.random.randint(-20, 20, (10, 300, 3))
        image[95:105, :] = np.clip(image[95:105, :] + noise, 0, 255)
        
        return image.astype(np.uint8)
    
    @pytest.fixture
    def test_image_gray(self, test_image_bgr):
        """创建测试用灰度图像"""
        return cv2.cvtColor(test_image_bgr, cv2.COLOR_BGR2GRAY)
    
    def test_transparency_detector_initialization(self, config_manager):
        """测试透明度检测器初始化"""
        detector = TransparencyDetector(config_manager)
        
        assert detector is not None
        assert detector.config_manager is config_manager
        assert detector.alpha_thresh > 0
        assert detector.scheme in ["contrast_change", "transparency_mask"]
    
    def test_transparency_detection_function(self, test_image_bgr, config_manager):
        """测试透明度检测函数"""
        result = detect_waterline_transparency(
            test_image_bgr, 
            config_manager=config_manager, 
            debug=False
        )
        
        # 结果应该是None或者一个合理的y坐标
        if result is not None:
            assert isinstance(result, (int, float))
            assert 0 <= result < test_image_bgr.shape[0]
    
    def test_transparency_detection_methods(self, test_image_bgr, config_manager):
        """测试透明度检测的不同方案"""
        detector = TransparencyDetector(config_manager)
        
        # 测试对比度变化方案
        detector.scheme = "contrast_change"
        result1 = detector.detect_waterline(test_image_bgr, debug=False)
        
        # 测试透明度掩码方案
        detector.scheme = "transparency_mask"
        result2 = detector.detect_waterline(test_image_bgr, debug=False)
        
        # 至少有一种方案应该能检测到结果
        assert result1 is not None or result2 is not None
    
    def test_gradient_detector_initialization(self, config_manager):
        """测试梯度检测器初始化"""
        detector = GradientDetector(config_manager)
        
        assert detector is not None
        assert detector.config_manager is config_manager
        assert detector.grad_thresh > 0
    
    def test_gradient_detection_function(self, test_image_gray, config_manager):
        """测试梯度检测函数"""
        result = detect_waterline_gradient(
            test_image_gray,
            config_manager=config_manager,
            debug=False
        )
        
        # 结果应该是None或者一个合理的y坐标
        if result is not None:
            assert isinstance(result, (int, float))
            assert 0 <= result < test_image_gray.shape[0]
    
    def test_gradient_column_analysis(self, test_image_gray, config_manager):
        """测试梯度检测的列分析功能"""
        detector = GradientDetector(config_manager)
        
        # 测试单列分析
        col_idx = test_image_gray.shape[1] // 2
        result = detector._analyze_column_gradient(test_image_gray, col_idx, debug=False)
        
        if result is not None:
            assert isinstance(result, int)
            assert 0 <= result < test_image_gray.shape[0]
    
    def test_hough_detector_initialization(self, config_manager):
        """测试霍夫变换检测器初始化"""
        detector = HoughDetector(config_manager)
        
        assert detector is not None
        assert detector.config_manager is config_manager
        assert detector.hough_thresh > 0
        assert detector.min_line_len_factor > 0
    
    def test_hough_detection_function(self, test_image_gray, config_manager):
        """测试霍夫变换检测函数"""
        result = detect_waterline_hough(
            test_image_gray,
            config_manager=config_manager,
            debug=False
        )
        
        # 结果应该是None或者一个合理的y坐标
        if result is not None:
            assert isinstance(result, (int, float))
            assert 0 <= result < test_image_gray.shape[0]
    
    def test_hough_line_detection(self, test_image_gray, config_manager):
        """测试霍夫变换直线检测"""
        detector = HoughDetector(config_manager)
        
        # 测试所有直线检测
        lines = detector.detect_all_lines(test_image_gray, debug=False)
        assert isinstance(lines, list)
        
        # 测试水平线过滤
        if lines:
            horizontal_lines = detector.filter_horizontal_lines(lines)
            assert isinstance(horizontal_lines, list)
            
            # 测试直线统计
            stats = detector.get_line_statistics(lines)
            assert isinstance(stats, dict)
            assert "total_lines" in stats
            assert "horizontal_lines" in stats
    
    def test_color_threshold_detector_initialization(self, config_manager):
        """测试颜色阈值检测器初始化"""
        detector = ColorThresholdDetector(config_manager)
        
        assert detector is not None
        assert detector.config_manager is config_manager
        assert len(detector.hsv_lower) == 3
        assert len(detector.hsv_upper) == 3
    
    def test_color_threshold_detection_function(self, test_image_bgr, config_manager):
        """测试颜色阈值检测函数"""
        result = detect_waterline_color_threshold(
            test_image_bgr,
            config_manager=config_manager,
            debug=False
        )
        
        # 结果应该是None或者一个合理的y坐标
        if result is not None:
            assert isinstance(result, (int, float))
            assert 0 <= result < test_image_bgr.shape[0]
    
    def test_color_threshold_mask_generation(self, test_image_bgr, config_manager):
        """测试颜色阈值掩码生成"""
        detector = ColorThresholdDetector(config_manager)
        
        # 测试水体掩码生成
        mask = detector.get_water_mask(test_image_bgr)
        assert mask.shape == test_image_bgr.shape[:2]
        assert mask.dtype == np.uint8
        
        # 测试水体轮廓获取
        contours = detector.get_water_contours(test_image_bgr)
        assert isinstance(contours, list)
    
    def test_color_threshold_visualization(self, test_image_bgr, config_manager):
        """测试颜色阈值检测可视化"""
        detector = ColorThresholdDetector(config_manager)
        
        # 测试检测步骤可视化
        viz_image = detector.visualize_detection_steps(test_image_bgr)
        assert viz_image.shape[0] == test_image_bgr.shape[0] * 2
        assert viz_image.shape[1] == test_image_bgr.shape[1] * 2
        
        # 测试检测结果图像
        waterline_y = detector.detect_waterline(test_image_bgr, debug=False)
        result_image = detector.create_detection_result_image(test_image_bgr, waterline_y)
        assert result_image.shape == test_image_bgr.shape
    
    def test_algorithm_robustness_with_noise(self, config_manager):
        """测试算法对噪声的鲁棒性"""
        # 创建带噪声的测试图像
        noisy_image = np.random.randint(0, 255, (100, 150, 3), dtype=np.uint8)
        noisy_gray = cv2.cvtColor(noisy_image, cv2.COLOR_BGR2GRAY)
        
        # 测试各算法是否能处理噪声图像而不崩溃
        try:
            detect_waterline_transparency(noisy_image, config_manager, debug=False)
            detect_waterline_gradient(noisy_gray, config_manager, debug=False)
            detect_waterline_hough(noisy_gray, config_manager, debug=False)
            detect_waterline_color_threshold(noisy_image, config_manager, debug=False)
        except Exception as e:
            pytest.fail(f"算法处理噪声图像时不应该崩溃: {e}")
    
    def test_algorithm_with_empty_image(self, config_manager):
        """测试算法处理空图像"""
        # 创建空图像
        empty_bgr = np.zeros((50, 50, 3), dtype=np.uint8)
        empty_gray = np.zeros((50, 50), dtype=np.uint8)
        
        # 测试各算法处理空图像
        result1 = detect_waterline_transparency(empty_bgr, config_manager, debug=False)
        result2 = detect_waterline_gradient(empty_gray, config_manager, debug=False)
        result3 = detect_waterline_hough(empty_gray, config_manager, debug=False)
        result4 = detect_waterline_color_threshold(empty_bgr, config_manager, debug=False)
        
        # 空图像应该返回None或者合理的默认值
        for result in [result1, result2, result3, result4]:
            if result is not None:
                assert isinstance(result, (int, float))
    
    def test_algorithm_parameter_validation(self, test_image_bgr, config_manager):
        """测试算法参数验证"""
        # 测试透明度检测器参数
        detector = TransparencyDetector(config_manager)
        
        # 修改参数并测试
        original_thresh = detector.alpha_thresh
        detector.alpha_thresh = 0  # 无效值
        
        # 算法应该能处理无效参数
        try:
            result = detector.detect_waterline(test_image_bgr, debug=False)
            # 恢复原始参数
            detector.alpha_thresh = original_thresh
        except Exception as e:
            pytest.fail(f"算法应该能处理无效参数: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
